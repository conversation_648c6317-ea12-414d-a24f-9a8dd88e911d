/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : SysStartUpTaskDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef SYS_STARTUP_TASK_DRV_C
#define SYS_STARTUP_TASK_DRV_C
#endif

/* include files */
#include "SysStartUpTaskDrv.h"

#define HW_PREFIX                           "XCM_"
#define ALG_PREFIX                          "IMU_"
#define TP_PREFIX                           "TP_0509"
#define FW_PREFIX                           "FW_"
/* static variable definition */
#define TIMER_10MS_INTERVAL         APP_TIMER_TICKS(10)                   /**<  interval (ticks). */
#define TIMER_1S_INTERVAL           APP_TIMER_TICKS(1000)                   /**<  interval (ticks). */
APP_TIMER_DEF(g_10ms_timer_id);                                  /**< 10 ms timer. */
APP_TIMER_DEF(g_1s_timer_id);                                  /**< 1000 ms timer. */
/* static function declaration */

/* static function definition */
void TaskStartScheduler( void )
{
    app_sched_execute();
    if (NRF_LOG_PROCESS() == false)
    {
        //nrf_pwr_mgmt_run();
    }
    FeedWdt(0,0);
}

void start_Timer_1s(void)
{
    ret_code_t err_code;
    err_code = app_timer_start(g_1s_timer_id, TIMER_1S_INTERVAL, NULL);
    APP_ERROR_CHECK(err_code);
}

void stop_Timer_1s(void)
{
    ret_code_t err_code;
    err_code = app_timer_stop(g_1s_timer_id);
    APP_ERROR_CHECK(err_code);
}

void start_Timer_10ms(void)
{
    ret_code_t err_code;
    err_code = app_timer_start(g_10ms_timer_id, TIMER_10MS_INTERVAL, NULL);
    APP_ERROR_CHECK(err_code);
}

void stop_Timer_10ms(void)
{
    ret_code_t err_code;
    err_code = app_timer_stop(g_10ms_timer_id);
    APP_ERROR_CHECK(err_code);
}

char bit4_to_char(uint8_t input)
{
    if (input < 0x0A)
    {
        return '0' + input;
    }
    else if (input < 0x10)
    {
        return 'A' + input - 0x0A;
    }
    else
    {
        return ' ';
    }
}

void bleInfoInit(void)
{
    uint8_t hw_rev[SYS_DEVICE_INFO_MAX_LEN];
    uint8_t fw_rev[SYS_DEVICE_INFO_MAX_LEN];
    uint8_t sw_rev[SYS_DEVICE_INFO_MAX_LEN];
    
    //device name
    if(gSystemCfg_t.mDataCfgSet.isSuffixEnabled)
    {
        gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen + 0] = '-';
        gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen + 1] = bit4_to_char((gSystemCfg_t.mProducInfo.ProdBtMac[1] >> 4) & 0x0F);
        gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen + 2] = bit4_to_char((gSystemCfg_t.mProducInfo.ProdBtMac[1] >> 0) & 0x0F);
        gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen + 3] = bit4_to_char((gSystemCfg_t.mProducInfo.ProdBtMac[0] >> 4) & 0x0F);
        gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen + 4] = bit4_to_char((gSystemCfg_t.mProducInfo.ProdBtMac[0] >> 0) & 0x0F);
        gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen + 5] = '\0';
    }
    else
    {
        //do nothing
    }
    //dfu name
    memcpy(&(gSystemCfg_t.mDataCfgSet.mDfuName),&(gSystemCfg_t.mDataCfgSet.mDeviceName),sizeof(gSystemCfg_t.mDataCfgSet.mDfuName));
    gSystemCfg_t.mDataCfgSet.mDfuName.mdata[gSystemCfg_t.mDataCfgSet.mDfuName.mLen + 0] = '-';
    gSystemCfg_t.mDataCfgSet.mDfuName.mdata[gSystemCfg_t.mDataCfgSet.mDfuName.mLen + 1] = bit4_to_char((gSystemCfg_t.mProducInfo.ProdBtMac[1] >> 4) & 0x0F);
    gSystemCfg_t.mDataCfgSet.mDfuName.mdata[gSystemCfg_t.mDataCfgSet.mDfuName.mLen + 2] = bit4_to_char((gSystemCfg_t.mProducInfo.ProdBtMac[1] >> 0) & 0x0F);
    gSystemCfg_t.mDataCfgSet.mDfuName.mdata[gSystemCfg_t.mDataCfgSet.mDfuName.mLen + 3] = bit4_to_char(((gSystemCfg_t.mProducInfo.ProdBtMac[0] + 1u) >> 4) & 0x0F);
    gSystemCfg_t.mDataCfgSet.mDfuName.mdata[gSystemCfg_t.mDataCfgSet.mDfuName.mLen + 4] = bit4_to_char(((gSystemCfg_t.mProducInfo.ProdBtMac[0] + 1u) >> 0) & 0x0F);
    gSystemCfg_t.mDataCfgSet.mDfuName.mdata[gSystemCfg_t.mDataCfgSet.mDfuName.mLen + 5] = '\0';
    gSystemCfg_t.mDataCfgSet.mDfuName.mLen += 6u;
    
    //hardware version
    memset(( char*)hw_rev, 0, SYS_DEVICE_INFO_MAX_LEN);
    strcpy(( char*)hw_rev, HW_PREFIX);
    memcpy(&hw_rev[strlen(HW_PREFIX)],gSystemCfg_t.mProducInfo.ProdHardVer,4u);
    strcat(( char*)hw_rev, (const char*)"-");
    strcat(( char*)hw_rev, ALG_PREFIX );
    memcpy(&hw_rev[strlen((const char*)hw_rev)],gSystemCfg_t.mImuAlgVer,4u);
    
    //fw version (TP version)
    memset(fw_rev, 0, SYS_DEVICE_INFO_MAX_LEN);
    strcpy(( char*)fw_rev, TP_PREFIX);
    fw_rev[strlen(TP_PREFIX) + 0] = bit4_to_char((gSystemCfg_t.mTpVer >> 4) & 0x0F);
    fw_rev[strlen(TP_PREFIX) + 1] = bit4_to_char((gSystemCfg_t.mTpVer >> 0) & 0x0F);
    fw_rev[strlen(TP_PREFIX) + 2] = '\0';
    
     //sw version (firmware version)
    memset(sw_rev, 0, SYS_DEVICE_INFO_MAX_LEN);
    strcpy(( char*)sw_rev, FW_PREFIX);
    memcpy(&sw_rev[strlen((const char*)sw_rev)],SoftInfo_t.ProdSoftVer,4u);
    
    ble_srv_utf8_str_t  Manufact ={strlen((const char*)gSystemCfg_t.mProducInfo.ProdManuFacture),gSystemCfg_t.mProducInfo.ProdManuFacture};
    ble_srv_utf8_str_t  model ={strlen((const char*)gSystemCfg_t.mDataCfgSet.mModelName.mdata),gSystemCfg_t.mDataCfgSet.mModelName.mdata};
    ble_srv_utf8_str_t  serial ={strlen((const char*)gSystemCfg_t.mProducInfo.ProdPCBASN),gSystemCfg_t.mProducInfo.ProdPCBASN};
    ble_srv_utf8_str_t  hw ={strlen((const char*)hw_rev),hw_rev};
    ble_srv_utf8_str_t  fw ={strlen((const char*)fw_rev),fw_rev};
    ble_srv_utf8_str_t  sw ={strlen((const char*)sw_rev),sw_rev};
    
    // Initialize modules.
    gap_params_init(gSystemCfg_t.mDataCfgSet.mDeviceName.mdata,strlen((const char*)(gSystemCfg_t.mDataCfgSet.mDeviceName.mdata)),gSystemCfg_t.mProducInfo.ProdBtMac);
    gatt_init();
    //advertising_init(0xF000 | gSystemSt_t.mSetMarkID,gSystemCfg_t.mDataCfgSet.mDeviceTYpe, BleAdvEvtCallBack);
    QueuedModule_init();
    Ble_dis_service_init(Manufact,model,serial,hw,fw,sw);
    bas_service_init();
    Ble_xim_service_init(BtXimServiceEvtCallBack,gSystemCfg_t.mDataCfgSet.BleServiceEn_t.bits.mOldCalib_En,gSystemCfg_t.mDataCfgSet.BleServiceEn_t.bits.mSysCfg_En);
    conn_params_init(BleParamsUpdataEvtCallBack);  
}

/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Task_StartUp
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void Task_StartUp(void *pdata)
{         
    ret_code_t err_code;
    ble_gap_addr_t SlaveMac_t;
    // Configure and initialize the BLE stack.
    ble_stack_init(BleConnectEvtCallBack);
    RxMsgProcessTaskInit();
    EvtProcessTaskInit();
    FastPwmTimInit();
    LedCtrlModuleInit();
    DSwSampInit();
    imu_set_calibration_param(gSystemCfg_t.mImuCalibSet);
    APP_SCHED_INIT(sizeof(sync_snapshot_t), (TASK_MSG_Q_SIZE_CMD_PROCESS + TASK_MSG_Q_SIZE_EVT));
    // Create timers.
    err_code = app_timer_create(&g_10ms_timer_id,
                                APP_TIMER_MODE_REPEATED,
                                Timer_10ms_timeout_handler);
    err_code = app_timer_create(&g_1s_timer_id,
                                APP_TIMER_MODE_REPEATED,
                                Timer_1s_timeout_handler);
    err_code = app_timer_start(g_10ms_timer_id, TIMER_10MS_INTERVAL, NULL);
    APP_ERROR_CHECK(err_code);
    err_code = app_timer_start(g_1s_timer_id, TIMER_1S_INTERVAL, NULL);
    APP_ERROR_CHECK(err_code);
    gSystemSt_t.BlePipeNotifyEn_t.Timer10msEn = SYS_CTRL_EN;
    err_code = sd_ble_gap_addr_get(&SlaveMac_t);
    DEBUG_PRINTF("get host Mac MSB %02x,%02x,%02x,%02x,%02x,%02x\r\n",SlaveMac_t.addr[5],SlaveMac_t.addr[4],SlaveMac_t.addr[3],SlaveMac_t.addr[2],SlaveMac_t.addr[1],SlaveMac_t.addr[0]); 
    memcpy(gSystemSt_t.mLastBleHostMac,SlaveMac_t.addr,6u);
    // Uart_Init(UartRxDataCallBack);
// #ifdef MIRAGE2B_BOARD
    Uart_Init(UartRxDataCallBack);
// #else
//     USB_DEVICE_Init(UsbRecvCmdISRCallBack);
//     usbPowerDetectionInit();
// #endif
    bleInfoInit();
    FeedWdt(0,0);
    DEBUG_PRINTF("start Task Run\r\n"); 
#ifdef DEBUG_TEST_MSG_EN
    NRF_LOG_PROCESS();
#endif
}
/***********************************************END**********************************************/
