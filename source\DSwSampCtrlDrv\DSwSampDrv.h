/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : DSwSampDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef D_SW_SAMP_DRV_H
#define D_SW_SAMP_DRV_H

/*Include files*/
#include "includes.h"
#include "DSwSampCfgExt.h"
#include "DSwSampHWExt.h"
#include "DSwSampAppExt.h"
#include "nrf_gpio.h"



/*declaration range definition*/
#ifdef D_SW_SAMP_DRV_C 
#define D_SW_SAMP_DRV_EXT 
#else 
#define D_SW_SAMP_DRV_EXT extern 
#endif    

/****following definitions CAN NOT be used outside this driver******/
/*  structure definiton  */
typedef struct
{
  #if(D_SW_SAMP_BIT_FIELD_MODE == FUN_EN)
    uint8_t  m_DSwInSt         :2;
    uint8_t  m_DSwInLastSt     :2;
    uint8_t  m_DSwInStRd       :2;
    uint8_t  m_Reserved        :2;
  #else
    uint8_t  m_DSwInSt;
    uint8_t  m_DSwInLastSt;
    uint8_t  m_DSwInStRd;
  #endif
    uint32_t m_ASwInValueSum;
    uint8_t  m_DSwInStCnt;
}DSwSampBitStr,*pDSwSampBitStr; 

typedef struct
{
    uint32_t         m_PortAddr;    /* Pointer to port data register */
    uint32_t         m_PinOffset;   /* Offset of IO pin              */
}t_DSwSampHWStr;


/* const variable definiton for pin that connected to switch */
static const t_DSwSampHWStr icDSwPortPin[D_SW_SAMP_OBJ_NUM]=
{ 
  #if (D_SW_SAMP_OBJ_NUM >= 1)
    {D_SW_SAMP_SW_PORT_0, D_SW_SAMP_SW_PIN_0}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 2)
    ,{D_SW_SAMP_SW_PORT_1, D_SW_SAMP_SW_PIN_1}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 3)
    ,{D_SW_SAMP_SW_PORT_2, D_SW_SAMP_SW_PIN_2}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 4)
    ,{D_SW_SAMP_SW_PORT_3, D_SW_SAMP_SW_PIN_3}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 5)
    ,{D_SW_SAMP_SW_PORT_4, D_SW_SAMP_SW_PIN_4}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 6)
    ,{D_SW_SAMP_SW_PORT_5, D_SW_SAMP_SW_PIN_5}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 7)
    ,{D_SW_SAMP_SW_PORT_6, D_SW_SAMP_SW_PIN_6}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 8)
    ,{D_SW_SAMP_SW_PORT_7, D_SW_SAMP_SW_PIN_7}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 9)
    ,{D_SW_SAMP_SW_PORT_8, D_SW_SAMP_SW_PIN_8}
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 10)
    ,{D_SW_SAMP_SW_PORT_9, D_SW_SAMP_SW_PIN_9}
  #endif 
};

/* const variable definiton for switch sample mode  */
static const uint8_t icDSwSampMode[D_SW_SAMP_OBJ_NUM]=
{
  #if (D_SW_SAMP_OBJ_NUM >= 1)
    D_SW_SAMP_SAMP_MODE_0
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 2)
    ,D_SW_SAMP_SAMP_MODE_1
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 3)
    ,D_SW_SAMP_SAMP_MODE_2
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 4)
    ,D_SW_SAMP_SAMP_MODE_3
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 5)
    ,D_SW_SAMP_SAMP_MODE_4
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 6)
    ,D_SW_SAMP_SAMP_MODE_5
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 7)
    ,D_SW_SAMP_SAMP_MODE_6
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 8)
    ,D_SW_SAMP_SAMP_MODE_7
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 9)
    ,D_SW_SAMP_SAMP_MODE_8
  #endif
  
  #if (D_SW_SAMP_OBJ_NUM >= 10)
    ,D_SW_SAMP_SAMP_MODE_9
  #endif 
};

/* macro definition */  

#define D_SW_SAMP_ALL_SET             (uint8_t)0x15u    /* All "D_SW_SAMP_HIGH" in three consecutive sampling */
#define D_SW_SAMP_ALL_CLR             (uint8_t)0x00u    /* All "D_SW_SAMP_LOW" in three consecutive sampling  */
#define D_SW_SAMP_6_BITS_MASK         (uint8_t)0x3Fu    /* Mask for low 6 bits                                */
#define D_SW_SAMP_2_BITS_MASK         (uint8_t)0x03u    /* Mask for low 2 bits                                */
#define D_SW_SAMP_1_BITS_MASK         (uint8_t)0x01u    /* Mask for low 1 bits                                */



#endif    /* end of header file */
