/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : FlashManageDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/07/06    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef FLASH_MANA_DRV_C
#define FLASH_MANA_DRV_C
#endif

/* include files */
#include "FlashManageDrv.h"

/* static variable definition */
static nrf_fstorage_api_t * p_fs_api;

static void fstorage_evt_handler(nrf_fstorage_evt_t * p_evt);
    
NRF_FSTORAGE_DEF(nrf_fstorage_t fstorage) =
{
    /* Set a handler for fstorage events. */
    .evt_handler = fstorage_evt_handler,

    /* These below are the boundaries of the flash space assigned to this instance of fstorage.
     * You must set these manually, even at runtime, before nrf_fstorage_init() is called.
     * The function nrf5_flash_end_addr_get() can be used to retrieve the last address on the
     * last page of flash available to write data. */
    .start_addr = FLASH_BASE_ADDR,
    .end_addr   = DS_BACKUP_ADDR_D1_END_ADDR,
};
/* static function declaration */

/* static function definition */
static void fstorage_evt_handler(nrf_fstorage_evt_t * p_evt)
{
    if (p_evt->result != NRF_SUCCESS)
    {
        //NRF_LOG_INFO("--> Event received: ERROR while executing an fstorage operation.");
        return;
    }

    switch (p_evt->id)
    {
        case NRF_FSTORAGE_EVT_WRITE_RESULT:
        {
           // NRF_LOG_INFO("--> Event received: wrote %d bytes at address 0x%x.", p_evt->len, p_evt->addr);
        } break;

        case NRF_FSTORAGE_EVT_ERASE_RESULT:
        {
            //NRF_LOG_INFO("--> Event received: erased %d page from address 0x%x.",p_evt->len, p_evt->addr);
        } break;

        default:
            break;
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  DataBackup
* Description   : back up a sector  
*
* Inputs        : @param  None:
*                          
*                 @global val  None:
*                  
* Outputs       : @param   None:
*                          
*                 @global val  None:
*
*                 @retval :The sector of a given address
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
static uint8_t DataBackup(uint32_t aTypeStartAddress,uint32_t aTypeEndAddress)
{
    ret_code_t rc;
    uint8_t ret = DS_DATA_OPERATE_SUCCESS;
    
    uint16_t  copyWriteLen = 0;
    uint32_t  StartSectorAddr = aTypeStartAddress & (~(FLASH_PAGE_SIZE - 1));
    uint32_t  BackUpStartSectorAddrr = (FLASH_BACKUP_BASE_ADDR + aTypeStartAddress - FLASH_BASE_ADDR) & (~(FLASH_PAGE_SIZE - 1));
    
    nrf_fstorage_erase(&fstorage,BackUpStartSectorAddrr,1,NULL);      //erease backup page
    while (nrf_fstorage_is_busy(&fstorage))
    {
        
    }
    //NRF_LOG_INFO("erease backup page =0x%x Done.",BackUpStartSectorAddrr);
    
    rc = nrf_fstorage_write(&fstorage, BackUpStartSectorAddrr, (void *)(StartSectorAddr), FLASH_PAGE_SIZE, NULL);
    APP_ERROR_CHECK(rc);

    while (nrf_fstorage_is_busy(&fstorage))
    {
        
    }
    //NRF_LOG_INFO("copy page 0x%x => 0x%x Done.",StartSectorAddr,BackUpStartSectorAddrr);
    nrf_fstorage_erase(&fstorage,StartSectorAddr,1,NULL);      //erease write page
    while (nrf_fstorage_is_busy(&fstorage))
    {
        
    }
    //NRF_LOG_INFO("erease write page 0x%x Done.",StartSectorAddr);
    
    if(aTypeStartAddress > StartSectorAddr)   //copy other type data to write page
    {
         copyWriteLen = aTypeStartAddress - StartSectorAddr;
        rc = nrf_fstorage_write(&fstorage, StartSectorAddr, (void *)(BackUpStartSectorAddrr), copyWriteLen, NULL);
        APP_ERROR_CHECK(rc);

        while (nrf_fstorage_is_busy(&fstorage))
        {
            
        }
        //NRF_LOG_INFO("copy data 0x%x => 0x%x Done.",StartSectorAddr,BackUpStartSectorAddrr);
    }
    if(aTypeEndAddress < (StartSectorAddr + FLASH_PAGE_SIZE -1))
    {
        copyWriteLen = FLASH_PAGE_SIZE - (aTypeEndAddress - StartSectorAddr +  1u) ;
        rc = nrf_fstorage_write(&fstorage, aTypeEndAddress+1, (void *)(BackUpStartSectorAddrr + (aTypeEndAddress - StartSectorAddr +1u)), copyWriteLen, NULL);
        APP_ERROR_CHECK(rc);

        while (nrf_fstorage_is_busy(&fstorage))
        {
            
        }
        //NRF_LOG_INFO("copy data 0x%x => 0x%x Done.",aTypeEndAddress+1,(BackUpStartSectorAddrr + (aTypeEndAddress - StartSectorAddr +1u)));
    }
    NRF_LOG_INFO("backup Done.");
    
    return ret;
}
/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  FlashDataWrite
* Description   :  
*
* Inputs        : @param  None:
*                          
*                 @global val  None:
*                  
* Outputs       : @param   None:
*                          
*                 @global val  None:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t FlashDataWrite(uint8_t aDataType,uint32_t aAddroffset,uint8_t *apbuf,uint16_t aLen,uint16_t backupflg)
{
    uint8_t ret = DS_DATA_OPERATE_SUCCESS;
    uint32_t aStartAddr = 0;
    uint32_t aEndAddr = 0;
    SaveData_header_str  dataHeader_t;
    ret_code_t rc;
    switch(aDataType)
    {
        case DS_DATA_TYPE_D0 :
        {
            aStartAddr = aAddroffset + DS_ADDR_D0_START_ADDR;
            aEndAddr = DS_ADDR_D0_END_ADDR;
            if(((aStartAddr % 4u) == 0u) && ((aLen % 4u) == 0u) && (aLen  <= FLASH_PAGE_SIZE) &&(aStartAddr >= DS_ADDR_D0_START_ADDR) && ((aStartAddr + aLen) <= (DS_ADDR_D0_END_ADDR +1u ) ))
            {
                //do nothing
            }
            else
            {
                ret = DS_DATA_OPERATE_FAILURE;
            }
        }
        break;
        case DS_DATA_TYPE_D1 :
        {
            aStartAddr = aAddroffset + DS_ADDR_D1_START_ADDR;
            aEndAddr = DS_ADDR_D1_END_ADDR;
            if(((aStartAddr % 4u) == 0u) && ((aLen % 4u) == 0u) && (aLen  <= FLASH_PAGE_SIZE)&&(aStartAddr >= DS_ADDR_D1_START_ADDR) && ((aStartAddr + aLen) <= (DS_ADDR_D1_END_ADDR +1u ) ))
            {
                //do nothing
            }
            else
            {
                ret = DS_DATA_OPERATE_FAILURE;
            }
        }
        break;
        case DS_DATA_TYPE_D2 :
        {
            aStartAddr = aAddroffset + DS_ADDR_D2_START_ADDR;
            aEndAddr = DS_ADDR_D2_END_ADDR;
            if(((aStartAddr % 4u) == 0u) && ((aLen % 4u) == 0u) && (aLen  <= FLASH_PAGE_SIZE)&&(aStartAddr >= DS_ADDR_D2_START_ADDR) && ((aStartAddr + aLen) <= (DS_ADDR_D2_END_ADDR +1u ) ))
            {
                //do nothing
            }
            else
            {
                ret = DS_DATA_OPERATE_FAILURE;
            }
        }
        break;
        default:
            ret = DS_DATA_OPERATE_FAILURE;
        break;
    }
    if(ret == DS_DATA_OPERATE_SUCCESS)
    {
        dataHeader_t.mSaveFlg = DS_DATA_SAVE_FLG;
        dataHeader_t.mSaveType = aDataType;
        dataHeader_t.mBackupFlg = backupflg;
        dataHeader_t.mTxLength = aLen;
        dataHeader_t.mAddrOffset = aAddroffset;
        dataHeader_t.mcrc16 = crc16_compute(apbuf,aLen,NULL);
        
        if(dataHeader_t.mBackupFlg == DS_DATA_OPERATE_BACKUP_EN)
        {
            DataBackup(aStartAddr,aEndAddr);
        }
        else
        {
            if((aStartAddr % FLASH_PAGE_SIZE) == 0u) //page start address
            {
                nrf_fstorage_erase(&fstorage,aStartAddr & (~(FLASH_PAGE_SIZE - 1)),1,NULL);      //erease page
                while (nrf_fstorage_is_busy(&fstorage))
                {
                    
                }  
            }
        }
        //write header
        rc = nrf_fstorage_write(&fstorage, aStartAddr, (void *)(&dataHeader_t), sizeof(SaveData_header_str), NULL);
        APP_ERROR_CHECK(rc);
        while (nrf_fstorage_is_busy(&fstorage))
        {
            
        }
        //write data buff
        rc = nrf_fstorage_write(&fstorage, aStartAddr+sizeof(SaveData_header_str), (void *)(apbuf), aLen, NULL);
        APP_ERROR_CHECK(rc);
        while (nrf_fstorage_is_busy(&fstorage))
        {
            
        }
    }
    else
    {
        //do nothing
    }
    return ret;
}    
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  EEPROMRead
* Description   :  
*
* Inputs        : @param  : 
*                 @param  : 
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t FlashDataRead(uint8_t aDataType,uint32_t aAddroffset,uint8_t *apbuf,uint16_t aLen)
{
    uint8_t ret = DS_DATA_OPERATE_SUCCESS;
    uint16_t crc = 0;
    uint32_t aAddr = 0;
    uint32_t abackupAddr = 0;
    ret_code_t rc;
    SaveData_header_str  dataHeader_t;
    switch(aDataType)
    {
        case DS_DATA_TYPE_D0 :
        {
            aAddr = aAddroffset + DS_ADDR_D0_START_ADDR;
            abackupAddr = aAddroffset + DS_BACKUP_ADDR_D0_START_ADDR;
            if((aAddr >= DS_ADDR_D0_START_ADDR) && ((aAddr + aLen) <= (DS_ADDR_D0_END_ADDR +1u ) ))
            {
                //do nothing
            }
            else
            {
                ret = DS_DATA_OPERATE_FAILURE;
            }
        }
        break;
        case DS_DATA_TYPE_D1 :
        {
            aAddr = aAddroffset + DS_ADDR_D1_START_ADDR;
            abackupAddr = aAddroffset + DS_BACKUP_ADDR_D1_START_ADDR;
            if((aAddr >= DS_ADDR_D1_START_ADDR) && ((aAddr + aLen) <= (DS_ADDR_D1_END_ADDR +1u ) ))
            {
               //do nothing
            }
            else
            {
                ret = DS_DATA_OPERATE_FAILURE;
            }
        }
        break;
        case DS_DATA_TYPE_D2 :
        {
            aAddr = aAddroffset + DS_ADDR_D2_START_ADDR;
            if((aAddr >= DS_ADDR_D2_START_ADDR) && ((aAddr + aLen) <= (DS_ADDR_D2_END_ADDR +1u ) ))
            {
                //do nothing
            }
            else
            {
                ret = DS_DATA_OPERATE_FAILURE;
            }
        }
        break;
        default:
            ret = DS_DATA_OPERATE_FAILURE;
        break;
    }
    if(ret == DS_DATA_OPERATE_SUCCESS)
    {
        //read header
        rc = nrf_fstorage_read(&fstorage, aAddr, &dataHeader_t, sizeof(SaveData_header_str));
        if (rc != NRF_SUCCESS)
        {
            NRF_LOG_INFO("nrf_fstorage_read() returned: %x\n",rc);
            return DS_DATA_OPERATE_FAILURE;
        }
        if(dataHeader_t.mSaveFlg == DS_DATA_SAVE_FLG)
        {
             rc = nrf_fstorage_read(&fstorage, aAddr +sizeof(SaveData_header_str) , apbuf, dataHeader_t.mTxLength);
            if (rc != NRF_SUCCESS)
            {
                NRF_LOG_INFO("nrf_fstorage_read() returned: %x\n",rc);
                return DS_DATA_OPERATE_FAILURE;
            }
            crc = crc16_compute(apbuf,dataHeader_t.mTxLength,NULL);
            if((crc != dataHeader_t.mcrc16) && (dataHeader_t.mBackupFlg == DS_DATA_OPERATE_BACKUP_EN))
            {
                //read backup
                rc = nrf_fstorage_read(&fstorage, abackupAddr, &dataHeader_t, sizeof(SaveData_header_str));
                if (rc != NRF_SUCCESS)
                {
                    NRF_LOG_INFO("nrf_fstorage_read() returned: %x\n",rc);
                    return DS_DATA_OPERATE_FAILURE;
                }
                if(dataHeader_t.mSaveFlg == DS_DATA_SAVE_FLG)
                {
                    rc = nrf_fstorage_read(&fstorage, abackupAddr +sizeof(SaveData_header_str) , apbuf, dataHeader_t.mTxLength);
                    if (rc != NRF_SUCCESS)
                    {
                        NRF_LOG_INFO("nrf_fstorage_read() returned: %x\n",rc);
                        return DS_DATA_OPERATE_FAILURE;
                    }
                    crc = crc16_compute(apbuf,dataHeader_t.mTxLength,NULL);
                    if(crc != dataHeader_t.mcrc16)
                    {
                        NRF_LOG_INFO("flash read backup %x crc err\n", abackupAddr);
                    }
                }
                else
                {
                    NRF_LOG_INFO("flash read backup %x no data\n", abackupAddr);
                    ret = DS_DATA_OPERATE_FAILURE;
                }
            }
            else if(crc != dataHeader_t.mcrc16)
            {
                 NRF_LOG_INFO("nrf_fstorage_read write page %x crc err\n", aAddr);
                ret = DS_DATA_OPERATE_FAILURE;
            }
            else
            {
                //do nothing
            }
        }
        else
        {
            ret = DS_DATA_OPERATE_FAILURE;
        }
    }
    return ret;
}    
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  FlashManageInit
* Description   :  
*
* Inputs        : @param  : 
*                 @param  : 
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void FlashManageInit(void)
{
    ret_code_t rc;
    p_fs_api = &nrf_fstorage_sd;
    rc = nrf_fstorage_init(&fstorage, p_fs_api, NULL);
    APP_ERROR_CHECK(rc);
    /*NRF_LOG_INFO("========| flash info |========");
    NRF_LOG_INFO("erase unit: \t%d bytes",      fstorage.p_flash_info->erase_unit);
    NRF_LOG_INFO("program unit: \t%d bytes",    fstorage.p_flash_info->program_unit);
    NRF_LOG_INFO("=============================="); */
}

/***********************************************END**********************************************/
