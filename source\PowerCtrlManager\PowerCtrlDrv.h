/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : PowerCtrlDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/11/30    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef PWR_CTRL_DRV_H
#define PWR_CTRL_DRV_H

/*Include files*/
#include "PowerCtrlAppExt.h"

/* support module */
#include "includes.h"
#include "nrf_bootloader_info.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
#define PWR_CTRL_BAT_TYPE_LI                          0u
#define PWM_CTRL_BAT_SAMP_FILTER_TIMES                3u  // 10ms unit
/*charge config*/
#define PWR_CTRL_PIN_CHG_EN                           SYS_CFG_PIN_CTRL_POWER_CHG_EN
#define PWR_CTRL_PIN_CHG_PGOOD                        SYS_CFG_PIN_CTRL_POWER_CHG_PG
#define PWR_CTRL_PIN_CHG_DET                          SYS_CFG_PIN_CTRL_POWER_CHG_DET

#define PWR_CTRL_PIN_BAT_SAMP                         SYS_CFG_BAT_DETECT_ADC_PIN
#define PWR_CTRL_ADC_CHNL_BAT_SAMP                    SYS_CFG_BAT_DETECT_ADC_NUM

//IMU power control
#define PWR_CTRL_PIN_IMU_POWER                        SYS_CFG_PIN_CTRL_POWER_IMU

//TP power control
#define PWR_CTRL_PIN_TP_POWER                         SYS_CFG_PIN_CTRL_POWER_TP

//HALL power control
#define PWR_CTRL_PIN_HALL_POWER                       SYS_CFG_PIN_CTRL_POWER_HALL

//ELEC_MAGNET power control
#define PWR_CTRL_PIN_ELEC_MAGNET_POWER                SYS_CFG_PIN_CTRL_POWER_ELEC_MAGNET

//motor 
#define PWR_CTRL_PIN_MOTOR_POWER                      SYS_CFG_PIN_MOTOR

//AIN  sample power
#define PWR_CTRL_PIN_AIN_KEY_POWER                    SYS_CFG_PIN_CTRL_POWER_AIN

//Blob power control
#define PWR_CTRL_PIN_BLOB_CTRL_R                      SYS_CFG_PIN_BLOB_LED_R
#define PWR_CTRL_PIN_BLOB_CTRL_G                      SYS_CFG_PIN_BLOB_LED_G
#define PWR_CTRL_PIN_BLOB_CTRL_B                      SYS_CFG_PIN_BLOB_LED_B

//power on hold 
#define PWR_CTRL_PIN_HOLD_CTRL                        SYS_CFG_PIN_CTRL_POWER_HOLD

#define DRY_BATT_PERCENT_0_VOLT     1800
#define DRY_BATT_PERCENT_100_VOLT   3006
#define BATT_CONVERTER_COUNTS       1000

// #define Li_BATT_PERCENT_0_VOLT      3280
// #define Li_BATT_PERCENT_100_VOLT    4180
#define Li_BATT_PERCENT_0_VOLT      (3200u)
#define Li_BATT_PERCENT_100_VOLT    (4100u)
#define Li_BATT_CENTI_VOLT					(Li_BATT_PERCENT_100_VOLT-Li_BATT_PERCENT_0_VOLT)
#define Li_BATT_CONVERTER_COUNTS    1000

#define BATT_VOLT_FILTER_NEW_VALUE_WEIGHT   0.01f


/* data type definiton  */
 const uint8_t dry_batt_converter[BATT_CONVERTER_COUNTS] = {
  0,   0,   0,   0,   0,   0,   0,   0,   0,   0, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   3,   3,   3,   3,   3,   3,   3,   3,   3, 
  3,   3,   3,   3,   3,   3,   3,   3,   3,   3, 
  3,   3,   3,   3,   3,   3,   3,   3,   3,   3, 
  3,   3,   3,   3,   3,   3,   3,   3,   3,   3, 
  3,   4,   4,   4,   4,   4,   4,   4,   4,   4, 
  4,   4,   4,   4,   4,   4,   4,   4,   4,   4, 
  4,   4,   4,   4,   4,   4,   4,   4,   4,   4, 
  4,   5,   5,   5,   5,   5,   5,   5,   5,   5, 
  5,   5,   5,   5,   5,   5,   5,   5,   5,   5, 
  5,   5,   5,   5,   5,   5,   5,   5,   5,   5, 
  5,   6,   6,   6,   6,   6,   6,   6,   6,   6, 
  6,   6,   6,   6,   6,   6,   6,   6,   6,   6, 
  6,   7,   7,   7,   7,   7,   7,   7,   7,   7, 
  7,   7,   7,   7,   7,   7,   7,   7,   7,   7, 
  7,   7,   7,   7,   7,   7,   7,   7,   7,   7, 
  7,   8,   8,   8,   8,   8,   8,   8,   8,   8, 
  8,   8,   8,   8,   8,   8,   8,   8,   8,   8, 
  8,   9,   9,   9,   9,   9,   9,   9,   9,   9, 
  9,   9,   9,   9,   9,   9,   9,   9,   9,   9, 
  9,  10,  10,  10,  10,  10,  10,  10,  10,  10, 
 10,  10,  10,  10,  10,  10,  10,  10,  10,  10, 
 10,  11,  11,  11,  11,  11,  11,  11,  11,  11, 
 11,  12,  12,  12,  12,  12,  12,  12,  12,  12, 
 12,  12,  12,  12,  12,  12,  12,  12,  12,  12, 
 12,  13,  13,  13,  13,  13,  13,  13,  13,  13, 
 13,  13,  13,  13,  13,  13,  13,  13,  13,  13, 
 13,  14,  14,  14,  14,  14,  14,  14,  14,  14, 
 14,  14,  14,  14,  14,  14,  14,  14,  14,  14, 
 14,  15,  15,  15,  15,  15,  15,  15,  15,  15, 
 15,  16,  16,  16,  16,  16,  16,  16,  16,  16, 
 16,  17,  17,  17,  17,  17,  17,  17,  17,  17, 
 17,  18,  18,  18,  18,  18,  18,  18,  18,  18, 
 18,  18,  18,  18,  18,  18,  18,  18,  18,  18, 
 18,  19,  19,  19,  19,  19,  19,  19,  19,  19, 
 19,  20,  20,  20,  20,  20,  20,  20,  20,  20, 
 20,  21,  21,  21,  21,  21,  21,  21,  21,  21, 
 21,  22,  22,  22,  22,  22,  22,  22,  22,  22, 
 22,  22,  22,  22,  22,  22,  22,  22,  22,  22, 
 22,  23,  23,  23,  23,  23,  23,  23,  23,  23, 
 23,  24,  24,  24,  24,  24,  24,  24,  24,  24, 
 24,  25,  25,  25,  25,  25,  25,  25,  25,  25, 
 25,  26,  26,  26,  26,  26,  27,  27,  27,  27, 
 27,  28,  28,  28,  28,  28,  28,  28,  28,  28, 
 28,  29,  29,  29,  29,  29,  30,  30,  30,  30, 
 30,  31,  31,  31,  31,  31,  31,  31,  31,  31, 
 31,  32,  32,  32,  32,  32,  33,  33,  33,  33, 
 33,  34,  34,  34,  34,  34,  34,  34,  34,  34, 
 34,  35,  35,  35,  35,  35,  36,  36,  36,  36, 
 36,  37,  37,  37,  37,  37,  38,  38,  38,  38, 
 38,  39,  39,  39,  40,  40,  40,  40,  40,  41, 
 41,  42,  42,  42,  42,  42,  42,  42,  42,  42, 
 42,  43,  43,  43,  44,  44,  44,  45,  45,  45, 
 46,  46,  47,  47,  47,  47,  47,  48,  48,  48, 
 49,  50,  50,  51,  51,  51,  51,  52,  52,  53, 
 53,  54,  54,  55,  55,  56,  56,  56,  56,  57, 
 57,  58,  58,  58,  59,  59,  59,  59,  59,  60, 
 60,  61,  61,  61,  62,  62,  62,  62,  62,  63, 
 63,  64,  64,  64,  64,  64,  65,  65,  65,  65, 
 65,  66,  66,  66,  66,  66,  67,  67,  67,  67, 
 67,  68,  68,  68,  68,  68,  69,  69,  69,  69, 
 69,  70,  70,  70,  71,  71,  71,  71,  71,  72, 
 72,  73,  73,  73,  74,  74,  74,  74,  74,  75, 
 75,  76,  76,  76,  77,  77,  77,  77,  77,  78, 
 78,  79,  79,  79,  80,  80,  80,  80,  80,  81, 
 81,  82,  82,  82,  82,  82,  83,  83,  83,  83, 
 83,  84,  84,  84,  84,  84,  84,  84,  84,  84, 
 84,  85,  85,  85,  85,  85,  86,  86,  86,  86, 
 86,  87,  87,  87,  87,  87,  88,  88,  88,  88, 
 88,  89,  89,  89,  89,  89,  90,  90,  90,  90, 
 90,  91,  91,  91,  91,  91,  92,  92,  92,  92, 
 92,  93,  93,  93,  93,  93,  93,  93,  93,  94, 
 94,  95,  95,  95,  95,  95,  95,  95,  95,  96, 
 96,  97,  97,  97,  97,  97,  97,  97,  97,  97, 
 97,  98,  98,  98,  98,  98,  98,  98,  98,  99, 
 99,  99,  99,  99,  99,  99,  99,  99,  99,  99, 
 99, 100, 100, 100, 100, 100, 100, 100, 100, 100, 
100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 
100, 100, 100, 100, 100, 100, 100, 100, 100, 100, 
};

static const uint8_t Li_batt_converter[Li_BATT_CONVERTER_COUNTS] = {
  0,   0,   0,   0,   0,   0,   0,   0,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   1,   1,   1,   1,   1,   1,   1, 
  1,   1,   1,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   2,   2,   2,   2,   2,   2,   2,   2, 
  2,   2,   3,   3,   3,   3,   3,   3,   3,   3, 
  3,   3,   3,   3,   3,   3,   3,   3,   3,   3, 
  3,   3,   3,   4,   4,   4,   4,   4,   4,   4, 
  4,   4,   4,   4,   4,   4,   4,   5,   5,   5, 
  5,   5,   5,   5,   5,   6,   6,   6,   6,   7, 
  7,   7,   8,   8,   9,   9,  10,  11,  11,  12, 
 12,  12,  13,  13,  13,  14,  14,  14,  15,  15, 
 15,  16,  16,  16,  17,  17,  17,  18,  18,  19, 
 19,  20,  20,  21,  21,  22,  23,  23,  24,  25, 
 25,  26,  27,  28,  28,  29,  30,  31,  32,  33, 
 34,  34,  35,  36,  36,  37,  38,  38,  39,  40, 
 40,  41,  41,  42,  42,  43,  43,  44,  45,  45, 
 46,  46,  47,  47,  48,  48,  48,  49,  49,  50, 
 50,  51,  51,  52,  52,  52,  53,  53,  54,  54, 
 54,  55,  55,  55,  56,  56,  57,  57,  57,  58, 
 58,  58,  59,  59,  59,  60,  60,  60,  61,  61, 
 61,  61,  62,  62,  62,  63,  63,  63,  63,  64, 
 64,  64,  65,  65,  65,  65,  66,  66,  67,  67, 
 67,  67,  68,  68,  68,  69,  69,  70,  70,  70, 
 71,  71,  71,  71,  72,  72,  72,  72,  73,  73, 
 73,  73,  73,  73,  74,  74,  74,  74,  74,  74, 
 75,  75,  75,  75,  75,  75,  75,  76,  76,  76, 
 76,  76,  76,  76,  77,  77,  77,  77,  77,  77, 
 78,  78,  78,  78,  79,  79,  79,  80,  80,  80, 
 80,  81,  81,  81,  81,  82,  82,  82,  82,  82, 
 83,  83,  83,  83,  83,  84,  84,  84,  84,  84, 
 85,  85,  85,  85,  86,  86,  86,  86,  86,  87, 
 87,  87,  87,  87,  88,  88,  88,  88,  88,  89,  
 89,  89,  89,  89,  90,  90,  91,  91,  91,  91, 
 92,  92,  92,  93,  93,  94,  94,  95,  95,  96, 
 96,  97,  97,  98,  98,  99,  99,  100,  100,  100, 
 100,  100,  100,  100,  100,  100,  100,  100,  100,  100, 
 100,  100,  100,  100,  100,  100,  100,  100,  100,  100, 
 100,  100,  100,  100,  100,  100,  100,  100,  100,  100,  
};
/* function configuration definition */
#define POWER_DELAY_MS(time)          nrf_delay_ms(time)

#endif /* End of header file */
