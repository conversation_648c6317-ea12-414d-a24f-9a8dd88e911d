/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : PacketProcessDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/06    guotai        N/A            Original                
*   1.1         2018/03/27    guotai                       UVC application 
************************************************************************************************
* END_FILE_HDR*/

#ifndef PACKET_PROCESS_DRV_C
#define PACKET_PROCESS_DRV_C
#endif

/* include files */
#include "PacketProcessDrv.h"

/* static variable definition */
static uint8_t  gUsbRxBuf[USB_RX_BUF_SIZE];
static uint8_t  gBleRxBuf[BLE_RX_BUF_SIZE];

/* static function declaration */

/* static function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  SendEvtFrame
* Description   :  send one frame Event data
*
* Inputs        : @param  apData: send data buffer pointer
*                 @param  aLen:   send length
*                 @param  aIntfType: send interface 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : send  status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t UsbSendSingleFrame(uint8_t *apData,uint16_t aLen)
{
    uint8_t ret = PACKET_OPERATE_SUCCESS;
    uint8_t RepeatSendCnt = FRAME_SEND_CNT_MAX;
    
    uint8_t sendSt = 0;

    sendSt = USBD_CUSTOM_HID_SendDataMsg(apData,USB_TX_FRAM_SIZE);

    while((sendSt == USB_RW_ST_BUSY) && (--RepeatSendCnt))
    {
        TIME_DELAY(FRAM_SEND_DELAY_TIME);//delay 1ms
        sendSt = USBD_CUSTOM_HID_SendDataMsg(apData,USB_TX_FRAM_SIZE);
    }
    if(sendSt != USB_RW_ST_OK)
    {
        ret = PACKET_OPERATE_FAIL;
    }
    else
    {
        //do nothing
    } 
    
    return ret;
}

uint8_t BleSendSingleFrame(uint16_t serviceBaseUUID,uint8_t *apData,uint16_t aLen)
{
    uint8_t RepeatSendCnt = FRAME_SEND_CNT_MAX;
    uint8_t ret = PACKET_OPERATE_SUCCESS;
    uint8_t sendSt = NRF_SUCCESS;

    sendSt = XimService_data_send(serviceBaseUUID,apData,aLen);

    while(((sendSt == NRF_ERROR_BUSY) || (sendSt == NRF_ERROR_RESOURCES)) && (--RepeatSendCnt))
    {
        TIME_DELAY(FRAM_SEND_DELAY_TIME);//delay 1ms
        sendSt = XimService_data_send(serviceBaseUUID,apData,aLen);
    }
    if(sendSt != NRF_SUCCESS)
    {
        NRF_LOG_INFO("single Send fail =%x",sendSt);
        ret = PACKET_OPERATE_FAIL;
    }
    else
    {
        //do nothing
    } 
    
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  RxDataCombPacket
* Description   :  recieve fram combination massege
*
* Inputs        : @param  apData: rx data buffer pointer
*                 @param  aLen:   rx length
*                 @param  apOutLen: massege Length output pointer 
*                 @param  aIntfType: send interface 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
static uint8_t UsbRxDataCombPacket(uint8_t * pRxBuffer, uint16_t RxLen, uint16_t *apOutLen)
{
    static uint8_t UsbRxPacketNum = 0u;
    static uint16_t UsbRxPacketLen = 0u;
    uint8_t  *pRxPacketNum = NULL;
    uint16_t *pRxPacketLen = NULL;
    uint8_t *pMsgBuf = NULL;
    uint8_t  ret = RX_PACKET_FINISH;
    uint16_t MsgBufSize =0;
    uint16_t FramSize =0;
     
    pRxPacketNum = &UsbRxPacketNum;
    pRxPacketLen = &UsbRxPacketLen;
    pMsgBuf = gUsbRxBuf;
    MsgBufSize = USB_RX_BUF_SIZE;
    FramSize = USB_RX_FRAM_SIZE;
   
    if(USB_DATA_PACKET_HEAD == (0xC0 & pRxBuffer[0]))
    {
        if((0x1F&pRxBuffer[0]) == 0x00) // first frame
        {
            *pRxPacketNum = 0;
            *pRxPacketLen = 0;    
        }
        if((0x1F&(*pRxPacketNum))==(0x1F&pRxBuffer[0]))
        {
            if((USB_DATA_PACKET_END & pRxBuffer[0]) == USB_DATA_PACKET_END) //last packet
            {        
                if( ((*pRxPacketLen) + RxLen-1) >= MsgBufSize)
                {
                    ret =  RX_PACKET_START; 
                    DEBUG_PRINTF("Rxdata>buf size\r\n");
                }
                else
                {
                    if(((RxLen-1u)%4u) == 0u)
                    {
                        memcpy(&pMsgBuf[(*pRxPacketLen)], &pRxBuffer[1], RxLen-1u);
                    }
                    else
                    {
                        memcpy(&pMsgBuf[(*pRxPacketLen)], &pRxBuffer[1], RxLen-1u + (4u - ((RxLen-1u)%4u)));
                    }
                    *pRxPacketLen = (*pRxPacketLen) + RxLen-1u;
                    *apOutLen = *pRxPacketLen ;
                }
                *pRxPacketNum = 0;
                *pRxPacketLen = 0;
            }
            else
            {
                if(FramSize != RxLen)
                {
                    ret = RX_PACKET_START;
                    *pRxPacketNum = 0;
                    *pRxPacketLen = 0;
                    DEBUG_PRINTF("RxLen err = %d ,Hed%x,%x,%x\r\n",RxLen,pRxBuffer[0],pRxBuffer[1],pRxBuffer[2]);
                }
                else
                {
                    if( ((*pRxPacketLen) + RxLen-1) >= MsgBufSize)
                    {
                        ret =  RX_PACKET_START;
                        *pRxPacketNum = 0;
                        *pRxPacketLen = 0;
                        DEBUG_PRINTF("Rxdata>buf size\r\n");
                    }
                    else
                    {
                        if(((RxLen-1u)%4u) == 0u)
                        {
                            memcpy(&pMsgBuf[(*pRxPacketLen)], &pRxBuffer[1], RxLen-1u);
                        }
                        else
                        {
                            memcpy(&pMsgBuf[(*pRxPacketLen)], &pRxBuffer[1], RxLen-1u + (4u - ((RxLen-1u)%4u)));
                        }
                         *pRxPacketLen = (*pRxPacketLen) + RxLen-1u;
                         (*pRxPacketNum)++;  
                         ret = RX_PACKET_WAIT;
                    }
                }
            }   
        }    
        else
        {
            *pRxPacketNum = 0;
            *pRxPacketLen = 0; 
            ret =  RX_PACKET_START; 
            DEBUG_PRINTF("RxNum err\r\n");
        }
    }
    else
    {
        ret =  RX_PACKET_START;
         DEBUG_PRINTF("RxType err=%x%x%x%x, Len =%d\r\n",pRxBuffer[0],pRxBuffer[1],pRxBuffer[2],pRxBuffer[3],RxLen);
    }
    return ret;
}

static uint8_t BleRxDataCombPacket(uint8_t * pRxBuffer, uint16_t RxLen, uint16_t *apOutLen)
{
    static uint8_t BleRxPacketNum = 0u;
    static uint16_t BleRxPacketLen = 0u;
    uint8_t  *pRxPacketNum = NULL;
    uint16_t *pRxPacketLen = NULL;
    uint8_t *pMsgBuf = NULL;
    uint8_t  ret = RX_PACKET_FINISH;
    uint16_t MsgBufSize =0;
    uint16_t FramSize =0;
     
    pRxPacketNum = &BleRxPacketNum;
    pRxPacketLen = &BleRxPacketLen;
    pMsgBuf = gBleRxBuf;
    MsgBufSize = BLE_RX_BUF_SIZE;
    FramSize = gerCurrentMtuSize();
   
    if(BLE_DATA_PACKET_HEAD == (0xC0 & pRxBuffer[0]))
    {
        if((0x1F&pRxBuffer[0]) == 0x00) // first frame
        {
            *pRxPacketNum = 0;
            *pRxPacketLen = 0;    
        }
        if((0x1F&(*pRxPacketNum))==(0x1F&pRxBuffer[0]))
        {
            if((BLE_DATA_PACKET_END & pRxBuffer[0]) == BLE_DATA_PACKET_END) //last packet
            {        
                if( ((*pRxPacketLen) + RxLen-1) >= MsgBufSize)
                {
                    ret =  RX_PACKET_START; 
                    DEBUG_PRINTF("Rxdata>buf size\r\n");
                }
                else
                {
                    memcpy(&pMsgBuf[(*pRxPacketLen)], &pRxBuffer[1], RxLen-1u);    
                    *pRxPacketLen = (*pRxPacketLen) + RxLen-1u;
                    *apOutLen = *pRxPacketLen ;
                }
                *pRxPacketNum = 0;
                *pRxPacketLen = 0;
            }
            else
            {
                if(FramSize != RxLen)
                {
                    ret = RX_PACKET_START;
                    *pRxPacketNum = 0;
                    *pRxPacketLen = 0;
                    DEBUG_PRINTF("RxLen err = %d ,Hed%x,%x,%x\r\n",RxLen,pRxBuffer[0],pRxBuffer[1],pRxBuffer[2]);
                }
                else
                {
                    if( ((*pRxPacketLen) + RxLen-1) >= MsgBufSize)
                    {
                        ret =  RX_PACKET_START;
                        *pRxPacketNum = 0;
                        *pRxPacketLen = 0;
                        DEBUG_PRINTF("Rxdata>buf size\r\n");
                    }
                    else
                    {
                         memcpy(&pMsgBuf[(*pRxPacketLen)], &pRxBuffer[1], RxLen-1u);                  
                         *pRxPacketLen = (*pRxPacketLen) + RxLen-1u;
                         (*pRxPacketNum)++;  
                         ret = RX_PACKET_WAIT;
                    }
                }
            }   
        }    
        else
        {
            *pRxPacketNum = 0;
            *pRxPacketLen = 0; 
            ret =  RX_PACKET_START; 
            DEBUG_PRINTF("RxNum err\r\n");
        }
    }
    else
    {
        ret =  RX_PACKET_START;
         DEBUG_PRINTF("RxType err=%x%x%x%x, Len =%d\r\n",pRxBuffer[0],pRxBuffer[1],pRxBuffer[2],pRxBuffer[3],RxLen);
    }
    return ret;
}
/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  CheckOrCalcSum
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t CheckOrCalcSum(uint8_t* apBuf,uint16_t alen , uint8_t* apSum, uint8_t aType)
{
    uint16_t tmp = 0u;
    uint16_t i =0;
    
    for(i=0;i < alen;i++)
    {
        tmp += apBuf[i];
    }
    
    if(aType == PACKET_SUM_CALC)
    {
        apSum[1] = (uint8_t)(tmp >>8);
        apSum[0] = (uint8_t)tmp;
    }
    else
    {
        if(apSum[1] == (uint8_t)(tmp >>8) && apSum[0] == (uint8_t)tmp)
        {
            return PACKET_OPERATE_SUCCESS;
        }
        else
        {
            return PACKET_OPERATE_FAIL;
        }
    }
    return PACKET_OPERATE_SUCCESS;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  RecieveMsg
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t RecieveMsgComb(uint8_t * pRxBuffer, uint16_t RxLen,uint8_t **apMsgOut,uint16_t* apLenOut,uint8_t aIntfType)
{
    uint16_t recvLen = 0;
    uint8_t ret = PACKET_OPERATE_SUCCESS;

    if(aIntfType == SEND_INTERFACE_USB)
    {
        if(RX_PACKET_FINISH == UsbRxDataCombPacket(pRxBuffer,RxLen,&recvLen))
        {
             *apLenOut = recvLen;
             *apMsgOut = gUsbRxBuf;
        }
        else
        {
             ret = PACKET_OPERATE_FAIL;
             *apLenOut = 0u;
        }
    }
    else
    {
        if(RX_PACKET_FINISH == BleRxDataCombPacket(pRxBuffer,RxLen,&recvLen))
        {
             *apLenOut = recvLen;
             *apMsgOut = gBleRxBuf;
        }
        else
        {
             ret = PACKET_OPERATE_FAIL;
             *apLenOut = 0u;
        }
    }
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  SendMsg
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t BleMutilPacketTx(uint16_t serviceBaseUUID,uint8_t *apMsg,uint16_t aLen)
{
    static uint8_t SendBuf[NRF_SDH_BLE_GATT_MAX_MTU_SIZE + 6];
    uint8_t SendNum = 0;
    uint16_t LastSenLen  =0u;
    uint8_t ret = PACKET_OPERATE_SUCCESS;
    uint8_t n = 0;
    uint16_t FramSize =0;
    uint8_t  startIndex= 0u;
    FramSize = gerCurrentMtuSize();

    if((aLen%(FramSize-1u)) == 0u)
    {
       SendNum = aLen/(FramSize-1u);
    }
    else
    {
       SendNum = aLen/(FramSize-1u) + 1u;
    }
    for(n = 0u; n < SendNum ;n++)
    {
        if(n == (SendNum - 1u)) 
        {
            LastSenLen = aLen-((FramSize-1) * n);
            memcpy(&SendBuf[startIndex+1u],&apMsg[n*(FramSize-1)],LastSenLen);
               
            SendBuf[startIndex]=BLE_DATA_PACKET_HEAD+BLE_DATA_PACKET_END+(n&0x1F);
            if( PACKET_OPERATE_SUCCESS != BleSendSingleFrame(serviceBaseUUID,SendBuf,LastSenLen + 1u))
            {
                ret = PACKET_OPERATE_FAIL;
            }
            else
            {
                //do nothing
            }
            break;
        }
        else
        {
           SendBuf[startIndex]=BLE_DATA_PACKET_HEAD+(n & 0x1F);
        }
        memcpy(&SendBuf[startIndex+1u],&apMsg[n*(FramSize-1)],FramSize-1);
        
        if( PACKET_OPERATE_SUCCESS != BleSendSingleFrame(serviceBaseUUID,SendBuf,FramSize + startIndex))
        {        
            ret = PACKET_OPERATE_FAIL;
            break;
        }
        else
        {
           // do nothing
        }
    }
    return ret;
}

uint8_t UsbMutilPacketTx(uint8_t *apMsg,uint16_t aLen)
{
    static uint8_t SendBuf[USB_TX_FRAM_SIZE + 6];
    uint8_t SendNum = 0;
    uint16_t LastSenLen  =0u;
    uint8_t ret = PACKET_OPERATE_SUCCESS;
    uint8_t n = 0;
    uint16_t FramSize =0;
    uint8_t  startIndex= 0u;
    FramSize = USB_TX_FRAM_SIZE;

    if((aLen%(FramSize-1u)) == 0u)
    {
       SendNum = aLen/(FramSize-1u);
    }
    else
    {
       SendNum = aLen/(FramSize-1u) + 1u;
    }
    for(n = 0u; n < SendNum ;n++)
    {
        if(n == (SendNum - 1u)) 
        {
            LastSenLen = aLen-((FramSize-1) * n);
            memcpy(&SendBuf[startIndex+1u],&apMsg[n*(FramSize-1)],LastSenLen);
               
            SendBuf[startIndex]=USB_DATA_PACKET_HEAD+USB_DATA_PACKET_END+(n&0x1F);
            if( PACKET_OPERATE_SUCCESS != UsbSendSingleFrame(SendBuf,LastSenLen + 1u))
            {
                ret = PACKET_OPERATE_FAIL;
            }
            else
            {
                //do nothing
            }
            break;
        }
        else
        {
           SendBuf[startIndex]=USB_DATA_PACKET_HEAD+(n & 0x1F);
        }
        memcpy(&SendBuf[startIndex+1u],&apMsg[n*(FramSize-1)],FramSize-1);
        
        if( PACKET_OPERATE_SUCCESS != UsbSendSingleFrame(SendBuf,FramSize + startIndex))
        {        
            ret = PACKET_OPERATE_FAIL;
            break;
        }
        else
        {
           // do nothing
        }
    }
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  SendCmdAck
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t UsbSendCmdAck(uint8_t *apMsg,uint16_t aLen)
{
    uint8_t SendBuf[USB_TX_FRAM_SIZE+1u];

    uint8_t ret = PACKET_OPERATE_SUCCESS;

    if(aLen > USB_TX_FRAM_SIZE)
    {
       ret = PACKET_OPERATE_FAIL;
    }
    else
    {
        memcpy(&SendBuf[1],apMsg,aLen);
        SendBuf[0]=USB_DATA_PACKET_END+USB_DATA_PACKET_HEAD;
        USBD_CUSTOM_HID_SendAckMsg(SendBuf,aLen+1);
        
    }
    return ret;
}
/***********************************************END**********************************************/
