/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : PowerCtrlDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef PWR_CTRL_DRV_C
#define PWR_CTRL_DRV_C
#endif

/* include files */
#include "PowerCtrlDrv.h"

/* static variable definition */
APP_TIMER_DEF(g_motor_timeout_timer_id);                                  /**< 10 ms timer. */
/* static function declaration */

/* static function definition */
static void Timer_motor_timeout_handler(void * p_context)
{
// #ifndef SHORT_AIR_GUN_BOARD 
//         FastPwmStop(1,false);
// #else
//         FastPwmStop(1,true);
// #endif
    PvElecMagnetPwrCtrl(0);
}
/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  BatOutVolSample
* Description   :  
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : battery voltage * 1000
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */
//static uint32_t GetBatVolSample(void) //mv
//{
//    uint32_t BatVoltage = 0u;

//    BatVoltage = GetAdcSample(PWR_CTRL_ADC_CHNL_BAT_SAMP);
//#if defined( MIRAGE2B_BOARD)
//    BatVoltage = (BatVoltage * 3600ul ) /16384u; //14bit adc
//#else
//    BatVoltage = (BatVoltage * (3600.0 * 2)) /16384u; //14bit adc
//#endif
//    return BatVoltage;
//}

//void PwrHoldCtrl(uint8_t aEnable)
//{
//    if(aEnable == 1u)  /*enable*/
//    {
//#if defined( MIRAGE2B_BOARD)
//        nrf_gpio_pin_clear(PWR_CTRL_PIN_HOLD_CTRL);
//#else
//        nrf_gpio_pin_set(PWR_CTRL_PIN_HOLD_CTRL);
//#endif
//    }
//    else
//    {
//#if defined( MIRAGE2B_BOARD)
//        nrf_gpio_pin_set(PWR_CTRL_PIN_HOLD_CTRL);
//#else
//        nrf_gpio_pin_clear(PWR_CTRL_PIN_HOLD_CTRL);
//#endif
//    }
//}
void ImuPwrCtrl(uint8_t aEnable)
{
//    if(aEnable == 1u)  /*enable*/
//    {
//        nrf_gpio_pin_clear(PWR_CTRL_PIN_IMU_POWER);
//        nrf_gpio_pin_set(SYS_CFG_PIN_IMU_CS);  //high
//    }
//    else
//    {
//        nrf_gpio_pin_set(PWR_CTRL_PIN_IMU_POWER);
//        nrf_gpio_pin_clear(SYS_CFG_PIN_IMU_CS);  //low
//    }
}

void TpPwrCtrl(uint8_t aEnable)
{
    #if SYS_CFG_USE_TP_SWITCH == FUN_EN
        if(aEnable == 1u)  /*enable*/
        {
            nrf_gpio_pin_clear(PWR_CTRL_PIN_TP_POWER);
        }
        else
        {
            nrf_gpio_pin_set(PWR_CTRL_PIN_TP_POWER);
        }
    #endif
}

void HallPwrCtrl(uint8_t aEnable)
{
//   if(aEnable == 1u)  /*enable*/
//    {
//        nrf_gpio_pin_clear(PWR_CTRL_PIN_HALL_POWER);
//    }
//    else
//    {
//        nrf_gpio_pin_set(PWR_CTRL_PIN_HALL_POWER);
//    }
}

void PvElecMagnetPwrCtrl(uint8_t aEnable)
{
//    if(aEnable == 1u)  /*enable*/
//    {
//        nrf_gpio_pin_set(PWR_CTRL_PIN_ELEC_MAGNET_POWER);
//    }
//    else
//    {
//        nrf_gpio_pin_clear(PWR_CTRL_PIN_ELEC_MAGNET_POWER);
//    }
}


void AINKeyPwrCtrl(uint8_t aEnable)
{
//   if(aEnable == 1u)  /*enable*/
//    {
//        nrf_gpio_pin_clear(PWR_CTRL_PIN_AIN_KEY_POWER);
//    }
//    else
//    {
//        nrf_gpio_pin_set(PWR_CTRL_PIN_AIN_KEY_POWER);
//    }
}

void BlobPwmPwrCtrl(uint8_t aEnable,uint16_t preiod,uint16_t duty_R,uint16_t duty_G,uint16_t duty_B)
{
#if 0
    FastPwmPinCfg_str  blobPwmCfg_t;
    if(aEnable == 1u)  /*enable*/
    {
        blobPwmCfg_t.duty[0] = ((duty_R) * preiod)/100;
        blobPwmCfg_t.duty[1] = ((duty_G) * preiod)/100;
        blobPwmCfg_t.duty[2] = ((duty_B) * preiod)/100;
        blobPwmCfg_t.duty[3] = 50;
        blobPwmCfg_t.out_pins[0] = PWR_CTRL_PIN_BLOB_CTRL_R;
        blobPwmCfg_t.out_pins[1] = PWR_CTRL_PIN_BLOB_CTRL_G;
        blobPwmCfg_t.out_pins[2] = PWR_CTRL_PIN_BLOB_CTRL_B;
        blobPwmCfg_t.out_pins[3] = NRFX_PWM_PIN_NOT_USED;
        blobPwmCfg_t.active_high[0] = 0;
        blobPwmCfg_t.active_high[1] = 0;
        blobPwmCfg_t.active_high[2] = 0;
        blobPwmCfg_t.active_high[3] = 0;
        FastPwmTimStart(0,preiod,blobPwmCfg_t);
    }
    else
    {
        FastPwmStop(0,true);
    }
#endif
}

void ElectroMagnetSwitchCtrl(uint8_t aEnable, uint32_t timeout_ms)
{
    ret_code_t err_code;
    if (true == aEnable)
    {
        PvElecMagnetPwrCtrl(1);
        if (timeout_ms != 0)
        {
            err_code = app_timer_create(&g_motor_timeout_timer_id, APP_TIMER_MODE_SINGLE_SHOT, Timer_motor_timeout_handler);
            err_code = app_timer_start(g_motor_timeout_timer_id, APP_TIMER_TICKS(timeout_ms), NULL);
            APP_ERROR_CHECK(err_code);
        }
    }
    else
    {
        app_timer_stop(g_motor_timeout_timer_id);
        PvElecMagnetPwrCtrl(0);
    }
}

void MotorPwmPwrCtrl(uint8_t aEnable,uint16_t preiod,uint16_t duty_m,uint32_t timeout_ms)
{
#if 0
    ret_code_t err_code;
    FastPwmPinCfg_str  MotorPwmCfg_t;
    if(aEnable == 1u)  /*enable*/
    {
        if(duty_m >= 100)
        {
            duty_m = 99;
        }
        MotorPwmCfg_t.duty[0] = ((100 - duty_m) * preiod)/100;
        MotorPwmCfg_t.duty[1] = 0;
        MotorPwmCfg_t.duty[2] = 0;
        MotorPwmCfg_t.duty[3] = 0;
        MotorPwmCfg_t.out_pins[0] = PWR_CTRL_PIN_MOTOR_POWER;
        MotorPwmCfg_t.out_pins[1] = NRFX_PWM_PIN_NOT_USED;
        MotorPwmCfg_t.out_pins[2] = NRFX_PWM_PIN_NOT_USED;
        MotorPwmCfg_t.out_pins[3] = NRFX_PWM_PIN_NOT_USED;
#ifndef SHORT_AIR_GUN_BOARD 
        MotorPwmCfg_t.active_high[0] = NRFX_PWM_PIN_INVERTED;
        MotorPwmCfg_t.active_high[1] = NRFX_PWM_PIN_INVERTED;
        MotorPwmCfg_t.active_high[2] = NRFX_PWM_PIN_INVERTED;
        MotorPwmCfg_t.active_high[3] = NRFX_PWM_PIN_INVERTED;
#else
        MotorPwmCfg_t.active_high[0] = 0;
        MotorPwmCfg_t.active_high[1] = 0;
        MotorPwmCfg_t.active_high[2] = 0;
        MotorPwmCfg_t.active_high[3] = 0;
#endif
        FastPwmTimStart(1,preiod,MotorPwmCfg_t);
        if(timeout_ms != 0)
        {
            err_code = app_timer_create(&g_motor_timeout_timer_id,APP_TIMER_MODE_SINGLE_SHOT,Timer_motor_timeout_handler);
            err_code = app_timer_start(g_motor_timeout_timer_id, APP_TIMER_TICKS(timeout_ms), NULL);
            APP_ERROR_CHECK(err_code);
        }
        else
        {
            //do nothing
        }
    }
    else
    {
        app_timer_stop(g_motor_timeout_timer_id);
#ifndef SHORT_AIR_GUN_BOARD 
        FastPwmStop(1,false);
#else
        FastPwmStop(1,true);
#endif
        
    }
#endif
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  FpgaPwrCtrl
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void BatCharge(uint8_t aEnable)
{
//    if(aEnable == 1u)  /*enable*/
//    {
//        nrf_gpio_pin_set(PWR_CTRL_PIN_CHG_EN);
//    }
//    else
//    {
//        nrf_gpio_pin_clear(PWR_CTRL_PIN_CHG_EN);
//    }
}

//uint32_t BatVolSample_filter(void) //return mv
//{
//    static uint32_t BatVolSum = 0;
//    static uint8_t   sampleCnt = 0;
//    static uint32_t lastBatVol = 0;
//    
//    BatVolSum += GetBatVolSample();
//    sampleCnt++;
//    if(sampleCnt >= PWM_CTRL_BAT_SAMP_FILTER_TIMES)
//    {
//        BatVolSum = BatVolSum / sampleCnt;
//        lastBatVol = ((float)lastBatVol)*(1.0f-BATT_VOLT_FILTER_NEW_VALUE_WEIGHT) + ((float)BatVolSum) * BATT_VOLT_FILTER_NEW_VALUE_WEIGHT;
//        sampleCnt = 0;
//        BatVolSum = 0;
//    }
//    if(lastBatVol == 0u)
//    {
//#ifndef SHORT_AIR_GUN_BOARD 
//        lastBatVol = DRY_BATT_PERCENT_100_VOLT;
//#else
//        lastBatVol = Li_BATT_PERCENT_100_VOLT;
//#endif
//    }
//    return lastBatVol;
//}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  GetBatElectric
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
//uint8_t GetBatElectric(uint8_t isLiBat,uint32_t volt_mv)
//{
//     static  uint32_t m_last_batt_percent = 100;
//     uint32_t before_remap_percent  = 0;
//     uint32_t percent = 0;
//    
//    if(!isLiBat)
//    {
//        volt_mv = volt_mv > DRY_BATT_PERCENT_0_VOLT ? volt_mv : DRY_BATT_PERCENT_0_VOLT;
//        volt_mv = volt_mv < DRY_BATT_PERCENT_100_VOLT ? volt_mv : DRY_BATT_PERCENT_100_VOLT;
//        before_remap_percent = (volt_mv - DRY_BATT_PERCENT_0_VOLT) * (BATT_CONVERTER_COUNTS-1) / (DRY_BATT_PERCENT_100_VOLT - DRY_BATT_PERCENT_0_VOLT);
//        percent = dry_batt_converter[before_remap_percent];
//        
//    }
//    else
//    {
//        volt_mv = volt_mv > Li_BATT_PERCENT_0_VOLT ? volt_mv : Li_BATT_PERCENT_0_VOLT;
//        volt_mv = volt_mv < Li_BATT_PERCENT_100_VOLT ? volt_mv : Li_BATT_PERCENT_100_VOLT;
////        before_remap_percent = (volt_mv - Li_BATT_PERCENT_0_VOLT) * (Li_BATT_CONVERTER_COUNTS-1) / (Li_BATT_PERCENT_100_VOLT - Li_BATT_PERCENT_0_VOLT);
//		percent = ((volt_mv - Li_BATT_PERCENT_0_VOLT)*100) / (Li_BATT_CENTI_VOLT) ;
////        percent = Li_batt_converter[before_remap_percent];
//    }
//#if defined( MIRAGE2B_BOARD)
//    if(percent > m_last_batt_percent)
//    {
//        return m_last_batt_percent;
//    }
//    else
//    {
//        m_last_batt_percent = percent;
//    }
//#endif
//    return percent;
//}
/* BEGIN_FUNCTION_HDR
*********************************************************************************************************
* Function Name : getChargeStatus
* Description   : read the statrus of charge chip
*
* Inputs        : 
*
* Outputs       : charge chip status 
*
* Limitations   : 
*                  
*
*********************************************************************************************************
END_FUNCTION_HDR */
uint8_t getChargeStatus(void)
{
//     if(    (nrf_gpio_pin_read(PWR_CTRL_PIN_CHG_PGOOD) == 0x00)
//         && (nrf_gpio_pin_read(PWR_CTRL_PIN_CHG_DET) == 0x00)
//       )
//     {
//         return PWR_OPWRATE_CHARGING;
//     }
//     else if(    (nrf_gpio_pin_read(PWR_CTRL_PIN_CHG_PGOOD) == 0x00)
//         && (nrf_gpio_pin_read(PWR_CTRL_PIN_CHG_DET) == 0x01)
//       )
//     {
//         return PWR_OPWRATE_CHG_DONE;
//     }
//     else
//     {
//         return PWR_OPWRATE_CHG_ERR;
//     }
}
/**@brief Function for putting the chip into sleep mode.
 *
 * @note This function will not return.
 */
static void Sleep_sdh_state_observer(nrf_sdh_state_evt_t state, void * p_context)
{
    if (state == NRF_SDH_EVT_STATE_DISABLED)
    {
        // Softdevice was disabled before going into reset. Inform bootloader to skip CRC on next boot.
        nrf_power_gpregret2_set(BOOTLOADER_DFU_SKIP_CRC);

        //Go to system off.
       // NRF_LOG_INFO("go to  system off");
        nrf_pwr_mgmt_shutdown(NRF_PWR_MGMT_SHUTDOWN_GOTO_SYSOFF);
    }
    else if (state == NRF_SDH_EVT_DISABLE_REQUEST)
    {
        NRF_LOG_INFO("go to  SoftDevice disable  fail");
    }
        
}

/* nrf_sdh state observer. */
NRF_SDH_STATE_OBSERVER(m_Sleep_state_obs, 0) =
{
    .handler = Sleep_sdh_state_observer,
};