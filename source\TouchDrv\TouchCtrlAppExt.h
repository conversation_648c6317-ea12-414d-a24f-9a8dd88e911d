/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : UartRWAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/07/31    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef TOUCH_CTRL_APP_EXT_H
#define TOUCH_CTRL_APP_EXT_H

/*Include files*/
#include "XimStdint.h"

/*declaration range definition*/
#ifdef  TOUCH_CTRL_DRV_C
#define TOUCH_CTRL_DRV_APP_EXT
#else 
#define TOUCH_CTRL_DRV_APP_EXT extern
#endif

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */

/* data type definiton  */
#pragma pack(1)
typedef enum {
    TP_IDLE_EVT = 0,
    TP_DOWN_EVT = 1,
    TP_MOVE_EVT = 2,
    TP_UP_EVT    =3
}TP_event_t;

/**@brief Touchpad output data  */
typedef struct {
    uint16_t        x;
    uint16_t        reservex;
    TP_event_t      atcive_event; 
    uint16_t        y;
    uint16_t        reservey;
}touchpad_motion_t;


/**@brief Packet types */
typedef enum {
    packet_type_touchpad = 1,

} packet_type_t;


//eg :09 0x03  down   0x09 04  move 0x00 09 up
typedef struct {
    uint16_t Point_count_change_event   : 1 ;
    uint16_t down_event                 : 1 ;
    uint16_t move_event                 : 1 ;//------------
    uint16_t up_event                   : 1 ;
    uint16_t large_palm_report          : 1 ;
    uint16_t large_palm_reject          : 1 ;
    uint16_t reserved1                  : 2 ;
    uint16_t width_changed_event        : 1 ;
    uint16_t reject_event               : 1 ;
    uint16_t point_dected_event         : 1 ;//------
    uint16_t reserved2                  : 2 ;
    uint16_t debug_event                : 1 ;
    uint16_t button_event               : 1 ;
} point_info_status_t ;// ZINITIX_POINT_STATUS_REG

typedef struct  {
    point_info_status_t                 status; ///--------
    uint8_t                             finger_cnt;
    uint8_t                             time_stamp;
    uint16_t                            x;
    uint16_t                            y;
    uint8_t                             width;
    uint8_t                             sub_status;
}zinitix_point_info_t;

typedef union {
    touchpad_motion_t tp_data;
} packet_data_t;

/**@brief Generic packet representation */
typedef struct {
    packet_type_t type; /**< Type of packet */
    packet_data_t data; /**< Packet data */
} packet_t;
#pragma pack()

/* variable definition */
 
/* function declaration */
TOUCH_CTRL_DRV_APP_EXT void xim_touchpad_drv_sw_reset(void);
TOUCH_CTRL_DRV_APP_EXT void xim_touchpad_drv_sleep(void);
TOUCH_CTRL_DRV_APP_EXT void xim_touchpad_drv_wakeup(void);
TOUCH_CTRL_DRV_APP_EXT bool xim_touchpad_drv_read_point_info(touchpad_motion_t *pTp_data);
TOUCH_CTRL_DRV_APP_EXT bool xim_touchpad_drv_read_info(uint16_t *pTpFw_ver);
TOUCH_CTRL_DRV_APP_EXT void xim_touchpad_drv_pin_reset(void);
TOUCH_CTRL_DRV_APP_EXT bool xim_touchpad_drv_init(uint16_t *pTpFw_ver);

#endif    /* end of header file */
