/*
 * ________________________________________________________________________________________________________
 * Copyright (c) 2017 InvenSense Inc. All rights reserved.
 *
 * This software, related documentation and any modifications thereto (collectively “Software”) is subject
 * to InvenSense and its licensors' intellectual property rights under U.S. and international copyright
 * and other intellectual property rights laws.
 *
 * InvenSense and its licensors retain all intellectual property and proprietary rights in and to the Software
 * and any use, reproduction, disclosure or distribution of the Software without an express license agreement
 * from InvenSense is strictly prohibited.
 *
 * EXCEPT AS OTHERWISE PROVIDED IN A LICENSE AGREEMENT BETWEEN THE PARTIES, THE SOFTWARE IS
 * PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED
 * TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 * EXCEPT AS OTHERWISE PROVIDED IN A LICENSE AGREEMENT BETWEEN THE PARTIES, IN NO EVENT SHALL
 * INVENSENSE BE LIABLE FOR ANY DIRECT, SPECIAL, INDIRECT, INCIDENTAL, OR CONSEQUENTIAL DAMAGES, OR ANY
 * DAMAGES WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT,
 * NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR PERFORMANCE
 * OF THE SOFTWARE.
 * ________________________________________________________________________________________________________
 */

#ifndef _INV_ICM426XX_DEFS_H_
#define _INV_ICM426XX_DEFS_H_

#ifdef __cplusplus
extern "C" {
#endif

/** @file Icm426xxDefs.h
 * File exposing the device register map
 */

#include <stdint.h>
#define ICM40608

/* List whoami values for all icm426xx variants*/
#define ICM40608_WHOAMI 0x39
#define ICM42600_WHOAMI 0x40
#define ICM42602_WHOAMI 0x41
#define ICM42605_WHOAMI 0x42
#define ICM42622_WHOAMI 0x46
#define ICM42631_WHOAMI 0x5C
#define ICM42633_WHOAMI 0x5B
#define ICM42686_WHOAMI 0x44
#define ICM42688_WHOAMI 0x47
#define ICM42608_WHOAMI 0x48
#define ICM20602_WHOAMI 0x12

/* Define whoami value for the targeted product and make sure the target is valid */
#if defined(ICM42600)
#define ICM_WHOAMI ICM42600_WHOAMI
#elif defined(ICM42602)
#define ICM_WHOAMI ICM42602_WHOAMI
#elif defined(ICM42605)
#define ICM_WHOAMI ICM42605_WHOAMI
#elif defined(ICM42622)
#define ICM_WHOAMI ICM42622_WHOAMI
#elif defined(ICM42631)
#define ICM_WHOAMI ICM42631_WHOAMI
#elif defined(ICM42633)
#define ICM_WHOAMI ICM42633_WHOAMI
#elif defined(ICM42686)
#define ICM_WHOAMI ICM42686_WHOAMI
#elif defined(ICM42688)
#define ICM_WHOAMI ICM42688_WHOAMI
#elif defined(ICM42608)
#define ICM_WHOAMI ICM42608_WHOAMI
#elif defined(ICM40608)
#define ICM_WHOAMI ICM40608_WHOAMI
#elif defined(ICM20602)
#define ICM_WHOAMI ICM20602_WHOAMI
#else
#error "Please define which ICM variant is targeted. Possible values: " \
														"ICM42600, " \
														"ICM42602, " \
														"ICM42605, " \
														"ICM42622, " \
														"ICM42631, " \
														"ICM42633, " \
														"ICM42686, " \
														"ICM42688, " \
														"ICM42608, " \
														"ICM40608" 
#endif

/** @brief ICM family definition
 *  Possible values are ICM_FAMILY_CPLUS or ICM_FAMILY_BPLUS 
 */
#if (defined(ICM42631) || defined(ICM42633))
	#define ICM_FAMILY_CPLUS
#else
	#define ICM_FAMILY_BPLUS
#endif
 

/* ----------------------------------------------------------------------------
 * Device Register map
 *
 * Next macros defines address for all icm426xx registers as listed by device 
 * datasheet.
 * Macros name is MPUREG_<REGISTER_NAME> with REGISTER_NAME being the name of
 * the corresponding register in datasheet.
 * Note that macro name is MPUREG_<REGISTER_NAME>_Bx with x being the bank 
 * number for registers that are in bank 1 and 2 (suffix is ommitted for 
 * bank0 registers)
 * ---------------------------------------------------------------------------- */

/* Bank 0 */
#define MPUREG_DEVICE_CONFIG      0x11
#define MPUREG_CHIP_CONFIG        MPUREG_DEVICE_CONFIG // Retro-compatibility
#define MPUREG_DRIVE_CONFIG       0x13
#define MPUREG_INT_CONFIG         0x14
#define MPUREG_FIFO_CONFIG        0x16
#define MPUREG_TEMP_DATA0_UI      0x1D
#define MPUREG_ACCEL_DATA_X0_UI   0x1F
#define MPUREG_GYRO_DATA_X0_UI    0x25
#define MPUREG_TMST_FSYNCH        0x2B
#define MPUREG_TMST_FSYNC1        MPUREG_TMST_FSYNCH // Retro-compatibility
#define MPUREG_INT_STATUS         0x2D
#define MPUREG_FIFO_COUNTH        0x2E
#define MPUREG_FIFO_BYTE_COUNT1   MPUREG_FIFO_COUNTH // Retro-compatibility
#define MPUREG_FIFO_COUNTL        0x2F
#define MPUREG_FIFO_BYTE_COUNT2   MPUREG_FIFO_COUNTL // Retro-compatibility
#define MPUREG_FIFO_DATA          0x30
#define MPUREG_APEX_DATA0         0x31
#define MPUREG_APEX_DATA1         0x32
#define MPUREG_APEX_DATA2         0x33
#define MPUREG_APEX_DATA3         0x34
#define MPUREG_APEX_DATA4         0x35
#define MPUREG_APEX_DATA5         0x36
#define MPUREG_INT_STATUS2        0x37
#define MPUREG_INT_STATUS3        0x38
#define MPUREG_SIGNAL_PATH_RESET  0x4B
#define MPUREG_INTF_CONFIG0       0x4C
#define MPUREG_INTF_CONFIG1       0x4D
#define MPUREG_PWR_MGMT_0         0x4E
#define MPUREG_GYRO_CONFIG0       0x4F
#define MPUREG_ACCEL_CONFIG0      0x50
#define MPUREG_GYRO_CONFIG1       0x51
#define MPUREG_ACCEL_GYRO_CONFIG0 0x52
#define MPUREG_ACCEL_CONFIG1      0x53
#define MPUREG_TMST_CONFIG        0x54
#define MPUREG_APEX_CONFIG0       0x56
#define MPUREG_SMD_CONFIG         0x57
#define MPUREG_FIFO_CONFIG1       0x5F
#define MPUREG_FIFO_CONFIG2       0x60
#define MPUREG_FSYNC_CONFIG       0x62
#define MPUREG_INT_CONFIG0        0x63
#define MPUREG_INT_CONFIG1        0x64
#define MPUREG_INT_SOURCE0        0x65
#define MPUREG_INT_SOURCE1        0x66
#define MPUREG_INT_SOURCE2        0x67
#define MPUREG_INT_SOURCE3        0x68
#define MPUREG_INT_SOURCE4        0x69
#define MPUREG_INT_SOURCE5        0x6A
#define MPUREG_FIFO_LOST_PKT0     0x6C
#define MPUREG_SELF_TEST_CONFIG   0x70
#define MPUREG_WHO_AM_I           0x75
#define MPUREG_SCAN0              0x71
#define MPUREG_MEM_BANK_SEL       0x72
#define MPUREG_MEM_START_ADDR     0x73
#define MPUREG_MEM_R_W            0x74
#define MPUREG_REG_BANK_SEL       0x76

/* Bank 1 */
#define MPUREG_SENSOR_CONFIG1_B1      0x04
#define MPUREG_GYRO_CONFIG_STATIC2_B1 0x0B
#define MPUREG_GYRO_CONFIG_STATIC3_B1 0x0C
#define MPUREG_GYRO_CONFIG_STATIC4_B1 0x0D
#define MPUREG_GYRO_CONFIG_STATIC5_B1 0x0E
#define MPUREG_XG_ST_DATA_B1          0x5F
#define MPUREG_YG_ST_DATA_B1          0x60
#define MPUREG_ZG_ST_DATA_B1          0x61
#define MPUREG_TMST_VAL0_B1           0x62
#define MPUREG_INTF_CONFIG4_B1        0x7A
#define MPUREG_INTF_CONFIG5_B1        0x7B
#define MPUREG_INTF_CONFIG6_B1        0x7C
#define MPUREG_INTF_CONFIG3_B1        0x79

/* Bank 2 */
#define MPUREG_ACCEL_CONFIG_STATIC2_B2 0x03
#define MPUREG_ACCEL_CONFIG_STATIC3_B2 0x04
#define MPUREG_ACCEL_CONFIG_STATIC4_B2 0x05
#define MPUREG_ACCEL_CONFIG_STATIC0_B2 0x39
#define MPUREG_XA_ST_DATA_B2           0x3B
#define MPUREG_YA_ST_DATA_B2           0x3C
#define MPUREG_ZA_ST_DATA_B2           0x3D
/* Only accessible from AUX1 */
#define MPUREG_OIS1_CONFIG1_B2         0x44
#define MPUREG_OIS1_CONFIG2_B2         0x45
#define MPUREG_OIS1_CONFIG3_B2         0x46
#define MPUREG_ACCEL_DATA_X0_OIS1_B2   0x49
#define MPUREG_GYRO_DATA_X0_OIS1_B2    0x4F
#define MPUREG_INT_STATUS_OIS1_B2      0x57
/* End of Only accessible from AUX1 */
/* Only accessible from AUX2 */
#define MPUREG_OIS2_CONFIG1_B2         0x59
#define MPUREG_OIS2_CONFIG2_B2         0x5A
#define MPUREG_OIS2_CONFIG3_B2         0x5B
#define MPUREG_ACCEL_DATA_X0_OIS2_B2   0x5E
#define MPUREG_GYRO_DATA_X0_OIS2_B2    0x64
#define MPUREG_INT_STATUS_OIS2_B2      0x6C
/* End of Only accessible from AUX2 */
#define MPUREG_TMD4_B2                 0x70
#define MPUREG_TMD5_B2                 0x71
#define MPUREG_TMD6_B2                 0x72
#define MPUREG_TMD7_B2                 0x73

/* Bank 3 */
#define MPUREG_PU_PD_CONFIG1_B3   0x06
#define MPUREG_PU_PD_CONFIG2_B3   0x0E

/* Bank 4 */
#define MPUREG_FDR_CONFIG_B4      0x09
#define MPUREG_APEX_CONFIG1_B4    0x40
#define MPUREG_APEX_CONFIG2_B4    0x41
#define MPUREG_APEX_CONFIG3_B4    0x42
#define MPUREG_APEX_CONFIG4_B4    0x43
#define MPUREG_APEX_CONFIG5_B4    0x44
#define MPUREG_APEX_CONFIG6_B4    0x45
#define MPUREG_APEX_CONFIG7_B4    0x46
#define MPUREG_APEX_CONFIG8_B4    0x47
#define MPUREG_APEX_CONFIG9_B4    0x48
#define MPUREG_APEX_CONFIG10_B4   0x49
#define MPUREG_ACCEL_WOM_X_THR_B4 0x4A
#define MPUREG_ACCEL_WOM_Y_THR_B4 0x4B
#define MPUREG_ACCEL_WOM_Z_THR_B4 0x4C
#define MPUREG_INT_SOURCE6_B4     0x4D
#define MPUREG_INT_SOURCE7_B4     0x4E
#define MPUREG_INT_SOURCE8_B4     0x4F
#define MPUREG_INT_SOURCE9_B4     0x50
#define MPUREG_INT_SOURCE10_B4    0x51
#define MPUREG_OFFSET_USER_0_B4   0x77
#define MPUREG_OFFSET_USER_1_B4   0x78
#define MPUREG_OFFSET_USER_2_B4   0x79
#define MPUREG_OFFSET_USER_3_B4   0x7A
#define MPUREG_OFFSET_USER_4_B4   0x7B
#define MPUREG_OFFSET_USER_5_B4   0x7C
#define MPUREG_OFFSET_USER_6_B4   0x7D
#define MPUREG_OFFSET_USER_7_B4   0x7E
#define MPUREG_OFFSET_USER_8_B4   0x7F

/* ----------------------------------------------------------------------------
 * Device features
 *
 * Next macros define some of the device features such as FIFO, sensor data
 * size or whoami value.
 * ---------------------------------------------------------------------------- */

#define ACCEL_DATA_SIZE               6
#define GYRO_DATA_SIZE                6
#define TEMP_DATA_SIZE                2

#define FIFO_HEADER_SIZE              1
#define FIFO_ACCEL_DATA_SIZE          ACCEL_DATA_SIZE
#define FIFO_GYRO_DATA_SIZE           GYRO_DATA_SIZE
#define FIFO_TEMP_DATA_SIZE           1
#define FIFO_TS_FSYNC_SIZE            2
#define FIFO_TEMP_HIGH_RES_SIZE       1
#define FIFO_ACCEL_GYRO_HIGH_RES_SIZE 3

#define FIFO_16BYTES_PACKET_SIZE      (FIFO_HEADER_SIZE + FIFO_ACCEL_DATA_SIZE + FIFO_GYRO_DATA_SIZE + FIFO_TEMP_DATA_SIZE + FIFO_TS_FSYNC_SIZE)
#define FIFO_20BYTES_PACKET_SIZE      (FIFO_HEADER_SIZE + FIFO_ACCEL_DATA_SIZE + FIFO_GYRO_DATA_SIZE + FIFO_TEMP_DATA_SIZE + FIFO_TS_FSYNC_SIZE +\
                                       FIFO_TEMP_HIGH_RES_SIZE + FIFO_ACCEL_GYRO_HIGH_RES_SIZE)

#define FIFO_HEADER_ODR_ACCEL         0x01
#define FIFO_HEADER_ODR_GYRO          0x02
#define FIFO_HEADER_FSYNC             0x04
#define FIFO_HEADER_TMST              0x08
#define FIFO_HEADER_HEADER_20         0x10
#define FIFO_HEADER_GYRO              0x20
#define FIFO_HEADER_ACC               0x40
#define FIFO_HEADER_MSG               0x80


#define INVALID_VALUE_FIFO              ((int16_t)0x8000)
#define INVALID_VALUE_FIFO_1B           ((int8_t)0x80)

/** Describe the content of the FIFO header */
typedef union
{
	unsigned char Byte;
	struct
	{
		unsigned char gyro_odr_different : 1;
		unsigned char accel_odr_different : 1;
		unsigned char fsync_bit : 1;
		unsigned char timestamp_bit : 1;
		unsigned char twentybits_bit : 1;
		unsigned char gyro_bit : 1;
		unsigned char accel_bit : 1;
		unsigned char msg_bit : 1;
	}bits;
} fifo_header_t;

#define I3C_IBI_PAYLOAD_ALL           0xFF
#define I3C_IBI_PAYLOAD_TIMEC         0x80
#define I3C_IBI_PAYLOAD_CAT_MISC      0x40
#define I3C_IBI_PAYLOAD_CAT_ERROR     0x20
#define I3C_IBI_PAYLOAD_CAT_APEX2     0x10
#define I3C_IBI_PAYLOAD_CAT_APEX1     0x08
#define I3C_IBI_PAYLOAD_CAT_OIS1_DRDY 0x04
#define I3C_IBI_PAYLOAD_CAT_FIFO      0x02
#define I3C_IBI_PAYLOAD_CAT_UI_DRDY   0x01


/* ----------------------------------------------------------------------------
 * Device registers description
 *
 * Next section defines some of the registers bitfield and declare corresponding
 * accessors.
 * Note that descriptors and accessors are not provided for all the registers 
 * but only for the most useful ones.
 * For all details on registers and bitfields functionalities please refer to 
 * the device datasheet.
 * ---------------------------------------------------------------------------- */


/* ---------------------------------------------------------------------------
 * register bank 0 
 * ---------------------------------------------------------------------------- */

/*
 * MPUREG_DEVICE_CONFIG
 * Register Name : DEVICE_CONFIG
 */

/* SPI_MODE */
#define BIT_DEVICE_CONFIG_SPI_MODE_POS     4
#define BIT_DEVICE_CONFIG_SPI_MODE_MASK    (0x1 << BIT_DEVICE_CONFIG_SPI_MODE_POS)
#define BIT_CHIP_CONFIG_SPI_MODE_MASK      BIT_DEVICE_CONFIG_SPI_MODE_MASK			//retro-compatibility
#define BIT_CHIP_CONFIG_SPI_MODE_POS       BIT_DEVICE_CONFIG_SPI_MODE_POS			//retro-compatibility

typedef enum
{
	ICM426XX_DEVICE_CONFIG_SPI_MODE_1_2 = (0x1 << BIT_DEVICE_CONFIG_SPI_MODE_POS),
	ICM426XX_DEVICE_CONFIG_SPI_MODE_0_3 = (0x0 << BIT_DEVICE_CONFIG_SPI_MODE_POS),
} ICM426XX_DEVICE_CONFIG_SPI_MODE_t;

typedef enum
{
	ICM426XX_CHIP_CONFIG_SPI_MODE_1_2 = (0x1 << BIT_CHIP_CONFIG_SPI_MODE_POS),
	ICM426XX_CHIP_CONFIG_SPI_MODE_0_3 = (0x0 << BIT_CHIP_CONFIG_SPI_MODE_POS),
} ICM426XX_CHIP_CONFIG_SPI_MODE_t;													//retro-compatibility


/* SOFT_RESET */
#define BIT_DEVICE_CONFIG_RESET_POS     0
#define BIT_DEVICE_CONFIG_RESET_MASK    0x01
#define BIT_CHIP_CONFIG_RESET_MASK      BIT_DEVICE_CONFIG_RESET_MASK				//retro-compatibility
#define BIT_CHIP_CONFIG_RESET_POS       BIT_DEVICE_CONFIG_RESET_POS					//retro-compatibility

typedef enum
{
	ICM426XX_DEVICE_CONFIG_RESET_EN   = 0x01,
	ICM426XX_DEVICE_CONFIG_RESET_NONE = 0x00,
} ICM426XX_DEVICE_CONFIG_RESET_t;

typedef enum
{
	ICM426XX_CHIP_CONFIG_RESET_EN   = 0x01,
	ICM426XX_CHIP_CONFIG_RESET_NONE = 0x00,
} ICM426XX_CHIP_CONFIG_RESET_t;														//retro-compatibility

/*
 * MPUREG_INT_CONFIG
 * Register Name: INT_CONFIG
 */

/* INT2_DRIVE_CIRCUIT */
#define BIT_INT_CONFIG_INT2_DRIVE_CIRCUIT_POS        4
#define BIT_INT_CONFIG_INT2_DRIVE_CIRCUIT_MASK   (0x01 << BIT_INT_CONFIG_INT2_DRIVE_CIRCUIT_POS)
 
typedef enum
{
	ICM426XX_INT_CONFIG_INT2_DRIVE_CIRCUIT_PP = (0x01 << BIT_INT_CONFIG_INT2_DRIVE_CIRCUIT_POS),
	ICM426XX_INT_CONFIG_INT2_DRIVE_CIRCUIT_OD = (0x00 << BIT_INT_CONFIG_INT2_DRIVE_CIRCUIT_POS),
} ICM426XX_INT_CONFIG_INT2_DRIVE_CIRCUIT_t;

/* INT2_POLARITY */
#define BIT_INT_CONFIG_INT2_POLARITY_POS        3
#define BIT_INT_CONFIG_INT2_POLARITY_MASK   (0x01 << BIT_INT_CONFIG_INT2_POLARITY_POS)

typedef enum
{
	ICM426XX_INT_CONFIG_INT2_POLARITY_HIGH = (0x01 << BIT_INT_CONFIG_INT2_POLARITY_POS),
	ICM426XX_INT_CONFIG_INT2_POLARITY_LOW  = (0x00 << BIT_INT_CONFIG_INT2_POLARITY_POS),
} ICM426XX_INT_CONFIG_INT2_POLARITY_t;

/* INT1_DRIVE_CIRCUIT */
#define BIT_INT_CONFIG_INT1_DRIVE_CIRCUIT_POS        1
#define BIT_INT_CONFIG_INT1_DRIVE_CIRCUIT_MASK   (0x01 << BIT_INT_CONFIG_INT1_DRIVE_CIRCUIT_POS)
 
typedef enum
{
	ICM426XX_INT_CONFIG_INT1_DRIVE_CIRCUIT_PP = (0x01 << BIT_INT_CONFIG_INT1_DRIVE_CIRCUIT_POS),
	ICM426XX_INT_CONFIG_INT1_DRIVE_CIRCUIT_OD = (0x00 << BIT_INT_CONFIG_INT1_DRIVE_CIRCUIT_POS),
} ICM426XX_INT_CONFIG_INT1_DRIVE_CIRCUIT_t;

/* INT1_POLARITY */
#define BIT_INT_CONFIG_INT1_POLARITY_POS       0
#define BIT_INT_CONFIG_INT1_POLARITY_MASK   0x01

typedef enum
{
	ICM426XX_INT_CONFIG_INT1_POLARITY_HIGH = 0x01,
	ICM426XX_INT_CONFIG_INT1_POLARITY_LOW  = 0x00,
} ICM426XX_INT_CONFIG_INT1_POLARITY_t;

/*
 * MPUREG_FIFO_CONFIG
 * Register Name: FIFO_CONFIG
 */

/* FIFO_MODE */
#define BIT_FIFO_CONFIG_MODE_POS        6
#define BIT_FIFO_CONFIG_MODE_MASK   (0x03 << BIT_FIFO_CONFIG_MODE_POS)

typedef enum
{
	ICM426XX_FIFO_CONFIG_MODE_STOP_ON_FULL  = (0x02 << BIT_FIFO_CONFIG_MODE_POS),
	ICM426XX_FIFO_CONFIG_MODE_STREAM        = (0x01 << BIT_FIFO_CONFIG_MODE_POS),
	ICM426XX_FIFO_CONFIG_MODE_BYPASS        = (0x00 << BIT_FIFO_CONFIG_MODE_POS),
} ICM426XX_FIFO_CONFIG_MODE_t;

#define ICM426XX_FIFO_CONFIG_MODE_SNAPSHOT = ICM426XX_FIFO_CONFIG_MODE_STOP_ON_FULL; // For retro-compatibility
/*
 * MPUREG_INT_STATUS
 * Register Name: INT_STATUS
 */
#define BIT_INT_STATUS_UI_FSYNC   0x40
#define BIT_INT_STATUS_PLL_RDY    0x20
#define BIT_INT_STATUS_RESET_DONE 0x10
#define BIT_INT_STATUS_DRDY       0x08
#define BIT_INT_STATUS_FIFO_THS   0x04
#define BIT_INT_STATUS_FIFO_FULL  0x02
#define BIT_INT_STATUS_AGC_RDY    0x01

/*
 * MPUREG_APEX_DATA0
 * MPUREG_APEX_DATA1
 * MPUREG_APEX_DATA2
 * MPUREG_APEX_DATA3
 * Register Name: APEX_DATA0
 * Register Name: APEX_DATA1
 * Register Name: APEX_DATA2
 * Register Name: APEX_DATA3
 */

/* ACTIVITY_CLASS */
#define BIT_APEX_DATA3_ACTIVITY_CLASS_POS       0
#define BIT_APEX_DATA3_ACTIVITY_CLASS_MASK   0x03

typedef enum
{
	ICM426XX_APEX_DATA3_ACTIVITY_CLASS_OTHER = 0x0,
	ICM426XX_APEX_DATA3_ACTIVITY_CLASS_WALK  = 0x1,
	ICM426XX_APEX_DATA3_ACTIVITY_CLASS_RUN   = 0x2,
} ICM426XX_APEX_DATA3_ACTIVITY_CLASS_t;

/* DMP_IDLE */
#define BIT_APEX_DATA3_DMP_IDLE_POS        2
#define BIT_APEX_DATA3_DMP_IDLE_MASK   (0x01 << BIT_APEX_DATA3_DMP_IDLE_POS)

typedef enum
{
	ICM426XX_APEX_DATA3_DMP_IDLE_ON     = (0x01 << BIT_APEX_DATA3_DMP_IDLE_POS),
	ICM426XX_APEX_DATA3_DMP_IDLE_OFF    = (0x00 << BIT_APEX_DATA3_DMP_IDLE_POS),
} ICM426XX_APEX_DATA3_DMP_IDLE_OFF_t;

/*
 * MPUREG_APEX_DATA4
 * Register Name: APEX_DATA4
 */
 
/** TAP status flags: non-zero value - tap detected
bit0 - positive or negative edge
bit1 and 2 - axis detected : 0-X ; 1-Y ; 2-Z
bit3 and 4 - tap type : 1-single ; 2 -double
*/

/* TAP_NUM */
#define BIT_APEX_DATA4_TAP_NUM_POS        3
#define BIT_APEX_DATA4_TAP_NUM_MASK   (0x03 << BIT_APEX_DATA4_TAP_NUM_POS)
typedef enum
{
	ICM426XX_APEX_DATA4_TAP_NUM_DOUBLE = (0x02 << BIT_APEX_DATA4_TAP_NUM_POS),
	ICM426XX_APEX_DATA4_TAP_NUM_SINGLE = (0x01 << BIT_APEX_DATA4_TAP_NUM_POS),
} ICM426XX_APEX_DATA4_TAP_NUM_t;

/* TAP_AXIS */
#define BIT_APEX_DATA4_TAP_AXIS_POS        1
#define BIT_APEX_DATA4_TAP_AXIS_MASK   (0x03 << BIT_APEX_DATA4_TAP_AXIS_POS)
typedef enum
{
	ICM426XX_APEX_DATA4_TAP_AXIS_Z = (0x02 << BIT_APEX_DATA4_TAP_AXIS_POS),
	ICM426XX_APEX_DATA4_TAP_AXIS_Y = (0x01 << BIT_APEX_DATA4_TAP_AXIS_POS),
	ICM426XX_APEX_DATA4_TAP_AXIS_X = (0x00 << BIT_APEX_DATA4_TAP_AXIS_POS),
} ICM426XX_APEX_DATA4_TAP_AXIS_t;

/* TAP_DIR */
#define BIT_APEX_DATA4_TAP_DIR_POS       0
#define BIT_APEX_DATA4_TAP_DIR_MASK   0x01
typedef enum
{
	ICM426XX_APEX_DATA4_TAP_DIR_POSITIVE = (0x01 << BIT_APEX_DATA4_TAP_DIR_POS),
	ICM426XX_APEX_DATA4_TAP_DIR_NEGATIVE = (0x00 << BIT_APEX_DATA4_TAP_DIR_POS),
} ICM426XX_APEX_DATA4_TAP_DIR_t;

/*
 * MPUREG_APEX_DATA5
 * Register Name: APEX_DATA5
 */
 
/* DOUBLE_TAP_TIMING */
#define BIT_APEX_DATA5_DOUBLE_TAP_TIMING_POS      0
#define BIT_APEX_DATA5_DOUBLE_TAP_TIMING_MASK (0x3F << BIT_APEX_DATA5_DOUBLE_TAP_TIMING_POS)

/*
 * MPUREG_INT_STATUS2
 * Register Name: INT_STATUS2
 */
#define BIT_INT_STATUS2_SMD_INT        0x08
#define BIT_INT_STATUS2_WOM_Z_INT      0x04
#define BIT_INT_STATUS2_WOM_Y_INT      0x02
#define BIT_INT_STATUS2_WOM_X_INT      0x01

/*
 * MPUREG_INT_STATUS3
 * Register Name: INT_STATUS3
 */
#define BIT_INT_STATUS3_STEP_DET        0x20
#define BIT_INT_STATUS3_STEP_CNT_OVFL   0x10
#define BIT_INT_STATUS3_TILT_DET        0x08
#if defined(ICM_FAMILY_BPLUS)
#define BIT_INT_STATUS3_WAKE_DET        0x04
#define BIT_INT_STATUS3_SLEEP_DET       0x02
#elif defined(ICM_FAMILY_CPLUS)
#define BIT_INT_STATUS3_LOWG_DET        0x04
#define BIT_INT_STATUS3_FF_DET          0x02
#endif
#define BIT_INT_STATUS3_TAP_DET         0x01

/*
 * MPUREG_SIGNAL_PATH_RESET
 * Register Name: SIGNAL_PATH_RESET
 */

/* DMP_INIT_EN */
#define BIT_SIGNAL_PATH_RESET_DMP_INIT_POS       6
#define BIT_SIGNAL_PATH_RESET_DMP_INIT_MASK  (0x01 << BIT_SIGNAL_PATH_RESET_DMP_INIT_POS)

typedef enum
{
	ICM426XX_SIGNAL_PATH_RESET_DMP_INIT_EN  = (0x01 << BIT_SIGNAL_PATH_RESET_DMP_INIT_POS),
	ICM426XX_SIGNAL_PATH_RESET_DMP_INIT_DIS = (0x00 << BIT_SIGNAL_PATH_RESET_DMP_INIT_POS),
} ICM426XX_SIGNAL_PATH_RESET_DMP_INIT_t;

/* DMP_MEM_RESET_EN */
#define BIT_SIGNAL_PATH_RESET_DMP_MEM_RESET_POS       5
#define BIT_SIGNAL_PATH_RESET_DMP_MEM_RESET_MASK  (0x01 << BIT_SIGNAL_PATH_RESET_DMP_MEM_RESET_POS)

typedef enum
{
	ICM426XX_SIGNAL_PATH_RESET_DMP_MEM_RESET_EN  = (0x01 << BIT_SIGNAL_PATH_RESET_DMP_MEM_RESET_POS),
	ICM426XX_SIGNAL_PATH_RESET_DMP_MEM_RESET_DIS = (0x00 << BIT_SIGNAL_PATH_RESET_DMP_MEM_RESET_POS),
} ICM426XX_SIGNAL_PATH_RESET_DMP_MEM_RESET_t;

/* TMST_STROBE */
#define BIT_SIGNAL_PATH_RESET_TMST_STROBE_POS       2
#define BIT_SIGNAL_PATH_RESET_TMST_STROBE_MASK  (0x01 << BIT_SIGNAL_PATH_RESET_TMST_STROBE_POS)

typedef enum
{
	ICM426XX_SIGNAL_PATH_RESET_TMST_STROBE_EN  = (0x01 << BIT_SIGNAL_PATH_RESET_TMST_STROBE_POS),
	ICM426XX_SIGNAL_PATH_RESET_TMST_STROBE_DIS = (0x00 << BIT_SIGNAL_PATH_RESET_TMST_STROBE_POS),
} ICM426XX_SIGNAL_PATH_RESET_TMST_STROBE_t;

/* FIFO_FLUSH */
#define BIT_SIGNAL_PATH_RESET_FIFO_FLUSH_POS        1
#define BIT_SIGNAL_PATH_RESET_FIFO_FLUSH_MASK   (0x01 << BIT_SIGNAL_PATH_RESET_FIFO_FLUSH_POS)

typedef enum
{
	ICM426XX_SIGNAL_PATH_RESET_FIFO_FLUSH_EN  = (0x01 << BIT_SIGNAL_PATH_RESET_FIFO_FLUSH_POS),
	ICM426XX_SIGNAL_PATH_RESET_FIFO_FLUSH_DIS = (0x00 << BIT_SIGNAL_PATH_RESET_FIFO_FLUSH_POS),
} ICM426XX_SIGNAL_PATH_RESET_FIFO_FLUSH_t;

/*
 * MPUREG_INTF_CONFIG0
 * Register Name: INTF_CONFIG0
 */

/* FIFO_SREG_INVALID_IND */
#define BIT_FIFO_SREG_INVALID_IND_POS        7
#define BIT_FIFO_SREG_INVALID_IND_MASK   (0x01 << BIT_FIFO_SREG_INVALID_IND_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG0_FIFO_SREG_INVALID_IND_DIS = (0x01 << BIT_FIFO_SREG_INVALID_IND_POS),
	ICM426XX_INTF_CONFIG0_FIFO_SREG_INVALID_IND_EN  = (0x00 << BIT_FIFO_SREG_INVALID_IND_POS),
} ICM426XX_INTF_CONFIG0_FIFO_SREG_INVALID_IND_t;

/* FIFO_COUNT_REC */
#define BIT_FIFO_COUNT_REC_POS               6
#define BIT_FIFO_COUNT_REC_MASK          (0x01 << BIT_FIFO_COUNT_REC_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG0_FIFO_COUNT_REC_RECORD = (0x01 << BIT_FIFO_COUNT_REC_POS),
	ICM426XX_INTF_CONFIG0_FIFO_COUNT_REC_BYTE   = (0x00 << BIT_FIFO_COUNT_REC_POS),
} ICM426XX_INTF_CONFIG0_FIFO_COUNT_REC_t;

/* FIFO_COUNT_ENDIAN */
#define BIT_FIFO_COUNT_ENDIAN_POS           5
#define BIT_FIFO_COUNT_ENDIAN_MASK      (0x01 << BIT_FIFO_COUNT_ENDIAN_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG0_FIFO_COUNT_BIG_ENDIAN    = (0x01 << BIT_FIFO_COUNT_ENDIAN_POS),
	ICM426XX_INTF_CONFIG0_FIFO_COUNT_LITTLE_ENDIAN = (0x00 << BIT_FIFO_COUNT_ENDIAN_POS),
} ICM426XX_INTF_CONFIG0_FIFO_COUNT_ENDIAN_t;

/* DATA_ENDIAN */
#define BIT_DATA_ENDIAN_POS                 4
#define BIT_DATA_ENDIAN_MASK            (0x01 << BIT_DATA_ENDIAN_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG0_DATA_BIG_ENDIAN    = (0x01 << BIT_DATA_ENDIAN_POS),
	ICM426XX_INTF_CONFIG0_DATA_LITTLE_ENDIAN = (0x00 << BIT_DATA_ENDIAN_POS),
} ICM426XX_INTF_CONFIG0_DATA_ENDIAN_t;

/* SPI_MODE_OIS2 */
#define BIT_SPI_MODE_OIS2_POS               3
#define BIT_SPI_MODE_OIS2_MASK          (0x01 << BIT_SPI_MODE_OIS2_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG0_SPI_MODE_OIS2_1_2 = (0x01 << BIT_SPI_MODE_OIS2_POS),
	ICM426XX_INTF_CONFIG0_SPI_MODE_OIS2_0_3 = (0x00 << BIT_SPI_MODE_OIS2_POS),
} ICM426XX_INTF_CONFIG0_SPI_MODE_OIS2_t;

/* SPI_MODE_AUX1 */
#define BIT_SPI_MODE_OIS1_POS               2
#define BIT_SPI_MODE_OIS1_MASK          (0x01 << BIT_SPI_MODE_OIS1_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG0_SPI_MODE_OIS1_1_2 = (0x01 << BIT_SPI_MODE_OIS1_POS),
	ICM426XX_INTF_CONFIG0_SPI_MODE_OIS1_0_3 = (0x00 << BIT_SPI_MODE_OIS1_POS),
} ICM426XX_INTF_CONFIG0_SPI_MODE_OIS1_t;

/*
 * MPUREG_INTF_CONFIG1
 * Register Name: INTF_CONFIG1
 */

/* ACCEL_LP_CLK_SEL */
#define BIT_ACCEL_LP_CLK_SEL_POS        3
#define BIT_ACCEL_LP_CLK_SEL_MASK   (0x01 << BIT_ACCEL_LP_CLK_SEL_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG1_ACCEL_LP_CLK_WUOSC = (0x00 << BIT_ACCEL_LP_CLK_SEL_POS),
	ICM426XX_INTF_CONFIG1_ACCEL_LP_CLK_RCOSC = (0x01 << BIT_ACCEL_LP_CLK_SEL_POS),
} ICM426XX_INTF_CONFIG1_ACCEL_LP_CLK_t;

/* RTC_MODE */
#define BIT_RTC_MODE_POS        2
#define BIT_RTC_MODE_MASK   (0x01 << BIT_RTC_MODE_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG1_RTC_MODE_DIS = (0x00 << BIT_RTC_MODE_POS),
	ICM426XX_INTF_CONFIG1_RTC_MODE_EN  = (0x01 << BIT_RTC_MODE_POS),
} ICM426XX_INTF_CONFIG1_RTC_MODE_t;

/*
 * MPUREG_PWR_MGMT_0
 * Register Name: PWR_MGMT_0
 */

/* TEMP_DIS */
#define BIT_PWR_MGMT_0_TEMP_POS        5
#define BIT_PWR_MGMT_0_TEMP_MASK   (0x01 << BIT_PWR_MGMT_0_TEMP_POS)

typedef enum
{
	ICM426XX_PWR_MGMT_0_TEMP_DIS = (0x01 << BIT_PWR_MGMT_0_TEMP_POS),
	ICM426XX_PWR_MGMT_0_TEMP_EN  = (0x00 << BIT_PWR_MGMT_0_TEMP_POS),
} ICM426XX_PWR_MGMT_0_TEMP_t;

/* IDLE */
#define BIT_PWR_MGMT_0_IDLE_POS        4
#define BIT_PWR_MGMT_0_IDLE_MASK   (0x01 << BIT_PWR_MGMT_0_IDLE_POS)

typedef enum
{
	ICM426XX_PWR_MGMT_0_IDLE_DIS = (0x01 << BIT_PWR_MGMT_0_IDLE_POS),
	ICM426XX_PWR_MGMT_0_IDLE_EN  = (0x00 << BIT_PWR_MGMT_0_IDLE_POS),
} ICM426XX_PWR_MGMT_0_IDLE_t;

/* GYRO_MODE */
#define BIT_PWR_MGMT_0_GYRO_MODE_POS        2
#define BIT_PWR_MGMT_0_GYRO_MODE_MASK   (0x03 << BIT_PWR_MGMT_0_GYRO_MODE_POS)

typedef enum
{
	ICM426XX_PWR_MGMT_0_GYRO_MODE_LN      = (0x03 << BIT_PWR_MGMT_0_GYRO_MODE_POS),
	ICM426XX_PWR_MGMT_0_GYRO_MODE_STANDBY = (0x01 << BIT_PWR_MGMT_0_GYRO_MODE_POS),
	ICM426XX_PWR_MGMT_0_GYRO_MODE_OFF     = (0x00 << BIT_PWR_MGMT_0_GYRO_MODE_POS),
} ICM426XX_PWR_MGMT_0_GYRO_MODE_t;

/* ACCEL_MODE */
#define BIT_PWR_MGMT_0_ACCEL_MODE_POS       0
#define BIT_PWR_MGMT_0_ACCEL_MODE_MASK   0x03

typedef enum
{
	ICM426XX_PWR_MGMT_0_ACCEL_MODE_LN  = 0x03,
	ICM426XX_PWR_MGMT_0_ACCEL_MODE_LP  = 0x02,
	ICM426XX_PWR_MGMT_0_ACCEL_MODE_OFF = 0x00,
} ICM426XX_PWR_MGMT_0_ACCEL_MODE_t;

/*
 * MPUREG_GYRO_CONFIG0
 * Register Name: GYRO_CONFIG0
 */

/* GYRO_FS_SEL*/
#define BIT_GYRO_CONFIG0_FS_SEL_POS       5
#define BIT_GYRO_CONFIG0_FS_SEL_MASK     (7 << BIT_GYRO_CONFIG0_FS_SEL_POS)

/** @brief Gyroscope FSR selection 
 */
typedef enum
{
#if defined(ICM42686)
	ICM426XX_GYRO_CONFIG0_FS_SEL_31dps   = (7 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 31dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_62dps   = (6 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 62dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_125dps  = (5 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 125dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_250dps  = (4 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 250dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_500dps  = (3 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 500dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_1000dps = (2 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 1000dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_2000dps = (1 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 2000dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_4000dps = (0 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 4000dps*/
#else
	ICM426XX_GYRO_CONFIG0_FS_SEL_16dps   = (7 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 16dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_31dps   = (6 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 31dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_62dps   = (5 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 62dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_125dps  = (4 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 125dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_250dps  = (3 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 250dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_500dps  = (2 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 500dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_1000dps = (1 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 1000dps*/
	ICM426XX_GYRO_CONFIG0_FS_SEL_2000dps = (0 << BIT_GYRO_CONFIG0_FS_SEL_POS),  /*!< 2000dps*/
#endif
} ICM426XX_GYRO_CONFIG0_FS_SEL_t;

/* GYRO_ODR */
#define BIT_GYRO_CONFIG0_ODR_POS       0
#define BIT_GYRO_CONFIG0_ODR_MASK   0x0F

/** @brief Gyroscope ODR selection 
 */
typedef enum
{
	ICM426XX_GYRO_CONFIG0_ODR_500_HZ    = 0x0F,  /*!< 500 Hz (2 ms)*/
	ICM426XX_GYRO_CONFIG0_ODR_12_5_HZ   = 0x0B,  /*!< 12.5 Hz (80 ms)*/
	ICM426XX_GYRO_CONFIG0_ODR_25_HZ     = 0x0A,  /*!< 25 Hz (40 ms)*/
	ICM426XX_GYRO_CONFIG0_ODR_50_HZ     = 0x09,  /*!< 50 Hz (20 ms)*/
	ICM426XX_GYRO_CONFIG0_ODR_100_HZ    = 0x08,  /*!< 100 Hz (10 ms)*/
	ICM426XX_GYRO_CONFIG0_ODR_200_HZ    = 0x07,  /*!< 200 Hz (5 ms)*/
	ICM426XX_GYRO_CONFIG0_ODR_1_KHZ     = 0x06,  /*!< 1 KHz (1 ms)*/
	ICM426XX_GYRO_CONFIG0_ODR_2_KHZ     = 0x05,  /*!< 2 KHz (500 us)*/
	ICM426XX_GYRO_CONFIG0_ODR_4_KHZ     = 0x04,  /*!< 4 KHz (250 us)*/
	ICM426XX_GYRO_CONFIG0_ODR_8_KHZ     = 0x03,  /*!< 8 KHz (125 us)*/
	ICM426XX_GYRO_CONFIG0_ODR_16_KHZ    = 0x02,  /*!< 16 KHz (62.5 us)*/
	ICM426XX_GYRO_CONFIG0_ODR_32_KHZ    = 0x01,  /*!< 32 KHz (31.25 us)*/
} ICM426XX_GYRO_CONFIG0_ODR_t;

/*
 * MPUREG_ACCEL_CONFIG0
 * Register Name: ACCEL_CONFIG0
 */

/* ACCEL_FS_SEL */
#define BIT_ACCEL_CONFIG0_FS_SEL_POS       5
#define BIT_ACCEL_CONFIG0_FS_SEL_MASK   (0x7 << BIT_ACCEL_CONFIG0_FS_SEL_POS)

/** @brief Accelerometer FSR selection 
 */
typedef enum
{
#if defined(ICM42686)
	ICM426XX_ACCEL_CONFIG0_FS_SEL_2g  = (0x4 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 2g*/
	ICM426XX_ACCEL_CONFIG0_FS_SEL_4g  = (0x3 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 4g*/
	ICM426XX_ACCEL_CONFIG0_FS_SEL_8g  = (0x2 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 8g*/
	ICM426XX_ACCEL_CONFIG0_FS_SEL_16g = (0x1 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 16g*/
	ICM426XX_ACCEL_CONFIG0_FS_SEL_32g = (0x0 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 32g*/
#else
	ICM426XX_ACCEL_CONFIG0_FS_SEL_RESERVED = (0x4 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  
	ICM426XX_ACCEL_CONFIG0_FS_SEL_2g       = (0x3 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 2g*/
	ICM426XX_ACCEL_CONFIG0_FS_SEL_4g       = (0x2 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 4g*/
	ICM426XX_ACCEL_CONFIG0_FS_SEL_8g       = (0x1 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 8g*/
	ICM426XX_ACCEL_CONFIG0_FS_SEL_16g      = (0x0 << BIT_ACCEL_CONFIG0_FS_SEL_POS),  /*!< 16g*/
#endif
} ICM426XX_ACCEL_CONFIG0_FS_SEL_t;

/* ACCEL_ODR */
#define BIT_ACCEL_CONFIG0_ODR_POS       0
#define BIT_ACCEL_CONFIG0_ODR_MASK   0x0F

/** @brief Accelerometer ODR selection 
 */
typedef enum
{
	ICM426XX_ACCEL_CONFIG0_ODR_500_HZ    = 0xF,  /*!< 500 Hz (2 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_1_5625_HZ = 0xE,  /*!< 1.5625 Hz (640 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_3_125_HZ  = 0xD,  /*!< 3.125 Hz (320 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_6_25_HZ   = 0xC,  /*!< 6.25 Hz (160 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_12_5_HZ   = 0xB,  /*!< 12.5 Hz (80 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_25_HZ     = 0xA,  /*!< 25 Hz (40 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_50_HZ     = 0x9,  /*!< 50 Hz (20 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_100_HZ    = 0x8,  /*!< 100 Hz (10 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_200_HZ    = 0x7,  /*!< 200 Hz (5 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_1_KHZ     = 0x6,  /*!< 1 KHz (1 ms)*/
	ICM426XX_ACCEL_CONFIG0_ODR_2_KHZ     = 0x5,  /*!< 2 KHz (500 us)*/
	ICM426XX_ACCEL_CONFIG0_ODR_4_KHZ     = 0x4,  /*!< 4 KHz (250 us)*/
	ICM426XX_ACCEL_CONFIG0_ODR_8_KHZ     = 0x3,  /*!< 8 KHz (125 us)*/
	ICM426XX_ACCEL_CONFIG0_ODR_16_KHZ    = 0x2,  /*!< 16 KHz (62.5 us)*/
	ICM426XX_ACCEL_CONFIG0_ODR_32_KHZ    = 0x1,  /*!< 32 KHz (31.25 us)*/
} ICM426XX_ACCEL_CONFIG0_ODR_t;

/*
 * MPUREG_GYRO_CONFIG1
 * Register Name: GYRO_CONFIG1
 */

/* TEMP_FILT_BW */
#define BIT_GYRO_CONFIG1_TEMP_FILT_BW_POS        5
#define BIT_GYRO_CONFIG1_TEMP_FILT_BW_MASK    (0x7 << BIT_GYRO_CONFIG1_TEMP_FILT_BW_POS)

/* GYRO_UI_FILT_ORD */
#define BIT_GYRO_CONFIG1_GYRO_UI_FILT_ORD_POS       2
#define BIT_GYRO_CONFIG1_GYRO_UI_FILT_ORD_MASK   (0x3 << BIT_GYRO_CONFIG1_GYRO_UI_FILT_ORD_POS)

typedef enum
{
	ICM426XX_GYRO_CONFIG_GYRO_UI_FILT_ORD_1ST_ORDER = (0x0 << BIT_GYRO_CONFIG1_GYRO_UI_FILT_ORD_POS),
	ICM426XX_GYRO_CONFIG_GYRO_UI_FILT_ORD_2ND_ORDER = (0x1 << BIT_GYRO_CONFIG1_GYRO_UI_FILT_ORD_POS),
	ICM426XX_GYRO_CONFIG_GYRO_UI_FILT_ORD_3RD_ORDER = (0x2 << BIT_GYRO_CONFIG1_GYRO_UI_FILT_ORD_POS),
} ICM426XX_GYRO_CONFIG_GYRO_UI_FILT_ORD_t;

/* GYRO_DEC2_M2_ORD */
#define BIT_GYRO_CONFIG1_GYRO_DEC2_M2_ORD_POS      0
#define BIT_GYRO_CONFIG1_GYRO_DEC2_M2_ORD_MASK   0x3


/*
 * MPUREG_ACCEL_GYRO_CONFIG0
 * Register Name: GYRO_ACCEL_CONFIG0
 */

/* ACCEL_UI_FILT_BW_IND */
#define BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS       4
#define BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_MASK   (0xF << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS) 

typedef enum
{
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_40 = (0x7 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_20 = (0x6 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_16 = (0x5 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_10 = (0x4 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_8  = (0x3 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_5  = (0x2 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_4  = (0x1 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_2  = (0x0 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
} ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_BW_t;

typedef enum
{
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_AVG_16  = (0x6 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
	ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_AVG_1   = (0x1 << BIT_GYRO_ACCEL_CONFIG0_ACCEL_FILT_POS),
} ICM426XX_GYRO_ACCEL_CONFIG0_ACCEL_FILT_AVG_t;

/* GYRO_UI_FILT_BW_IND */
#define BIT_GYRO_ACCEL_CONFIG0_GYRO_FILT_POS       0
#define BIT_GYRO_ACCEL_CONFIG0_GYRO_FILT_MASK   0x0F

typedef enum
{
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_40 = 0x07,
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_20 = 0x06,
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_16 = 0x05,
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_10 = 0x04,
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_8  = 0x03,
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_5  = 0x02,
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_4  = 0x01,
	ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_2  = 0x00,
} ICM426XX_GYRO_ACCEL_CONFIG0_GYRO_FILT_BW_t;

/*
 * MPUREG_ACCEL_CONFIG1
 * Register Name: ACCEL_CONFIG1
 */

/* ACCEL_UI_FILT_ORD */
#define BIT_ACCEL_CONFIG1_ACCEL_UI_FILT_ORD_POS       3
#define BIT_ACCEL_CONFIG1_ACCEL_UI_FILT_ORD_MASK    (0x3 << BIT_ACCEL_CONFIG1_ACCEL_UI_FILT_ORD_POS)

typedef enum
{
	ICM426XX_ACCEL_CONFIG_ACCEL_UI_FILT_ORD_1ST_ORDER = (0x0 << BIT_ACCEL_CONFIG1_ACCEL_UI_FILT_ORD_POS),
	ICM426XX_ACCEL_CONFIG_ACCEL_UI_FILT_ORD_2ND_ORDER = (0x1 << BIT_ACCEL_CONFIG1_ACCEL_UI_FILT_ORD_POS),
	ICM426XX_ACCEL_CONFIG_ACCEL_UI_FILT_ORD_3RD_ORDER = (0x2 << BIT_ACCEL_CONFIG1_ACCEL_UI_FILT_ORD_POS),
} ICM426XX_ACCEL_CONFIG_ACCEL_UI_FILT_ORD_t;

/* ACCEL_DEC2_M2_ORD */
#define BIT_ACCEL_CONFIG1_ACCEL_DEC2_M2_ORD_POS       1
#define BIT_ACCEL_CONFIG1_ACCEL_DEC2_M2_ORD_MASK    (0x3 << BIT_ACCEL_CONFIG1_ACCEL_DEC2_M2_ORD_POS)

/*
 * MPUREG_TMST_CONFIG
 * Register Name: TMST_CONFIG
 */

/* TMST_TO_REGS */
#define BIT_TMST_CONFIG_TMST_TO_REGS_EN_POS       4
#define BIT_TMST_CONFIG_TMST_TO_REGS_EN_MASK   (0x1 << BIT_TMST_CONFIG_TMST_TO_REGS_EN_POS)

 typedef enum
{
	ICM426XX_TMST_CONFIG_TMST_TO_REGS_EN   = (0x1 << BIT_TMST_CONFIG_TMST_TO_REGS_EN_POS),
	ICM426XX_TMST_CONFIG_TMST_TO_REGS_DIS  = (0x0 << BIT_TMST_CONFIG_TMST_TO_REGS_EN_POS),
} ICM426XX_TMST_CONFIG_TMST_TO_REGS_EN_t;

/* TMST_RES */
#define BIT_TMST_CONFIG_RESOL_POS        3
#define BIT_TMST_CONFIG_RESOL_MASK    (0x1 << BIT_TMST_CONFIG_RESOL_POS)

typedef enum
{
	ICM426XX_TMST_CONFIG_RESOL_16us = (0x01 << BIT_TMST_CONFIG_RESOL_POS),
	ICM426XX_TMST_CONFIG_RESOL_1us  = (0x00 << BIT_TMST_CONFIG_RESOL_POS),
} ICM426XX_TMST_CONFIG_RESOL_t;

/* TMST_FSYNC */
#define BIT_TMST_CONFIG_TMST_FSYNC_POS        1
#define BIT_TMST_CONFIG_TMST_FSYNC_MASK    (0x1 << BIT_TMST_CONFIG_TMST_FSYNC_POS)

typedef enum
{
	ICM426XX_TMST_CONFIG_TMST_FSYNC_EN  = (0x01 << BIT_TMST_CONFIG_TMST_FSYNC_POS),
	ICM426XX_TMST_CONFIG_TMST_FSYNC_DIS = (0x00 << BIT_TMST_CONFIG_TMST_FSYNC_POS),
} ICM426XX_TMST_CONFIG_TMST_FSYNC_EN_t;

/* TMST_EN */
#define BIT_TMST_CONFIG_TMST_EN_POS       0
#define BIT_TMST_CONFIG_TMST_EN_MASK    0x1

typedef enum
{
	ICM426XX_TMST_CONFIG_TMST_EN  = 0x01,
	ICM426XX_TMST_CONFIG_TMST_DIS = 0x00,
} ICM426XX_TMST_CONFIG_TMST_EN_t;

/*
 * MPUREG_APEX_CONFIG0
 * Register Name: APEX_CONFIG0
 */

/* DMP_POWER_SAVE_EN */
#define BIT_APEX_CONFIG0_DMP_POWER_SAVE_POS       7
#define BIT_APEX_CONFIG0_DMP_POWER_SAVE_MASK   (0x1 << BIT_APEX_CONFIG0_DMP_POWER_SAVE_POS)

 typedef enum
{
	ICM426XX_APEX_CONFIG0_DMP_POWER_SAVE_EN   = (0x1 << BIT_APEX_CONFIG0_DMP_POWER_SAVE_POS),
	ICM426XX_APEX_CONFIG0_DMP_POWER_SAVE_DIS  = (0x0 << BIT_APEX_CONFIG0_DMP_POWER_SAVE_POS),
} ICM426XX_APEX_CONFIG0_DMP_POWER_SAVE_t;

/* TAP_ENABLE */
#define BIT_APEX_CONFIG0_TAP_ENABLE_POS       6
#define BIT_APEX_CONFIG0_TAP_ENABLE_MASK   (0x1 << BIT_APEX_CONFIG0_TAP_ENABLE_POS)

 typedef enum
{
	ICM426XX_APEX_CONFIG0_TAP_ENABLE_EN   = (0x1 << BIT_APEX_CONFIG0_TAP_ENABLE_POS),
	ICM426XX_APEX_CONFIG0_TAP_ENABLE_DIS  = (0x0 << BIT_APEX_CONFIG0_TAP_ENABLE_POS),
} ICM426XX_APEX_CONFIG0_TAP_ENABLE_t;

/* PEDO_EN */
#define BIT_APEX_CONFIG0_PEDO_EN_POS       5
#define BIT_APEX_CONFIG0_PEDO_EN_MASK   (0x1 << BIT_APEX_CONFIG0_PEDO_EN_POS)

 typedef enum
{
	ICM426XX_APEX_CONFIG0_PEDO_EN_EN   = (0x1 << BIT_APEX_CONFIG0_PEDO_EN_POS),
	ICM426XX_APEX_CONFIG0_PEDO_EN_DIS  = (0x0 << BIT_APEX_CONFIG0_PEDO_EN_POS),
} ICM426XX_APEX_CONFIG0_PEDO_EN_t;

#if defined(ICM_FAMILY_BPLUS)

/* R2W_EN */
#define BIT_APEX_CONFIG0_R2W_EN_POS       3
#define BIT_APEX_CONFIG0_R2W_EN_MASK   (0x1 << BIT_APEX_CONFIG0_R2W_EN_POS)

 typedef enum
{
	ICM426XX_APEX_CONFIG0_R2W_EN_EN   = (0x1 << BIT_APEX_CONFIG0_R2W_EN_POS),
	ICM426XX_APEX_CONFIG0_R2W_EN_DIS  = (0x0 << BIT_APEX_CONFIG0_R2W_EN_POS),
} ICM426XX_APEX_CONFIG0_R2W_EN_t;

#elif defined(ICM_FAMILY_CPLUS)

/* FF_EN */
#define BIT_APEX_CONFIG0_FF_EN_POS       2
#define BIT_APEX_CONFIG0_FF_EN_MASK   (0x1 << BIT_APEX_CONFIG0_FF_EN_POS)

 typedef enum
{
	ICM426XX_APEX_CONFIG0_FF_EN_EN   = (0x1 << BIT_APEX_CONFIG0_FF_EN_POS),
	ICM426XX_APEX_CONFIG0_FF_EN_DIS  = (0x0 << BIT_APEX_CONFIG0_FF_EN_POS),
} ICM426XX_APEX_CONFIG0_FF_EN_t;

/* LOWG_EN */
#define BIT_APEX_CONFIG0_LOWG_EN_POS       3
#define BIT_APEX_CONFIG0_LOWG_EN_MASK   (0x1 << BIT_APEX_CONFIG0_LOWG_EN_POS)

 typedef enum
{
	ICM426XX_APEX_CONFIG0_LOWG_EN_EN   = (0x1 << BIT_APEX_CONFIG0_LOWG_EN_POS),
	ICM426XX_APEX_CONFIG0_LOWG_EN_DIS  = (0x0 << BIT_APEX_CONFIG0_LOWG_EN_POS),
} ICM426XX_APEX_CONFIG0_LOWG_EN_t;

#endif

/* TILT_EN */
#define BIT_APEX_CONFIG0_TILT_EN_POS       4
#define BIT_APEX_CONFIG0_TILT_EN_MASK   (0x1 << BIT_APEX_CONFIG0_TILT_EN_POS)

 typedef enum
{
	ICM426XX_APEX_CONFIG0_TILT_EN_EN   = (0x1 << BIT_APEX_CONFIG0_TILT_EN_POS),
	ICM426XX_APEX_CONFIG0_TILT_EN_DIS  = (0x0 << BIT_APEX_CONFIG0_TILT_EN_POS),
} ICM426XX_APEX_CONFIG0_TILT_EN_t;

/* DMP_ODR */
#define BIT_APEX_CONFIG0_DMP_ODR_POS       0
#define BIT_APEX_CONFIG0_DMP_ODR_MASK   (0x3 << BIT_APEX_CONFIG0_DMP_ODR_POS)

/** @brief DMP ODR selection
 */
typedef enum
{
	ICM426XX_APEX_CONFIG0_DMP_ODR_25Hz     = (0x0 << BIT_APEX_CONFIG0_DMP_ODR_POS), /**< 25Hz (40ms) */
	ICM426XX_APEX_CONFIG0_DMP_ODR_50Hz     = (0x2 << BIT_APEX_CONFIG0_DMP_ODR_POS), /**< 50Hz (20ms) */
	ICM426XX_APEX_CONFIG0_DMP_ODR_100Hz    = (0x3 << BIT_APEX_CONFIG0_DMP_ODR_POS), /**< 100Hz (10ms) */
	ICM426XX_APEX_CONFIG0_DMP_ODR_500Hz    = (0x1 << BIT_APEX_CONFIG0_DMP_ODR_POS), /**< 500Hz (40ms) */
} ICM426XX_APEX_CONFIG0_DMP_ODR_t;

/*
 * MPUREG_SMD_CONFIG
 * Register Name: SMD_CONFIG
 */

/* WOM_INT_MODE */
#define BIT_SMD_CONFIG_WOM_INT_MODE_POS       3
#define BIT_SMD_CONFIG_WOM_INT_MODE_MASK   (0x1 << BIT_SMD_CONFIG_WOM_INT_MODE_POS)

typedef enum
{
	ICM426XX_SMD_CONFIG_WOM_INT_MODE_ANDED = (0x01 << BIT_SMD_CONFIG_WOM_INT_MODE_POS),
	ICM426XX_SMD_CONFIG_WOM_INT_MODE_ORED  = (0x00 << BIT_SMD_CONFIG_WOM_INT_MODE_POS),
} ICM426XX_SMD_CONFIG_WOM_INT_MODE_t;

/* WOM_MODE */
#define BIT_SMD_CONFIG_WOM_MODE_POS       2
#define BIT_SMD_CONFIG_WOM_MODE_MASK   (0x1 << BIT_SMD_CONFIG_WOM_MODE_POS)

typedef enum
{
	ICM426XX_SMD_CONFIG_WOM_MODE_CMP_PREV = (0x01 << BIT_SMD_CONFIG_WOM_MODE_POS),
	ICM426XX_SMD_CONFIG_WOM_MODE_CMP_INIT = (0x00 << BIT_SMD_CONFIG_WOM_MODE_POS),
} ICM426XX_SMD_CONFIG_WOM_MODE_t;

/* SMD_MODE */
#define BIT_SMD_CONFIG_SMD_MODE_POS       0
#define BIT_SMD_CONFIG_SMD_MODE_MASK    0x3

typedef enum
{
	ICM426XX_SMD_CONFIG_SMD_MODE_LONG     = 0x03,
	ICM426XX_SMD_CONFIG_SMD_MODE_SHORT    = 0x02,
	ICM426XX_SMD_CONFIG_SMD_MODE_WOM      = 0x01,
	ICM426XX_SMD_CONFIG_SMD_MODE_DISABLED = 0x00,
} ICM426XX_SMD_CONFIG_SMD_MODE_t;

/*
 * MPUREG_FIFO_CONFIG1
 * Register Name: FIFO_CONFIG1
 */
#define BIT_FIFO_CONFIG1_RESUME_PARTIAL_RD_POS       6
#define BIT_FIFO_CONFIG1_RESUME_PARTIAL_RD_MASK   (0x1 << BIT_FIFO_CONFIG1_RESUME_PARTIAL_RD_POS)

/* FIFO_WM_GT_TH */
#define BIT_FIFO_CONFIG1_WM_GT_TH_POS       5
#define BIT_FIFO_CONFIG1_WM_GT_TH_MASK   (0x1 << BIT_FIFO_CONFIG1_WM_GT_TH_POS)

typedef enum
{
	ICM426XX_FIFO_CONFIG1_WM_GT_TH_EN  = (0x1 << BIT_FIFO_CONFIG1_WM_GT_TH_POS),
	ICM426XX_FIFO_CONFIG1_WM_GT_TH_DIS = (0x0 << BIT_FIFO_CONFIG1_WM_GT_TH_POS),
} ICM426XX_FIFO_CONFIG1_WM_GT_t;

/* FIFO_HIRES_EN */
#define BIT_FIFO_CONFIG1_HIRES_POS       4
#define BIT_FIFO_CONFIG1_HIRES_MASK   (0x1 << BIT_FIFO_CONFIG1_HIRES_POS)

typedef enum
{
	ICM426XX_FIFO_CONFIG1_HIRES_EN  = (0x1 << BIT_FIFO_CONFIG1_HIRES_POS),
	ICM426XX_FIFO_CONFIG1_HIRES_DIS = (0x0 << BIT_FIFO_CONFIG1_HIRES_POS),
} ICM426XX_FIFO_CONFIG1_HIRES_t;

/* FIFO_TMST_FSYNC_EN */
#define BIT_FIFO_CONFIG1_TMST_FSYNC_POS       3
#define BIT_FIFO_CONFIG1_TMST_FSYNC_MASK   (0x1 << BIT_FIFO_CONFIG1_TMST_FSYNC_POS)

typedef enum
{
	ICM426XX_FIFO_CONFIG1_TMST_FSYNC_EN  = (0x1 << BIT_FIFO_CONFIG1_TMST_FSYNC_POS),
	ICM426XX_FIFO_CONFIG1_TMST_FSYNC_DIS = (0x0 << BIT_FIFO_CONFIG1_TMST_FSYNC_POS),
} ICM426XX_FIFO_CONFIG1_TMST_FSYNC_t;

/* FIFO_TEMP_EN */
#define BIT_FIFO_CONFIG1_TEMP_POS       2
#define BIT_FIFO_CONFIG1_TEMP_MASK   (0x1 << BIT_FIFO_CONFIG1_TEMP_POS)

typedef enum
{
	ICM426XX_FIFO_CONFIG1_TEMP_EN  = (0x1 << BIT_FIFO_CONFIG1_TEMP_POS),
	ICM426XX_FIFO_CONFIG1_TEMP_DIS = (0x0 << BIT_FIFO_CONFIG1_TEMP_POS),
} ICM426XX_FIFO_CONFIG1_TEMP_t;

/* FIFO_GYRO_EN */
#define BIT_FIFO_CONFIG1_GYRO_POS       1
#define BIT_FIFO_CONFIG1_GYRO_MASK   (0x1 << BIT_FIFO_CONFIG1_GYRO_POS)

typedef enum
{
	ICM426XX_FIFO_CONFIG1_GYRO_EN  = (0x1 << BIT_FIFO_CONFIG1_GYRO_POS),
	ICM426XX_FIFO_CONFIG1_GYRO_DIS = (0x0 << BIT_FIFO_CONFIG1_GYRO_POS),
} ICM426XX_FIFO_CONFIG1_GYRO_t;

/* FIFO_ACCEL_EN*/
#define BIT_FIFO_CONFIG1_ACCEL_POS       0
#define BIT_FIFO_CONFIG1_ACCEL_MASK    0x1

typedef enum
{
	ICM426XX_FIFO_CONFIG1_ACCEL_EN  = 0x01,
	ICM426XX_FIFO_CONFIG1_ACCEL_DIS = 0x00,
} ICM426XX_FIFO_CONFIG1_ACCEL_t;

/*
 * MPUREG_FSYNC_CONFIG
 * Register Name: FSYNC_CONFIG
 */

/* FSYNC_UI_SEL */
#define BIT_FSYNC_CONFIG_UI_SEL_POS       4
#define BIT_FSYNC_CONFIG_UI_SEL_MASK   (0x7 << BIT_FSYNC_CONFIG_UI_SEL_POS)

typedef enum
{
	ICM426XX_FSYNC_CONFIG_UI_SEL_NO      = (0x0 << BIT_FSYNC_CONFIG_UI_SEL_POS),
	ICM426XX_FSYNC_CONFIG_UI_SEL_TEMP    = (0x1 << BIT_FSYNC_CONFIG_UI_SEL_POS),
	ICM426XX_FSYNC_CONFIG_UI_SEL_GYRO_X  = (0x2 << BIT_FSYNC_CONFIG_UI_SEL_POS),
	ICM426XX_FSYNC_CONFIG_UI_SEL_GYRO_Y  = (0x3 << BIT_FSYNC_CONFIG_UI_SEL_POS),
	ICM426XX_FSYNC_CONFIG_UI_SEL_GYRO_Z  = (0x4 << BIT_FSYNC_CONFIG_UI_SEL_POS),
	ICM426XX_FSYNC_CONFIG_UI_SEL_ACCEL_X = (0x5 << BIT_FSYNC_CONFIG_UI_SEL_POS),
	ICM426XX_FSYNC_CONFIG_UI_SEL_ACCEL_Y = (0x6 << BIT_FSYNC_CONFIG_UI_SEL_POS),
	ICM426XX_FSYNC_CONFIG_UI_SEL_ACCEL_Z = (0x7 << BIT_FSYNC_CONFIG_UI_SEL_POS),
} ICM426XX_FSYNC_CONFIG_UI_SEL_t;

/*
 * MPUREG_INT_CONFIG1
 * Register Name: INT_CONFIG1
 */

/* INT_TPULSE_DURATION */
#define BIT_INT_TPULSE_DURATION_POS      6
#define BIT_INT_TPULSE_DURATION_MASK  (0x1 << BIT_INT_TPULSE_DURATION_POS)

typedef enum
{
  ICM426XX_INT_TPULSE_DURATION_8_US    = (0x1 << BIT_INT_TPULSE_DURATION_POS),
  ICM426XX_INT_TPULSE_DURATION_100_US  = (0x0 << BIT_INT_TPULSE_DURATION_POS),
} ICM426XX_INT_TPULSE_DURATION_t;

/* INT_TDEASSERT_DISABLE */
#define BIT_INT_TDEASSERT_POS      5
#define BIT_INT_TDEASSERT_MASK  (0x1 << BIT_INT_TDEASSERT_POS)

typedef enum
{
  ICM426XX_INT_TDEASSERT_DISABLED = (0x1 << BIT_INT_TDEASSERT_POS),
  ICM426XX_INT_TDEASSERT_ENABLED  = (0x0 << BIT_INT_TDEASSERT_POS),
} ICM426XX_INT_TDEASSERT_t;

/* ASY_RESET_DISABLE */
#define BIT_INT_CONFIG1_ASY_RST_POS      4
#define BIT_INT_CONFIG1_ASY_RST_MASK  (0x1 << BIT_INT_CONFIG1_ASY_RST_POS)

typedef enum
{
  ICM426XX_INT_CONFIG1_ASY_RST_DISABLED = (0x1 << BIT_INT_CONFIG1_ASY_RST_POS),
  ICM426XX_INT_CONFIG1_ASY_RST_ENABLED  = (0x0 << BIT_INT_CONFIG1_ASY_RST_POS),
} ICM426XX_INT_CONFIG1_ASY_RST_t;

/*
 * MPUREG_INT_SOURCE0
 * Register Name: INT_SOURCE0
 */
#define BIT_INT_UI_FSYNC_INT_EN_POS         6
#define BIT_INT_PLL_RDY_INT_EN_POS          5
#define BIT_INT_RESET_DONE_INT_EN_POS       4
#define BIT_INT_UI_DRDY_INT_EN_POS          3
#define BIT_INT_FIFO_THS_INT_EN_POS         2
#define BIT_INT_FIFO_FULL_INT_EN_POS        1
#define BIT_INT_UI_AGC_RDY_INT_EN_POS       0

#define BIT_INT_SOURCE0_UI_FSYNC_INT1_EN    0x40
#define BIT_INT_SOURCE0_PLL_RDY_INT1_EN     0x20
#define BIT_INT_SOURCE0_RESET_DONE_INT1_EN  0x10
#define BIT_INT_SOURCE0_UI_DRDY_INT1_EN     0x08
#define BIT_INT_SOURCE0_FIFO_THS_INT1_EN    0x04
#define BIT_INT_SOURCE0_FIFO_FULL_INT1_EN   0x02
#define BIT_INT_SOURCE0_UI_AGC_RDY_INT1_EN  0x01

/*
 * MPUREG_INT_SOURCE1
 * Register Name: INT_SOURCE1
 */
#define BIT_INT_SMD_INT_EN_POS              3
#define BIT_INT_WOM_Z_INT_EN_POS            2
#define BIT_INT_WOM_Y_INT_EN_POS            1
#define BIT_INT_WOM_X_INT_EN_POS            0

#define BIT_INT_SOURCE1_SMD_INT1_EN         0x08
#define BIT_INT_SOURCE1_WOM_Z_INT1_EN       0x04
#define BIT_INT_SOURCE1_WOM_Y_INT1_EN       0x02
#define BIT_INT_SOURCE1_WOM_X_INT1_EN       0x01

/*
 * MPUREG_INT_SOURCE2
 * Register Name: INT_SOURCE2
 */
#define BIT_INT_SOURCE2_OIS2_AGC_RDY_INT1_EN 0x20
#define BIT_INT_SOURCE2_OIS2_FSYNC_INT1_EN   0x10
#define BIT_INT_SOURCE2_OIS2_DRDY_INT1_EN    0x08
#define BIT_INT_SOURCE2_OIS1_AGC_RDY_INT1_EN 0x04
#define BIT_INT_SOURCE2_OIS1_FSYNC_INT1_EN   0x02
#define BIT_INT_SOURCE2_OIS1_DRDY_INT1_EN    0x01

/*
 * MPUREG_INT_SOURCE3
 * Register Name: INT_SOURCE3
 */
#define BIT_INT_SOURCE3_UI_FSYNC_INT2_EN    0x40
#define BIT_INT_SOURCE3_PLL_RDY_INT2_EN     0x20
#define BIT_INT_SOURCE3_RESET_DONE_INT2_EN  0x10
#define BIT_INT_SOURCE3_UI_DRDY_INT2_EN     0x08
#define BIT_INT_SOURCE3_FIFO_THS_INT2_EN    0x04
#define BIT_INT_SOURCE3_FIFO_FULL_INT2_EN   0x02
#define BIT_INT_SOURCE3_UI_AGC_RDY_INT2_EN  0x01

/*
 * MPUREG_INT_SOURCE4
 * Register Name: INT_SOURCE4
 */
#define BIT_INT_SOURCE4_SMD_INT2_EN         0x08
#define BIT_INT_SOURCE4_WOM_Z_INT2_EN       0x04
#define BIT_INT_SOURCE4_WOM_Y_INT2_EN       0x02
#define BIT_INT_SOURCE4_WOM_X_INT2_EN       0x01

/*
 * MPUREG_INT_SOURCE5
 * Register Name: INT_SOURCE5
 */
#define BIT_INT_SOURCE5_OIS2_AGC_RDY_INT2_EN 0x20
#define BIT_INT_SOURCE5_OIS2_FSYNC_INT2_EN   0x10
#define BIT_INT_SOURCE5_OIS2_DRDY_INT2_EN    0x08
#define BIT_INT_SOURCE5_OIS1_AGC_RDY_INT2_EN 0x04
#define BIT_INT_SOURCE5_OIS1_FSYNC_INT2_EN   0x02
#define BIT_INT_SOURCE5_OIS1_DRDY_INT2_EN    0x01

/*
 * MPUREG_SELF_TEST_CONFIG
 * Register Name: SELF_TEST_CONFIG
*/
#define BIT_ST_REGULATOR_EN                 0x40
#define BIT_ACCEL_Z_ST_EN                   0x20
#define BIT_ACCEL_Y_ST_EN                   0x10
#define BIT_ACCEL_X_ST_EN                   0x08
#define BIT_GYRO_Z_ST_EN                    0x04
#define BIT_GYRO_Y_ST_EN                    0x02
#define BIT_GYRO_X_ST_EN                    0x01


/*
 * MPUREG_SCAN0
 * Register Name: SCAN0
*/
#define BIT_DMP_MEM_ACCESS_EN		0x08
#define BIT_MEM_OTP_ACCESS_EN		0x04
#define BIT_FIFO_MEM_RD_SYS			0x02
#define BIT_FIFO_MEM_WR_SER			0x01

/* ----------------------------------------------------------------------------
 * Register bank 1
 * ---------------------------------------------------------------------------- */

#define BIT_SENSOR_CONFIG2_OIS_MODE_POS    4
#define BIT_SENSOR_CONFIG2_OIS_MODE_MASK  (0x03 << BIT_SENSOR_CONFIG2_OIS_MODE_POS)

typedef enum
{
	ICM426XX_SENSOR_CONFIG2_OIS_MODE_32KHZ = (0x2 << BIT_SENSOR_CONFIG2_OIS_MODE_POS),
	ICM426XX_SENSOR_CONFIG2_OIS_MODE_8KHZ  = (0x1 << BIT_SENSOR_CONFIG2_OIS_MODE_POS),
	ICM426XX_SENSOR_CONFIG2_OIS_MODE_DIS   = (0x0 << BIT_SENSOR_CONFIG2_OIS_MODE_POS)
} ICM426XX_SENSOR_CONFIG2_OIS_MODE_t;


/*
 * MPUREG_GYRO_CONFIG_STATIC2_B1
 * Register Name: GYRO_CONFIG_STATIC2
 */

/* GYRO_AAF_DIS */
#define BIT_GYRO_AAF_DIS_POS        1
#define BIT_GYRO_AAF_DIS_MASK   (0x01 << BIT_GYRO_AAF_DIS_POS)

typedef enum
{
	ICM426XX_GYRO_AAF_EN  = (0x0 << BIT_GYRO_AAF_DIS_POS),
	ICM426XX_GYRO_AAF_DIS = (0x1 << BIT_GYRO_AAF_DIS_POS),
} ICM426XX_GYRO_AAF_DIS_t;

/* GYRO_NF_DIS */
#define BIT_GYRO_NF_DIS_POS        0
#define BIT_GYRO_NF_DIS_MASK   (0x01 << BIT_GYRO_NF_DIS_POS)
typedef enum
{
	ICM426XX_GYRO_NF_EN  = (0x0 << BIT_GYRO_NF_DIS_POS),
	ICM426XX_GYRO_NF_DIS = (0x1 << BIT_GYRO_NF_DIS_POS),
} ICM426XX_GYRO_NF_DIS_t;

/*
 * MPUREG_GYRO_CONFIG_STATIC3_B1
 * Register Name: GYRO_CONFIG_STATIC3
 */

/* GYRO_AAF_DELT */
#define BIT_GYRO_AAF_DELT_POS        0
#define BIT_GYRO_AAF_DELT_MASK   (0x3F << BIT_GYRO_AAF_DELT_POS)

/*
 * MPUREG_GYRO_CONFIG_STATIC4_B1
 * Register Name: GYRO_CONFIG_STATIC4
 */

/* GYRO_AAF_DELTSQR */
#define BIT_GYRO_AAF_DELTSQR_POS_LO        0
#define BIT_GYRO_AAF_DELTSQR_MASK_LO   (0xFF << BIT_GYRO_AAF_DELTSQR_POS_LO)

/*
 * MPUREG_GYRO_CONFIG_STATIC5_B1
 * Register Name: GYRO_CONFIG_STATIC5
 */

#define BIT_GYRO_AAF_DELTSQR_POS_HI        0
#define BIT_GYRO_AAF_DELTSQR_MASK_HI   (0x0F << BIT_GYRO_AAF_DELTSQR_POS_HI)

/* GYRO_AAF_BITSHIFT */
#define BIT_GYRO_AAF_BITSHIFT_POS     4
#define BIT_GYRO_AAF_BITSHIFT_MASK   (0x0F << BIT_GYRO_AAF_BITSHIFT_POS)

/*
 * MPUREG_INTF_CONFIG4_B1
 * Register Name: INTF_CONFIG4
 */

/* SPI_AP_4WIRE */
#define BIT_INTF_CONFIG4_AP_SPI_POS       1
#define BIT_INTF_CONFIG4_AP_SPI_MASK   (0x1 << BIT_INTF_CONFIG4_AP_SPI_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG4_AP_SPI4W = (0x1 << BIT_INTF_CONFIG4_AP_SPI_POS),
	ICM426XX_INTF_CONFIG4_AP_SPI3W = (0x0 << BIT_INTF_CONFIG4_AP_SPI_POS),
} ICM426XX_INTF_CONFIG4_AP_SPI_t;

/* SPI_AUX1_4WIRE */
#define BIT_INTF_CONFIG4_AUX1_SPI_POS       2
#define BIT_INTF_CONFIG4_AUX1_SPI_MASK   (0x1 << BIT_INTF_CONFIG4_AUX1_SPI_POS)

typedef enum
{
	ICM426XX_INTF_CONFIG4_AUX1_SPI4W = (0x1 << BIT_INTF_CONFIG4_AUX1_SPI_POS),
	ICM426XX_INTF_CONFIG4_AUX1_SPI3W = (0x0 << BIT_INTF_CONFIG4_AUX1_SPI_POS),
} ICM426XX_INTF_CONFIG4_AUX1_SPI_t;

/*
 * MPUREG_INTF_CONFIG5_B1
 * Register Name: INTF_CONFIG5
 */

/* GPIO_PAD_SEL */
#define BIT_INTF_CONFIG5_GPIO_PAD_SEL_POS       1
#define BIT_INTF_CONFIG5_GPIO_PAD_SEL_MASK   (0x3 << BIT_INTF_CONFIG5_GPIO_PAD_SEL_POS)

/*
 * MPUREG_INTF_CONFIG6_B1
 * Register Name: INTF_CONFIG6
 */

/* I3C_DDR_EN */
#define BIT_INTF_CONFIG6_I3C_DDR_EN_POS       1
#define BIT_INTF_CONFIG6_I3C_DDR_EN_MASK   (0x1 << BIT_INTF_CONFIG6_I3C_DDR_EN_POS)

/* I3C_SDR_EN */
#define BIT_INTF_CONFIG6_I3C_SDR_EN_POS       0
#define BIT_INTF_CONFIG6_I3C_SDR_EN_MASK   (0x1 << BIT_INTF_CONFIG6_I3C_SDR_EN_POS)

/*
 * MPUREG_INTF_CONFIG6_B1
 * Register Name: INTF_CONFIG6
 */

/* I3C_IBI_BYTE_EN */
#define BIT_INTF_CONFIG6_I3C_IBI_BYTE_EN_POS       3
#define BIT_INTF_CONFIG6_I3C_IBI_BYTE_EN_MASK   (0x1 << BIT_INTF_CONFIG6_I3C_IBI_BYTE_EN_POS)

/* I3C_IBI_EN */
#define BIT_INTF_CONFIG6_I3C_IBI_EN_POS       2
#define BIT_INTF_CONFIG6_I3C_IBI_EN_MASK   (0x1 << BIT_INTF_CONFIG6_I3C_IBI_EN_POS)

/* ----------------------------------------------------------------------------
 * Register bank 2
 * ---------------------------------------------------------------------------- */

/*
 * MPUREG_ACCEL_CONFIG_STATIC2_B2
 * Register Name: ACCEL_CONFIG_STATIC2
 */

 /* ACCEL_AAF_DIS */
#define BIT_ACCEL_AAF_DIS_POS        0
#define BIT_ACCEL_AAF_DIS_MASK   (0x01 << BIT_ACCEL_AAF_DIS_POS)

typedef enum
{
	ICM426XX_ACCEL_AAF_EN  = (0x0 << BIT_ACCEL_AAF_DIS_POS),
	ICM426XX_ACCEL_AAF_DIS = (0x1 << BIT_ACCEL_AAF_DIS_POS),
} ICM426XX_ACCEL_AAF_DIS_t;

/* ACCEL_AAF_DELT */
#define BIT_ACCEL_AAF_DELT_POS        1
#define BIT_ACCEL_AAF_DELT_MASK   (0x3F << BIT_ACCEL_AAF_DELT_POS)

/*
 * MPUREG_ACCEL_CONFIG_STATIC3_B2
 * Register Name: ACCEL_CONFIG_STATIC3
 */

/* ACCEL_AAF_DELTSQR */
#define BIT_ACCEL_AAF_DELTSQR_POS_LO        0
#define BIT_ACCEL_AAF_DELTSQR_MASK_LO   (0xFF << BIT_ACCEL_AAF_DELTSQR_POS_LO)

/*
 * MPUREG_ACCEL_CONFIG_STATIC4_B2
 * Register Name: ACCEL_CONFIG_STATIC4
 */
 
/* ACCEL_AAF_DELTSQR */
#define BIT_ACCEL_AAF_DELTSQR_POS_HI        0
#define BIT_ACCEL_AAF_DELTSQR_MASK_HI   (0x0F << BIT_ACCEL_AAF_DELTSQR_POS_HI)

/* ACCEL_AAF_BITSHIFT */
#define BIT_ACCEL_AAF_BITSHIFT_POS     4
#define BIT_ACCEL_AAF_BITSHIFT_MASK   (0x0F << BIT_ACCEL_AAF_BITSHIFT_POS)

/*
 * MPUREG_OIS1_CONFIG1_B2
 * Register Name: OIS1_CONFIG1
 */

/* OIS1_ACCEL_LP_CLK_SEL */
#define BIT_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_POS       5
#define BIT_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_MASK   (0x1 << BIT_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_POS)

typedef enum
{
	ICM426XX_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_WUOSC  = (0x0 << BIT_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_POS),
	ICM426XX_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_RCOSC  = (0x1 << BIT_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_POS),
} ICM426XX_OIS1_CONFIG1_ACCEL_LP_CLK_SEL_t;

/* OIS1_DEC */
#define BIT_OIS1_CONFIG1_DEC_POS       2
#define BIT_OIS1_CONFIG1_DEC_MASK   (0x7 << BIT_OIS1_CONFIG1_DEC_POS)

/** @brief OIS1 rate selection (base clock fixed by OTP divided by decimator value)
 */
typedef enum
{
	ICM426XX_OIS1_CONFIG1_DEC_1  = (0x0 << BIT_OIS1_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 1 */
	ICM426XX_OIS1_CONFIG1_DEC_2  = (0x1 << BIT_OIS1_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 2 */
	ICM426XX_OIS1_CONFIG1_DEC_4  = (0x2 << BIT_OIS1_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 4 */
	ICM426XX_OIS1_CONFIG1_DEC_8  = (0x3 << BIT_OIS1_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 8 */
	ICM426XX_OIS1_CONFIG1_DEC_16 = (0x4 << BIT_OIS1_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 16 */
	ICM426XX_OIS1_CONFIG1_DEC_32 = (0x5 << BIT_OIS1_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 32 */
} ICM426XX_OIS1_CONFIG1_DEC_t;

/* GYRO_OIS1_EN */
#define BIT_OIS1_CONFIG1_GYRO_EN_POS       1
#define BIT_OIS1_CONFIG1_GYRO_EN_MASK   (0x1 << BIT_OIS1_CONFIG1_GYRO_EN_POS)

typedef enum
{
	ICM426XX_OIS1_CONFIG1_GYRO_EN  = (0x1 << BIT_OIS1_CONFIG1_GYRO_EN_POS),
	ICM426XX_OIS1_CONFIG1_GYRO_DIS = (0x0 << BIT_OIS1_CONFIG1_GYRO_EN_POS),
} ICM426XX_OIS1_CONFIG1_GYRO_EN_t;

/* ACCEL_OIS1_EN */
#define BIT_OIS1_CONFIG1_ACCEL_EN_POS       0
#define BIT_OIS1_CONFIG1_ACCEL_EN_MASK   (0x1 << BIT_OIS1_CONFIG1_ACCEL_EN_POS)

typedef enum
{
	ICM426XX_OIS1_CONFIG1_ACCEL_EN  = (0x1 << BIT_OIS1_CONFIG1_ACCEL_EN_POS),
	ICM426XX_OIS1_CONFIG1_ACCEL_DIS = (0x0 << BIT_OIS1_CONFIG1_ACCEL_EN_POS),
} ICM426XX_OIS1_CONFIG1_ACCEL_EN_t;

/*
 * MPUREG_OIS1_CONFIG2_B2
 * Register Name: OIS1_CONFIG2
 */

/* GYRO_OIS1_FS_SEL */
#define BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS       3
#define BIT_OIS1_CONFIG2_GYRO_FS_SEL_MASK   (0x7 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS)

/** @brief OIS1 gyroscope FSR selection
 */
typedef enum
{
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_16dps   = (0x7 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 15.625 dps */
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_31dps   = (0x6 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 31.25 dps */
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_62dps   = (0x5 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 62.5 dps */
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_125dps  = (0x4 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 125 dps */
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_250dps  = (0x3 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 250 dps */
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_500dps  = (0x2 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 500 dps */
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_1000dps = (0x1 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 1000 dps */
	ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_2000dps = (0x0 << BIT_OIS1_CONFIG2_GYRO_FS_SEL_POS), /**< 2000 dps */
} ICM426XX_OIS1_CONFIG2_GYRO_FS_SEL_t;

/* ACCEL_OIS1_FS_SEL */
#define BIT_OIS1_CONFIG2_ACCEL_FS_SEL_POS       0
#define BIT_OIS1_CONFIG2_ACCEL_FS_SEL_MASK   (0x7 << BIT_OIS1_CONFIG2_ACCEL_FS_SEL_POS)

/** @brief OIS1 accelerometer FSR selection
 */
typedef enum
{
	ICM426XX_OIS1_CONFIG2_ACCEL_FS_SEL_RESERVED  = (0x4 << BIT_OIS1_CONFIG2_ACCEL_FS_SEL_POS),
	ICM426XX_OIS1_CONFIG2_ACCEL_FS_SEL_2g        = (0x3 << BIT_OIS1_CONFIG2_ACCEL_FS_SEL_POS), /**< 2g */
	ICM426XX_OIS1_CONFIG2_ACCEL_FS_SEL_4g        = (0x2 << BIT_OIS1_CONFIG2_ACCEL_FS_SEL_POS), /**< 4g */
	ICM426XX_OIS1_CONFIG2_ACCEL_FS_SEL_8g        = (0x1 << BIT_OIS1_CONFIG2_ACCEL_FS_SEL_POS), /**< 8g */
	ICM426XX_OIS1_CONFIG2_ACCEL_FS_SEL_16g       = (0x0 << BIT_OIS1_CONFIG2_ACCEL_FS_SEL_POS), /**< 16g */
} ICM426XX_OIS1_CONFIG2_ACCEL_FS_SEL_t;

/*
 * MPUREG_INT_STATUS_OIS1_B2
 * Register Name: INT_STATUS_OIS1
 */
#define BIT_INT_STATUS_OIS1_FSYNC		0x04
#define BIT_INT_STATUS_OIS1_DRDY		0x02
#define BIT_INT_STATUS_OIS1_AGC_RDY		0x01

/*
 * MPUREG_OIS2_CONFIG1_B2
 * Register Name: OIS2_CONFIG1
 */

/* OIS2_ACCEL_LP_CLK_SEL */
#define BIT_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_POS       5
#define BIT_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_MASK   (0x1 << BIT_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_POS)

typedef enum
{
	ICM426XX_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_WUOSC  = (0x0 << BIT_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_POS),
	ICM426XX_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_RCOSC  = (0x1 << BIT_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_POS),
} ICM426XX_OIS2_CONFIG1_ACCEL_LP_CLK_SEL_t;

/* OIS2_DEC */
#define BIT_OIS2_CONFIG1_DEC_POS       2
#define BIT_OIS2_CONFIG1_DEC_MASK   (0x7 << BIT_OIS2_CONFIG1_DEC_POS)

/** @brief OIS2 rate selection (base clock fixed by OTP divided by decimator value)
 */
typedef enum
{
	ICM426XX_OIS2_CONFIG1_DEC_1  = (0x0 << BIT_OIS2_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 1 */
	ICM426XX_OIS2_CONFIG1_DEC_2  = (0x1 << BIT_OIS2_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 2 */
	ICM426XX_OIS2_CONFIG1_DEC_4  = (0x2 << BIT_OIS2_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 4 */
	ICM426XX_OIS2_CONFIG1_DEC_8  = (0x3 << BIT_OIS2_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 8 */
	ICM426XX_OIS2_CONFIG1_DEC_16 = (0x4 << BIT_OIS2_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 16 */
	ICM426XX_OIS2_CONFIG1_DEC_32 = (0x5 << BIT_OIS2_CONFIG1_DEC_POS), /**< OTP_OIS_clock / 32 */
} ICM426XX_OIS2_CONFIG1_DEC_t;

/* GYRO_OIS2_EN */
#define BIT_OIS2_CONFIG1_GYRO_EN_POS       1
#define BIT_OIS2_CONFIG1_GYRO_EN_MASK   (0x1 << BIT_OIS2_CONFIG1_GYRO_EN_POS)

typedef enum
{
	ICM426XX_OIS2_CONFIG1_GYRO_EN  = (0x1 << BIT_OIS2_CONFIG1_GYRO_EN_POS),
	ICM426XX_OIS2_CONFIG1_GYRO_DIS = (0x0 << BIT_OIS2_CONFIG1_GYRO_EN_POS),
} ICM426XX_OIS2_CONFIG1_GYRO_EN_t;

/* ACCEL_OIS2_EN */
#define BIT_OIS2_CONFIG1_ACCEL_EN_POS       0
#define BIT_OIS2_CONFIG1_ACCEL_EN_MASK   (0x1 << BIT_OIS2_CONFIG1_ACCEL_EN_POS)

typedef enum
{
	ICM426XX_OIS2_CONFIG1_ACCEL_EN  = (0x1 << BIT_OIS2_CONFIG1_ACCEL_EN_POS),
	ICM426XX_OIS2_CONFIG1_ACCEL_DIS = (0x0 << BIT_OIS2_CONFIG1_ACCEL_EN_POS),
} ICM426XX_OIS2_CONFIG1_ACCEL_EN_t;

/*
 * MPUREG_OIS2_CONFIG2_B2
 * Register Name: OIS2_CONFIG2
 */

/* GYRO_OIS2_FS_SEL */
#define BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS       3
#define BIT_OIS2_CONFIG2_GYRO_FS_SEL_MASK   (0x7 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS)

/** @brief OIS2 gyroscope FSR selection
 */
typedef enum
{
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_16dps   = (0x7 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 15.625 dps */
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_31dps   = (0x6 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 31.25 dps */
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_62dps   = (0x5 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 62.5 dps */
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_125dps  = (0x4 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 125 dps */
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_250dps  = (0x3 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 250 dps */
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_500dps  = (0x2 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 500 dps */
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_1000dps = (0x1 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 1000 dps */
	ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_2000dps = (0x0 << BIT_OIS2_CONFIG2_GYRO_FS_SEL_POS), /**< 2000 dps */
} ICM426XX_OIS2_CONFIG2_GYRO_FS_SEL_t;

/* ACCEL_OIS2_FS_SEL */
#define BIT_OIS2_CONFIG2_ACCEL_FS_SEL_POS       0
#define BIT_OIS2_CONFIG2_ACCEL_FS_SEL_MASK   (0x7 << BIT_OIS2_CONFIG2_ACCEL_FS_SEL_POS)

/** @brief OIS2 accelerometer FSR selection
 */
typedef enum
{
	ICM426XX_OIS2_CONFIG2_ACCEL_FS_SEL_RESERVED  = (0x4 << BIT_OIS2_CONFIG2_ACCEL_FS_SEL_POS),
	ICM426XX_OIS2_CONFIG2_ACCEL_FS_SEL_2g        = (0x3 << BIT_OIS2_CONFIG2_ACCEL_FS_SEL_POS), /**< 2g */
	ICM426XX_OIS2_CONFIG2_ACCEL_FS_SEL_4g        = (0x2 << BIT_OIS2_CONFIG2_ACCEL_FS_SEL_POS), /**< 4g */
	ICM426XX_OIS2_CONFIG2_ACCEL_FS_SEL_8g        = (0x1 << BIT_OIS2_CONFIG2_ACCEL_FS_SEL_POS), /**< 8g */
	ICM426XX_OIS2_CONFIG2_ACCEL_FS_SEL_16g       = (0x0 << BIT_OIS2_CONFIG2_ACCEL_FS_SEL_POS), /**< 16g */
} ICM426XX_OIS2_CONFIG2_ACCEL_FS_SEL_t;


/*
 * MPUREG_INT_STATUS_OIS2_B2
 * Register Name: INT_STATUS_OIS2
 */
#define BIT_INT_STATUS_OIS2_FSYNC		0x04
#define BIT_INT_STATUS_OIS2_DRDY		0x02
#define BIT_INT_STATUS_OIS2_AGC_RDY		0x01


/* ----------------------------------------------------------------------------
 * Register bank 4
 * ---------------------------------------------------------------------------- */

/*
 * MPUREG_FDR_CONFIG_B4
 * Register Name: FDR_CONFIG
*/

/* FDR_SEL */

#define BIT_FDR_CONFIG_FDR_SEL_POS   0
#define BIT_FDR_CONFIG_FDR_SEL_MASK  0x7F

/*
 * MPUREG_APEX_CONFIG1_B4
 * Register Name: APEX_CONFIG1
*/

/* DMP_POWER_SAVE_TIME_SEL */

#define BIT_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_POS   0
#define BIT_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_MASK  0x0F

typedef enum
{
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_0S  = 0x0,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_4S  = 0x1,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_8S  = 0x2,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_12S = 0x3,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_16S = 0x4,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_20S = 0x5,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_24S = 0x6,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_28S = 0x7,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_32S = 0x8,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_36S = 0x9,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_40S = 0xA,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_44S = 0xB,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_48S = 0xC,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_52S = 0xD,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_56S = 0xE,
	ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_SEL_60S = 0xF
} ICM426XX_APEX_CONFIG1_DMP_POWER_SAVE_TIME_t;

/* LOW_ENERGY_AMP_TH_SEL */

#define BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS    4
#define BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_MASK  (0x0F << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_30MG  = (0 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_35MG  = (1 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_40MG  = (2 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_45MG  = (3 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_50MG  = (4 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_55MG  = (5 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_60MG  = (6 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_65MG  = (7 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_70MG  = (8 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_75MG  = (9 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_80MG  = (10 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_85MG  = (11 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_90MG  = (12 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_95MG  = (13 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_100MG = (14 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS),
	ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_105MG = (15 << BIT_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_POS)
} ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_t;

// Retro-compatibility
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_1006632MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_30MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_1174405MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_35MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_1342177MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_40MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_1509949MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_45MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_1677721MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_50MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_1845493MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_55MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_2013265MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_60MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_2181038MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_65MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_2348810MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_70MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_2516582MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_75MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_2684354MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_80MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_2852126MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_85MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_3019898MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_90MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_3187671MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_95MG 
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_3355443MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_100MG
#define ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_3523215MG ICM426XX_APEX_CONFIG1_LOW_ENERGY_AMP_TH_SEL_105MG

/*
 * MPUREG_APEX_CONFIG2_B4
 * Register Name: APEX_CONFIG2
*/

/* PEDO_AMP_TH_SEL */

#define BIT_APEX_CONFIG2_PEDO_AMP_TH_POS   4
#define BIT_APEX_CONFIG2_PEDO_AMP_TH_MASK (0x0F<<BIT_APEX_CONFIG2_PEDO_AMP_TH_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_30MG = (0  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_34MG = (1  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_38MG = (2  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_42MG = (3  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_46MG = (4  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_50MG = (5  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_54MG = (6  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_58MG = (7  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_62MG = (8  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_66MG = (9  << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_70MG = (10 << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_74MG = (11 << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_78MG = (12 << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_82MG = (13 << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_86MG = (14 << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS),
	ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_90MG = (15 << BIT_APEX_CONFIG2_PEDO_AMP_TH_POS)
} ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_t;

// Retro-compatibility
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1006632_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_30MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1140850_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_34MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1275068_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_38MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1409286_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_42MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1543503_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_46MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1677721_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_50MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1811939_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_54MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_1946157_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_58MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_2080374_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_62MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_2214592_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_66MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_2348810_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_70MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_2483027_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_74MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_2617245_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_78MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_2751463_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_82MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_2885681_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_86MG
#define ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_3019898_MG ICM426XX_APEX_CONFIG2_PEDO_AMP_TH_90MG


/* PEDO_STEP_CNT_TH_SEL */

#define BIT_APEX_CONFIG2_PEDO_STEP_CNT_TH_POS  0
#define BIT_APEX_CONFIG2_PEDO_STEP_CNT_TH_MASK 0x0F

/*
 * MPUREG_APEX_CONFIG3_B4
 * Register Name: APEX_CONFIG3
*/

/* PEDO_STEP_DET_TH_SEL */

#define BIT_APEX_CONFIG3_PEDO_STEP_DET_TH_POS   5
#define BIT_APEX_CONFIG3_PEDO_STEP_DET_TH_MASK (0x07<<BIT_APEX_CONFIG3_PEDO_STEP_DET_TH_POS)

/* PEDO_SB_TIMER_TH_SEL */

#define BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS   2
#define BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_MASK (0x07<<BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_50_SAMPLES  = (0  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS),
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_75_SAMPLES  = (1  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS),
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_100_SAMPLES = (2  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS),
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_125_SAMPLES = (3  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS),
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_150_SAMPLES = (4  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS),
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_175_SAMPLES = (5  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS),
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_200_SAMPLES = (6  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS),
	ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_225_SAMPLES = (7  << BIT_APEX_CONFIG3_PEDO_SB_TIMER_TH_POS)
} ICM426XX_APEX_CONFIG3_PEDO_SB_TIMER_TH_t;

/* PEDO_HI_ENRGY_TH_SEL */

#define BIT_APEX_CONFIG3_PEDO_HI_ENRGY_TH_POS   0
#define BIT_APEX_CONFIG3_PEDO_HI_ENRGY_TH_MASK  0x03

typedef enum
{
	ICM426XX_APEX_CONFIG3_PEDO_HI_ENRGY_TH_90  = 0x0,
	ICM426XX_APEX_CONFIG3_PEDO_HI_ENRGY_TH_107 = 0x1,
	ICM426XX_APEX_CONFIG3_PEDO_HI_ENRGY_TH_136 = 0x2,
	ICM426XX_APEX_CONFIG3_PEDO_HI_ENRGY_TH_159 = 0x3
} ICM426XX_APEX_CONFIG3_PEDO_HI_ENRGY_TH_t;

/*
 * MPUREG_APEX_CONFIG4_B4
 * Register Name: APEX_CONFIG4
*/

/* TILT_WAIT_TIME_SEL */

#define BIT_APEX_CONFIG4_TILT_WAIT_TIME_POS   6
#define BIT_APEX_CONFIG4_TILT_WAIT_TIME_MASK (0x03<<BIT_APEX_CONFIG4_TILT_WAIT_TIME_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG4_TILT_WAIT_TIME_0S = (0  << BIT_APEX_CONFIG4_TILT_WAIT_TIME_POS),
	ICM426XX_APEX_CONFIG4_TILT_WAIT_TIME_2S = (1  << BIT_APEX_CONFIG4_TILT_WAIT_TIME_POS),
	ICM426XX_APEX_CONFIG4_TILT_WAIT_TIME_4S = (2  << BIT_APEX_CONFIG4_TILT_WAIT_TIME_POS),
	ICM426XX_APEX_CONFIG4_TILT_WAIT_TIME_6S = (3  << BIT_APEX_CONFIG4_TILT_WAIT_TIME_POS)
} ICM426XX_APEX_CONFIG4_TILT_WAIT_TIME_t;

#if defined(ICM_FAMILY_BPLUS)
/* R2W_SLEEP_TIME_OUT */

#define BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS   3
#define BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_MASK (0x07<<BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_1_28S  = (0  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS),
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_2_56S  = (1  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS),
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_3_84S  = (2  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS),
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_5_12S  = (3  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS),
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_6_4S   = (4  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS),
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_7_68S  = (5  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS),
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_8_96S  = (6  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS),
	ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_10_24S = (7  << BIT_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_POS)
} ICM426XX_APEX_CONFIG4_R2W_SLEEP_TIME_OUT_t;

/*
 * MPUREG_APEX_CONFIG5_B4
 * Register Name: APEX_CONFIG5
*/

/* R2W_MOUNTING_MATRIX */

#define BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS   0
#define BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_MASK (0x07<<BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_0 = (0  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS),
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_1 = (1  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS),
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_2 = (2  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS),
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_3 = (3  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS),
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_4 = (4  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS),
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_5 = (5  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS),
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_6 = (6  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS),
	ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_7 = (7  << BIT_APEX_CONFIG5_R2W_MOUNTING_MATRIX_POS)
} ICM426XX_APEX_CONFIG5_R2W_MOUNTING_MATRIX_t;

/*
 * MPUREG_APEX_CONFIG6_B4
 * Register Name: APEX_CONFIG6
*/

/* R2W_SLEEP_GEST_DELAY */

#define BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS   0
#define BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_MASK (0x07<<BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_0_32S  = (0  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS),
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_0_64S  = (1  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS),
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_0_96S  = (2  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS),
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_1_28S  = (3  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS),
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_1_6S   = (4  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS),
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_1_92S  = (5  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS),
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_2_24S  = (6  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS),
	ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_2_56S  = (7  << BIT_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_POS)
} ICM426XX_APEX_CONFIG6_R2W_SLEEP_GEST_DELAY_t;

#elif defined(ICM_FAMILY_CPLUS)

/* LOWG_PEAK_TH_HYST */

#define BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS   3
#define BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_MASK (0x07<<BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_31MG  = (0  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_63MG  = (1  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_94MG  = (2  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_125MG = (3  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_156MG = (4  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_188MG = (5  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_219MG = (6  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_250MG = (7  << BIT_APEX_CONFIG4_LOWG_PEAK_TH_HYST_POS)
} ICM426XX_APEX_CONFIG4_LOWG_PEAK_TH_HYST_t;

/* HIGHG_PEAK_TH_HYST */

#define BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS   0
#define BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_MASK (0x07<<BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_31MG  = (0  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_63MG  = (1  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_94MG  = (2  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_125MG = (3  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_156MG = (4  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_188MG = (5  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_219MG = (6  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS),
	ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_250MG = (7  << BIT_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_POS)
} ICM426XX_APEX_CONFIG4_HIGHG_PEAK_TH_HYST_t;

/*
 * MPUREG_APEX_CONFIG5_B4
 * Register Name: APEX_CONFIG5
*/

/* LOWG_PEAK_TH */

#define BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS   3
#define BIT_APEX_CONFIG5_LOWG_PEAK_TH_MASK (0x1f<<BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_31MG    = (0x00 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_63MG    = (0x01 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_94MG    = (0x02 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_125MG   = (0x03 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_156MG   = (0x04 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_188MG   = (0x05 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_219MG   = (0x06 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_250MG   = (0x07 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_281MG   = (0x08 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_313MG   = (0x09 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_344MG   = (0x0A << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_375MG   = (0x0B << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_406MG   = (0x0C << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_438MG   = (0x0D << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_469MG   = (0x0E << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_500MG   = (0x0F << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_531MG   = (0x10 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_563MG   = (0x11 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_594MG   = (0x12 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_625MG   = (0x13 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_656MG   = (0x14 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_688MG   = (0x15 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_719MG   = (0x16 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_750MG   = (0x17 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_781MG   = (0x18 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_813MG   = (0x19 << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_844MG   = (0x1A << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_875MG   = (0x1B << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_906MG   = (0x1C << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_938MG   = (0x1D << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_969MG   = (0x1E << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_1000MG  = (0x1F << BIT_APEX_CONFIG5_LOWG_PEAK_TH_POS)
} ICM426XX_APEX_CONFIG5_LOWG_PEAK_TH_t;

/* LOWG_TIME_TH */

#define BIT_APEX_CONFIG5_LOWG_TIME_TH_POS   0
#define BIT_APEX_CONFIG5_LOWG_TIME_TH_MASK (0x07<<BIT_APEX_CONFIG5_LOWG_TIME_TH_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_1_SAMPLE  = (0x00 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_2_SAMPLES = (0x01 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_3_SAMPLES = (0x02 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_4_SAMPLES = (0x03 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_5_SAMPLES = (0x04 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_6_SAMPLES = (0x05 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_7_SAMPLES = (0x06 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_8_SAMPLES = (0x07 << BIT_APEX_CONFIG5_LOWG_TIME_TH_POS)
} ICM426XX_APEX_CONFIG5_LOWG_TIME_TH_SAMPLES_t;

/*
 * MPUREG_APEX_CONFIG6_B4
 * Register Name: APEX_CONFIG6
*/
/* HIGHG_PEAK_TH */

#define BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS   3
#define BIT_APEX_CONFIG6_HIGHG_PEAK_TH_MASK (0x1f<<BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_250MG  = (0x00 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_500MG  = (0x01 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_750MG  = (0x02 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_1000MG = (0x03 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_1250MG = (0x04 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_1500MG = (0x05 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_1750MG = (0x06 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_2000MG = (0x07 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_2250MG = (0x08 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_2500MG = (0x09 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_2750MG = (0x0A << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_3000MG = (0x0B << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_3250MG = (0x0C << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_3500MG = (0x0D << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_3750MG = (0x0E << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_4000MG = (0x0F << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_4250MG = (0x10 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_4500MG = (0x11 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_4750MG = (0x12 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_5000MG = (0x13 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_5250MG = (0x14 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_5500MG = (0x15 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_5750MG = (0x16 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_6000MG = (0x17 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_6250MG = (0x18 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_6500MG = (0x19 << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_6750MG = (0x1A << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_7000MG = (0x1B << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_7250MG = (0x1C << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_7500MG = (0x1D << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS), 
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_7750MG = (0x1E << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_8000MG = (0x1F << BIT_APEX_CONFIG6_HIGHG_PEAK_TH_POS)
} ICM426XX_APEX_CONFIG6_HIGHG_PEAK_TH_t;

/* HIGHG_TIME_TH */

#define BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS   0
#define BIT_APEX_CONFIG6_HIGHG_TIME_TH_MASK (0x07<<BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_1_SAMPLE  = (0x00 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_2_SAMPLES = (0x01 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_3_SAMPLES = (0x02 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_4_SAMPLES = (0x03 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_5_SAMPLES = (0x04 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_6_SAMPLES = (0x05 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_7_SAMPLES = (0x06 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS),
	ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_8_SAMPLES = (0x07 << BIT_APEX_CONFIG6_HIGHG_TIME_TH_POS)
} ICM426XX_APEX_CONFIG6_HIGHG_TIME_TH_SAMPLES_t;

#endif

/*
 * MPUREG_APEX_CONFIG7_B4
 * Register Name: APEX_CONFIG7
*/

/* TAP_MIN_JERK_THR */
#define BIT_APEX_CONFIG7_TAP_MIN_JERK_THR_POS        2
#define BIT_APEX_CONFIG7_TAP_MIN_JERK_THR_MASK   (0x3F << BIT_APEX_CONFIG7_TAP_MIN_JERK_THR_POS)

#define ICM426XX_APEX_CONFIG7_TAP_MIN_JERK_THR_281MG_DEFAULT    0x11

/* TAP_MAX_PEAK_TOL */
#define BIT_APEX_CONFIG7_TAP_MAX_PEAK_TOL_POS      0
#define BIT_APEX_CONFIG7_TAP_MAX_PEAK_TOL_MASK   0x3

typedef enum
{
	ICM426XX_APEX_CONFIG7_TAP_MAX_PEAK_TOL_12 = 0x0,
	ICM426XX_APEX_CONFIG7_TAP_MAX_PEAK_TOL_25 = 0x1,
	ICM426XX_APEX_CONFIG7_TAP_MAX_PEAK_TOL_37 = 0x2,
	ICM426XX_APEX_CONFIG7_TAP_MAX_PEAK_TOL_50 = 0x3
} ICM426XX_APEX_CONFIG7_TAP_MAX_PEAK_TOL_t;

/*
 * MPUREG_APEX_CONFIG8_B4
 * Register Name: APEX_CONFIG8
*/ 

/* TAP_TMAX */
#define BIT_APEX_CONFIG8_TAP_TMAX_POS       5
#define BIT_APEX_CONFIG8_TAP_TMAX_MASK   (0x03 << BIT_APEX_CONFIG8_TAP_TMAX_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG8_TAP_TMAX_250MS = (0 << BIT_APEX_CONFIG8_TAP_TMAX_POS),
	ICM426XX_APEX_CONFIG8_TAP_TMAX_375MS = (1 << BIT_APEX_CONFIG8_TAP_TMAX_POS),
	ICM426XX_APEX_CONFIG8_TAP_TMAX_500MS = (2 << BIT_APEX_CONFIG8_TAP_TMAX_POS),
	ICM426XX_APEX_CONFIG8_TAP_TMAX_625MS = (3 << BIT_APEX_CONFIG8_TAP_TMAX_POS)
} ICM426XX_APEX_CONFIG8_TAP_TMAX_t;

/* TAP_TAVG */
#define BIT_APEX_CONFIG8_TAP_TAVG_POS       3
#define BIT_APEX_CONFIG8_TAP_TAVG_MASK   (0x03 << BIT_APEX_CONFIG8_TAP_TAVG_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG8_TAP_TAVG_1SAMPLE = (0 << BIT_APEX_CONFIG8_TAP_TAVG_POS),
	ICM426XX_APEX_CONFIG8_TAP_TAVG_2SAMPLE = (1 << BIT_APEX_CONFIG8_TAP_TAVG_POS),
	ICM426XX_APEX_CONFIG8_TAP_TAVG_4SAMPLE = (2 << BIT_APEX_CONFIG8_TAP_TAVG_POS),
	ICM426XX_APEX_CONFIG8_TAP_TAVG_8SAMPLE = (3 << BIT_APEX_CONFIG8_TAP_TAVG_POS)
} ICM426XX_APEX_CONFIG8_TAP_TAVG_t;

/* TAP_TMIN */
#define BIT_APEX_CONFIG8_TAP_TMIN_POS       0
#define BIT_APEX_CONFIG8_TAP_TMIN_MASK   0x07

typedef enum
{
	ICM426XX_APEX_CONFIG8_TAP_TMIN_125MS = 0x0,
	ICM426XX_APEX_CONFIG8_TAP_TMIN_140MS = 0x1,
	ICM426XX_APEX_CONFIG8_TAP_TMIN_156MS = 0x2,
	ICM426XX_APEX_CONFIG8_TAP_TMIN_171MS = 0x3,
	ICM426XX_APEX_CONFIG8_TAP_TMIN_187MS = 0x4,
	ICM426XX_APEX_CONFIG8_TAP_TMIN_203MS = 0x5,
	ICM426XX_APEX_CONFIG8_TAP_TMIN_218MS = 0x6,
	ICM426XX_APEX_CONFIG8_TAP_TMIN_234MS = 0x7
} ICM426XX_APEX_CONFIG8_TAP_TMIN_t;

/*
 * MPUREG_APEX_CONFIG9_B4
 * Register Name: APEX_CONFIG9
*/
#define BIT_APEX_CONFIG9_SENSITIVITY_MODE_POS       0
#define BIT_APEX_CONFIG9_SENSITIVITY_MODE_MASK   0x01
typedef enum
{
	ICM426XX_APEX_CONFIG9_SENSITIVITY_MODE_NORMAL   = 0x00,
	ICM426XX_APEX_CONFIG9_SENSITIVITY_MODE_RESERVED = 0x01
} ICM426XX_APEX_CONFIG9_SENSITIVITY_MODE_t;

#if defined(ICM_FAMILY_CPLUS)

/*
 * MPUREG_APEX_CONFIG10_B4
 * Register Name: APEX_CONFIG10
*/

/* FF_DEBOUNCE_DURATION */
#define BIT_APEX_CONFIG10_FF_DEBOUNCE_DURATION_POS     0
#define BIT_APEX_CONFIG10_FF_DEBOUNCE_DURATION_MASK   (0x03 << BIT_APEX_CONFIG10_FF_DEBOUNCE_DURATION_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG10_FF_DEBOUNCE_DURATION_0_MS = (0  << BIT_APEX_CONFIG10_FF_DEBOUNCE_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_DEBOUNCE_DURATION_1000_MS = (1  << BIT_APEX_CONFIG10_FF_DEBOUNCE_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_DEBOUNCE_DURATION_2000_MS = (2  << BIT_APEX_CONFIG10_FF_DEBOUNCE_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_DEBOUNCE_DURATION_3000_MS = (3  << BIT_APEX_CONFIG10_FF_DEBOUNCE_DURATION_POS)
} ICM426XX_APEX_CONFIG10_FF_DEBOUNCE_DURATION_t;

/* FF_MAX_DURATION */
#define BIT_APEX_CONFIG10_FF_MAX_DURATION_POS     2
#define BIT_APEX_CONFIG10_FF_MAX_DURATION_MASK   (0x07 << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_113_CM = (0  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_154_CM = (1  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_201_CM = (2  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_255_CM = (3  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_314_CM = (4  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_380_CM = (5  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_452_CM = (6  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_531_CM = (7  << BIT_APEX_CONFIG10_FF_MAX_DURATION_POS)
} ICM426XX_APEX_CONFIG10_FF_MAX_DURATION_t;

/* FF_MIN_DURATION */
#define BIT_APEX_CONFIG10_FF_MIN_DURATION_POS     5
#define BIT_APEX_CONFIG10_FF_MIN_DURATION_MASK   (0x07 << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS)

typedef enum
{
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_13_CM = (0  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_19_CM = (1  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_28_CM = (2  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_38_CM = (3  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_50_CM = (4  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_64_CM = (5  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_78_CM = (6  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS),
	ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_95_CM = (7  << BIT_APEX_CONFIG10_FF_MIN_DURATION_POS)
} ICM426XX_APEX_CONFIG10_FF_MIN_DURATION_t;

#endif

/*
 * MPUREG_INT_SOURCE6_B4
 * Register Name: INT_SOURCE6
 */
#define BIT_INT_STEP_DET_INT_EN_POS      5
#define BIT_INT_STEP_CNT_OVFL_INT_EN_POS 4
#define BIT_INT_TILT_DET_INT_EN_POS      3
#if defined(ICM_FAMILY_BPLUS)
#define BIT_INT_WAKE_DET_INT_EN_POS      2
#define BIT_INT_SLEEP_DET_INT_EN_POS     1
#elif defined(ICM_FAMILY_CPLUS)
#define BIT_INT_LOWG_DET_INT_EN_POS      2
#define BIT_INT_FF_DET_INT_EN_POS        1
#endif
#define BIT_INT_TAP_DET_INT_EN_POS       0

#define BIT_INT_SOURCE6_STEP_DET_INT1_EN      0x20
#define BIT_INT_SOURCE6_STEP_CNT_OVFL_INT1_EN 0x10
#define BIT_INT_SOURCE6_TILT_DET_INT1_EN      0x8
#if defined(ICM_FAMILY_BPLUS)
#define BIT_INT_SOURCE6_WAKE_DET_INT1_EN      0x4
#define BIT_INT_SOURCE6_SLEEP_DET_INT1_EN     0x2
#elif defined(ICM_FAMILY_CPLUS)
#define BIT_INT_SOURCE6_LOWG_DET_INT1_EN      0x4
#define BIT_INT_SOURCE6_FF_DET_INT1_EN        0x2
#endif
#define BIT_INT_SOURCE6_TAP_DET_INT1_EN       0x1

/*
 * MPUREG_INT_SOURCE7_B4
 * Register Name: INT_SOURCE7
 */
#define BIT_INT_SOURCE7_STEP_DET_INT2_EN      0x20
#define BIT_INT_SOURCE7_STEP_CNT_OVFL_INT2_EN 0x10
#define BIT_INT_SOURCE7_TILT_DET_INT2_EN      0x8
#if defined(ICM_FAMILY_BPLUS)
#define BIT_INT_SOURCE7_WAKE_DET_INT2_EN      0x4
#define BIT_INT_SOURCE7_SLEEP_DET_INT2_EN     0x2
#elif defined(ICM_FAMILY_CPLUS)
#define BIT_INT_SOURCE7_LOWG_DET_INT2_EN      0x4
#define BIT_INT_SOURCE7_FF_DET_INT2_EN        0x2
#endif
#define BIT_INT_SOURCE7_TAP_DET_INT2_EN       0x1

/*
 * MPUREG_INT_SOURCE8_B4
 * Register Name: INT_SOURCE8
 */
#define BIT_INT_OIS1_DRDY_IBI_EN_POS  6
#define BIT_INT_UI_FSYNC_IBI_EN_POS   5
#define BIT_INT_PLL_RDY_IBI_EN_POS    4
#define BIT_INT_UI_DRDY_IBI_EN_POS    3
#define BIT_INT_FIFO_THS_IBI_EN_POS   2
#define BIT_INT_FIFO_FULL_IBI_EN_POS  1
#define BIT_INT_UI_AGC_RDY_IBI_EN_POS 0

#define BIT_INT_SOURCE8_OIS1_DRDY_IBI_EN  0x40
#define BIT_INT_SOURCE8_UI_FSYNC_IBI_EN   0x20
#define BIT_INT_SOURCE8_PLL_RDY_IBI_EN    0x10
#define BIT_INT_SOURCE8_UI_DRDY_IBI_EN    0x08
#define BIT_INT_SOURCE8_FIFO_THS_IBI_EN   0x04
#define BIT_INT_SOURCE8_FIFO_FULL_IBI_EN  0x02
#define BIT_INT_SOURCE8_UI_AGC_RDY_IBI_EN 0x01

/*
 * MPUREG_INT_SOURCE9_B4
 * Register Name: INT_SOURCE9
 */
#define BIT_INT_SMD_IBI_EN_POS                 4
#define BIT_INT_WOM_Z_IBI_EN_POS               3
#define BIT_INT_WOM_Y_IBI_EN_POS               2
#define BIT_INT_WOM_X_IBI_EN_POS               1

#define BIT_INT_SOURCE9_SMD_IBI_EN             0x10
#define BIT_INT_SOURCE9_WOM_Z_IBI_EN           0x08
#define BIT_INT_SOURCE9_WOM_Y_IBI_EN           0x04
#define BIT_INT_SOURCE9_WOM_X_IBI_EN           0x02

/*
 * MPUREG_INT_SOURCE10_B4
 * Register Name: INT_SOURCE10
 */
#define BIT_INT_STEP_DET_IBI_EN_POS      5
#define BIT_INT_STEP_CNT_OVFL_IBI_EN_POS 4
#define BIT_INT_TILT_DET_IBI_EN_POS      3
#if defined(ICM_FAMILY_BPLUS)
#define BIT_INT_WAKE_DET_IBI_EN_POS      2
#define BIT_INT_SLEEP_DET_IBI_EN_POS     1
#elif defined(ICM_FAMILY_CPLUS)
#define BIT_INT_LOWG_DET_IBI_EN_POS      2
#define BIT_INT_FF_DET_IBI_EN_POS        1
#endif
#define BIT_INT_TAP_DET_IBI_EN_POS       0

#define BIT_INT_SOURCE10_STEP_DET_IBI_EN      0x20
#define BIT_INT_SOURCE10_STEP_CNT_OVFL_IBI_EN 0x10
#define BIT_INT_SOURCE10_TILT_DET_IBI_EN      0x08
#if defined(ICM_FAMILY_BPLUS)
#define BIT_INT_SOURCE10_WAKE_DET_IBI_EN      0x04
#define BIT_INT_SOURCE10_SLEEP_DET_IBI_EN     0x02
#elif defined(ICM_FAMILY_CPLUS)
#define BIT_INT_SOURCE10_LOWG_DET_IBI_EN      0x04
#define BIT_INT_SOURCE10_FF_DET_IBI_EN        0x02
#endif
#define BIT_INT_SOURCE10_TAP_DET_IBI_EN       0x01

/*
 * MPUREG_OFFSET_USER_0_B4
 * Register Name: OFFSET_USER0
 */
 
/* GYRO_X_OFFUSER */
#define BIT_GYRO_X_OFFUSER_POS_LO        0
#define BIT_GYRO_X_OFFUSER_MASK_LO   (0xFF << BIT_GYRO_X_OFFUSER_POS_LO)

/*
 * MPUREG_OFFSET_USER_1_B4
 * Register Name: OFFSET_USER1
 */
 
#define BIT_GYRO_X_OFFUSER_POS_HI        0
#define BIT_GYRO_X_OFFUSER_MASK_HI   (0x0F << BIT_GYRO_X_OFFUSER_POS_HI)

/* GYRO_Y_OFFUSER */
#define BIT_GYRO_Y_OFFUSER_POS_HI        4
#define BIT_GYRO_Y_OFFUSER_MASK_HI   (0x0F << BIT_GYRO_Y_OFFUSER_POS_HI)

/*
 * MPUREG_OFFSET_USER_2_B4
 * Register Name: OFFSET_USER2
 */

#define BIT_GYRO_Y_OFFUSER_POS_LO        0
#define BIT_GYRO_Y_OFFUSER_MASK_LO   (0xFF << BIT_GYRO_Y_OFFUSER_POS_LO)

/*
 * MPUREG_OFFSET_USER_3_B4
 * Register Name: OFFSET_USER3
 */
 
/* GYRO_Z_OFFUSER */
#define BIT_GYRO_Z_OFFUSER_POS_LO        0
#define BIT_GYRO_Z_OFFUSER_MASK_LO   (0xFF << BIT_GYRO_Z_OFFUSER_POS_LO)

/*
 * MPUREG_OFFSET_USER_4_B4
 * Register Name: OFFSET_USER4
 */

#define BIT_GYRO_Z_OFFUSER_POS_HI        0
#define BIT_GYRO_Z_OFFUSER_MASK_HI   (0x0F << BIT_GYRO_Z_OFFUSER_POS_HI)

/* ACCEL_X_OFFUSER */
#define BIT_ACCEL_X_OFFUSER_POS_HI        4
#define BIT_ACCEL_X_OFFUSER_MASK_HI   (0x0F << BIT_ACCEL_X_OFFUSER_POS_HI)

/*
 * MPUREG_OFFSET_USER_5_B4
 * Register Name: OFFSET_USER5
 */
 
#define BIT_ACCEL_X_OFFUSER_POS_LO        0
#define BIT_ACCEL_X_OFFUSER_MASK_LO   (0xFF << BIT_ACCEL_X_OFFUSER_POS_LO)

/*
 * MPUREG_OFFSET_USER_6_B4
 * Register Name: OFFSET_USER_6
 */
 
/* ACCEL_Y_OFFUSER */
#define BIT_ACCEL_Y_OFFUSER_POS_LO       0
#define BIT_ACCEL_Y_OFFUSER_MASK_LO   (0xFF << BIT_ACCEL_Y_OFFUSER_POS_LO)


/*
 * MPUREG_OFFSET_USER_7_B4
 * Register Name: OFFSET_USER_7
 */

#define BIT_ACCEL_Y_OFFUSER_POS_HI        0
#define BIT_ACCEL_Y_OFFUSER_MASK_HI   (0x0F << BIT_ACCEL_Y_OFFUSER_POS_HI)

/* ACCEL_Z_OFFUSER */
#define BIT_ACCEL_Z_OFFUSER_POS_HI        4
#define BIT_ACCEL_Z_OFFUSER_MASK_HI   (0x0F << BIT_ACCEL_Z_OFFUSER_POS_HI)

/*
 * MPUREG_OFFSET_USER_8_B4
 * Register Name: OFFSET_USER_8
 */

/* ACCEL_Z_OFFUSER_L */
#define BIT_ACCEL_Z_OFFUSER_POS_LO        0
#define BIT_ACCEL_Z_OFFUSER_MASK_LO   (0xFF << BIT_ACCEL_Z_OFFUSER_POS_LO)

#ifdef __cplusplus
}
#endif

#endif  /* #ifndef _INV_ICM426XX_DEFS_H_ */
