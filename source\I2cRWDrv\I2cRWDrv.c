/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : I2cRWAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/29    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/


#ifndef I2C_RW_DRV_C
#define I2C_RW_DRV_C
#endif

/********************************************************************************
**  Include files
********************************************************************************/
#include "I2cRWDrv.h"

/* static variable definition */
#if I2C_INSTANCE_ENABLE_1
static const nrf_drv_twi_t m_twi_master_i2c1 = NRF_DRV_TWI_INSTANCE(I2C_1_MASTER_TWI_INST);
#endif
#if I2C_INSTANCE_ENABLE_2
static const nrf_drv_twi_t m_twi_master_i2c2 = NRF_DRV_TWI_INSTANCE(I2C_2_MASTER_TWI_INST);
#endif
/* static function declaration */

/* static function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  I2C_Init
* Description   :  I2C init
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : None
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */
static uint8_t I2C_ReInit(nrf_drv_twi_t const *pHi2c)
{
//    uint8_t ret = I2C_RW_ST_SUCCESS;
//    nrf_drv_twi_config_t config;
//    if(I2C_1_MASTER_TWI_INST == pHi2c->inst_idx)
//    {
//        config.scl                = I2C_1_SCK_PIN;
//        config.sda                = I2C_1_SDA_PIN;
//        config.frequency          = I2C_1_FREQUENCY;
//        config.interrupt_priority = I2C_1_IRQ_PRIORITY;
//        config.clear_bus_init     = I2C_1_CLEAR_BUS_INIT_EN;
//        config.hold_bus_uninit    = I2C_1_HOLD_BUS_UNINIT_EN;
//    }
//    #if SYS_CFG_USE_TP_SWITCH == FUN_EN
//    else
//    {
//        config.scl                = I2C_2_SCK_PIN;
//        config.sda                = I2C_2_SDA_PIN;
//        config.frequency          = I2C_2_FREQUENCY;
//        config.interrupt_priority = I2C_2_IRQ_PRIORITY;
//        config.clear_bus_init     = I2C_2_CLEAR_BUS_INIT_EN;
//        config.hold_bus_uninit    = I2C_2_HOLD_BUS_UNINIT_EN;
//    }
//    #endif
//    if(NRF_SUCCESS == nrf_drv_twi_init(pHi2c, &config, NULL, NULL))
//    {
//        nrf_drv_twi_enable(pHi2c);
//    }
//    else
//    {
//        ret = I2C_RW_ST_ERROR;
//    }
//    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  RepiarI2c
* Description   :  RepiarI2c
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : None
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */
void RepiarI2c(nrf_drv_twi_t const *pHi2c)
{
    nrf_drv_twi_uninit(pHi2c);
    I2C_ReInit(pHi2c);
}

void I2c_uninit(void)
{
#if I2C_INSTANCE_ENABLE_1
    nrf_drv_twi_uninit(&m_twi_master_i2c1);
#endif
#if I2C_INSTANCE_ENABLE_2
    nrf_drv_twi_uninit(&m_twi_master_i2c2);
#endif
}

void I2c_init(void)
{
#if I2C_INSTANCE_ENABLE_1
    RepiarI2c(&m_twi_master_i2c1);
#endif
#if I2C_INSTANCE_ENABLE_2
    RepiarI2c(&m_twi_master_i2c2);
#endif
}
/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  I2C_Write
* Description   :  I2C transmitted data
*
* Inputs        : @param  aSlaveAddr: specifies the slave address which will be transmitted 
*                 @param  pData: send data pointer 
*                 @param  aDataNum: send data length
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t I2C_Write(uint8_t twi_instance_index, uint8_t aSlaveAddr,uint8_t* apRegAddr,uint8_t aRegAddrLen, uint8_t *pData,uint16_t aDataNum)
{
    uint8_t SendBuf[I2C_SEND_MAX_NUM];
    uint8_t ret = I2C_RW_ST_SUCCESS;
    uint8_t i = 0u;
    nrf_drv_twi_t const *hi2c = NULL;
    
    if(twi_instance_index == 0)
    {
#if I2C_INSTANCE_ENABLE_1
        hi2c = &m_twi_master_i2c1;
#else
       return I2C_RW_ST_ERROR;
#endif
    }
    else
    {
#if I2C_INSTANCE_ENABLE_2
        hi2c = &m_twi_master_i2c2;
#else
       return I2C_RW_ST_ERROR;
#endif
    }
    if((aDataNum + aRegAddrLen )> I2C_SEND_MAX_NUM)
    {
        aDataNum = I2C_SEND_MAX_NUM - aRegAddrLen;
    }
    else
    {
        //do nothing
    }
    /*write register address*/
    for(i =0; i < aRegAddrLen ; i++)
    {
        SendBuf[i] = apRegAddr[i];
    }
    /*copy write data*/
    for(i =0; i < aDataNum ; i++)
    {
       SendBuf[aRegAddrLen +i] = pData[i];
    }
    if(NRF_SUCCESS != nrf_drv_twi_tx(hi2c, (aSlaveAddr >> 1u), SendBuf, (aDataNum + aRegAddrLen ), false))
    {
        DEBUG_PRINTF(" I2c-%d  write err\r\n",twi_instance_index);
        RepiarI2c(hi2c);
        ret = I2C_RW_ST_ERROR;
    }
    else
    {
        //do nothing
    }
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  I2C_Read
* Description   :  I2C receiver data
*
* Inputs        : @param  aSlaveAddr: specifies the slave address which will be transmitted 
*                 @param  pData: receiver data pointer 
*                 @param  aDataNum: receiver data length
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */
uint8_t I2C_Read(uint8_t twi_instance_index, uint16_t aSlaveAddr,uint8_t* apRegAddr,uint8_t aRegAddrLen,uint8_t *pData, uint16_t aDataNum)
{
    uint8_t SendBuf[I2C_REG_ADDR_MAX_NUM];
    uint8_t ret = I2C_RW_ST_SUCCESS;
    uint8_t i = 0u;
    nrf_drv_twi_t const *hi2c = NULL;
    if(twi_instance_index == 0)
    {
#if I2C_INSTANCE_ENABLE_1
        hi2c = &m_twi_master_i2c1;
#else
       return I2C_RW_ST_ERROR;
#endif
    }
    else
    {
#if I2C_INSTANCE_ENABLE_2
        hi2c = &m_twi_master_i2c2;
#else
       return I2C_RW_ST_ERROR;
#endif
    }
    /*read register address*/
    if( aRegAddrLen  > I2C_REG_ADDR_MAX_NUM)
    {
        aRegAddrLen = I2C_REG_ADDR_MAX_NUM;
    }
    else
    {
        //do nothing
    }
    /*write register address*/
    for(i =0; i < aRegAddrLen ; i++)
    {
        SendBuf[i] = apRegAddr[i];
    }
    
    /*select read rigister address*/
    if(NRF_SUCCESS == nrf_drv_twi_tx(hi2c, (aSlaveAddr >> 1u), SendBuf, aRegAddrLen, true))
    {
        if(NRF_SUCCESS !=  nrf_drv_twi_rx(hi2c, (aSlaveAddr >> 1u), pData, aDataNum))
        {
            DEBUG_PRINTF(" I2c-%d  read err\r\n",twi_instance_index);
            RepiarI2c(hi2c);
            ret = I2C_RW_ST_ERROR;
        }
        else
        {
            //do nothing
        }
    }
    else
    {
        DEBUG_PRINTF(" I2c-%d  read err\r\n",twi_instance_index);
        RepiarI2c(hi2c);
        ret = I2C_RW_ST_ERROR;
    }
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  I2C_WriteOneReg
* Description   :  I2C transmitted data
*
* Inputs        : @param  aSlaveAddr: specifies the slave address which will be transmitted 
*                 @param  pData: send data pointer 
*                 @param  aDataNum: send data length
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t I2C_WriteOneReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint8_t aRegAddr,uint8_t *pData,  uint16_t aDataLen)
{
    uint8_t ret = I2C_RW_ST_SUCCESS;
    
    if(I2C_Write(twi_instance_index,aSlaveAddr,&aRegAddr,1u, pData, aDataLen) != I2C_RW_ST_SUCCESS)
    {
        ret = I2C_RW_ST_ERROR;
    }
    else
    {
        //do nothing
    }
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  I2C_WriteTwoReg
* Description   :  I2C transmitted data
*
* Inputs        : @param  aSlaveAddr: specifies the slave address which will be transmitted 
*                 @param  pData: send data pointer 
*                 @param  aDataNum: send data length
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t I2C_WriteTwoReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint16_t aRegAddr,uint8_t *pData,  uint16_t aDataLen)
{
    uint8_t ret = I2C_RW_ST_SUCCESS;
     uint8_t addrBuf[2];
     memcpy(addrBuf,&aRegAddr,2u);
    if(I2C_Write(twi_instance_index,aSlaveAddr,addrBuf,2u, pData, aDataLen) != I2C_RW_ST_SUCCESS)
    {
        ret = I2C_RW_ST_ERROR;
    }
    else
    {
        //do nothing
    }
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  I2C_ReadOneReg
* Description   :  I2C receiver data
*
* Inputs        : @param  aSlaveAddr: specifies the slave address which will be transmitted 
*                 @param  pData: receiver data pointer 
*                 @param  aDataNum: receiver data length
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */
uint8_t I2C_ReadOneReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint8_t aRegAddr,uint8_t *pData,  uint16_t aDataLen)
{
    uint8_t ret = I2C_RW_ST_SUCCESS;
    

    if(I2C_Read(twi_instance_index,aSlaveAddr,&aRegAddr,1u, pData, aDataLen) != I2C_RW_ST_SUCCESS)
    {
        ret = I2C_RW_ST_ERROR;
    }
    else
    {
        //do nothing
    }
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  I2C_ReadTwoReg
* Description   :  I2C receiver data
*
* Inputs        : @param  aSlaveAddr: specifies the slave address which will be transmitted 
*                 @param  pData: receiver data pointer 
*                 @param  aDataNum: receiver data length
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */
uint8_t I2C_ReadTwoReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint16_t aRegAddr,uint8_t *pData,  uint16_t aDataLen)
{
    uint8_t ret = I2C_RW_ST_SUCCESS;
    
     uint8_t addrBuf[2];
     memcpy(addrBuf,&aRegAddr,2u);
    if(I2C_Read(twi_instance_index,aSlaveAddr,addrBuf,2u, pData, aDataLen) != I2C_RW_ST_SUCCESS)
    {
        ret = I2C_RW_ST_ERROR;
    }
    else
    {
        //do nothing
    }
    return ret;
}
/***********************************************END**********************************************/
