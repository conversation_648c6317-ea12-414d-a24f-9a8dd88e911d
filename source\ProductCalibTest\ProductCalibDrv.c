 /*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : RxMsgProcessTaskDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef PRODUCT_CALIB_TEST_DRV_C
#define PRODUCT_CALIB_TEST_DRV_C
#endif

/* include files */
#include "ProductCalibDrv.h"

/* static variable definition */
static uint8_t  gMagnetCalibType = calib_datasource_type_xim_calib;
static Calib_inside_Process_str  gCalibInsideProcesSt_t = 
{
    calib_inside_none,
    calib_inside_ack_status_ready,
    calib_inside_cmd_none,
    {0},
    {0},
    {0},
};
/* static function declaration */
static void convert_from_u16_to_protocol(const int16_t input, protocol_int16_t* p_protocol_u16)
{
    p_protocol_u16->mostByte = (input >> 8) & 0xFF;
    p_protocol_u16->lastByte = (input >> 0) & 0xFF;
}
static void convert_from_u32_to_protocol(const uint32_t input, protocol_uint32_t* p_protocol_u32)
{
    p_protocol_u32->b31_24 = (input >> 24) & 0xFF;
    p_protocol_u32->b23_16 = (input >> 16) & 0xFF;
    p_protocol_u32->b15_8  = (input >> 8) & 0xFF;
    p_protocol_u32->b7_0   = (input >> 0) & 0xFF;
}
static int16_t revert_from_protocol_to_int16(const protocol_int16_t* p_protocol_i16)
{
    uint32_t raw = p_protocol_i16->mostByte *256 + p_protocol_i16->lastByte;
    int32_t ret = ((raw&0x8000) == 0)?raw:raw - 0x10000;
    return ret;
}
static float revert_forom_protocol_to_float(const protocol_uint32_t protocol_u32)
{
    uint32_t raw = (protocol_u32.b31_24 << 24 )
            + (protocol_u32.b23_16 << 16)
            + (protocol_u32.b15_8 << 8)
            + (protocol_u32.b7_0 << 0);
    return *(float*)&raw;
}

static bool private_cmp_isSame(uint8_t const * const p_src, uint8_t const * const p_tar, uint32_t len)
{
    bool isSame = true;
    for(int i =0;i<len;i++)
    {
        if(p_src[i] == 0)
        {
            break;
        }
        if(p_tar[i] == 0)
        {
            isSame = false;
            break;
        }
        if(p_src[i] != p_tar[i])
        {
            isSame = false;
            break;
        }
    }
    return isSame;
}
static protocol_u32_t convert_from_float_to_protocol(float value_in)
{
    uint32_t p_raw = *(uint32_t*)&value_in;
    protocol_u32_t ret;
    ret.b32_24 = (p_raw >> 24)&0xFF;
    ret.b23_16 = (p_raw >> 16)&0xFF;
    ret.b15_8  = (p_raw >>  8)&0xFF;
    ret.b7_0   = (p_raw >>  0)&0xFF;
    return ret;
}
static bool isMinMaxCheckPassed(float value, float cfg_min, float cfg_max)
{
    if(value > cfg_max)
    {
        return false;
    }
    if(value < cfg_min)
    {
        return false;
    }
    return true;
}

static bool calculate_acc_offset_scale(void)
{
    bool isCheckPassed = true;

    gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.x = (gCalibInsideProcesSt_t.mAcc_max.x + gCalibInsideProcesSt_t.mAcc_min.x)/2;
    gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.y = (gCalibInsideProcesSt_t.mAcc_max.y + gCalibInsideProcesSt_t.mAcc_min.y)/2;
    gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.z = (gCalibInsideProcesSt_t.mAcc_max.z + gCalibInsideProcesSt_t.mAcc_min.z)/2;
    gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.x = 4096.0f/(gCalibInsideProcesSt_t.mAcc_max.x - gCalibInsideProcesSt_t.mAcc_min.x);
    gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.y = 4096.0f/(gCalibInsideProcesSt_t.mAcc_max.y - gCalibInsideProcesSt_t.mAcc_min.y);
    gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.z = 4096.0f/(gCalibInsideProcesSt_t.mAcc_max.z - gCalibInsideProcesSt_t.mAcc_min.z);
    

    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.x, accel_offset_check_min, accel_offset_check_max))
    {
         isCheckPassed = false;
    }
    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.y, accel_offset_check_min, accel_offset_check_max))
    {
         isCheckPassed = false;
    }
    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.z, accel_offset_check_min, accel_offset_check_max))
    {
        isCheckPassed = false;
    }
    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.x, accel_scale_check_min, accel_scale_check_max))
    {
        isCheckPassed = false;
    }
    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.y, accel_scale_check_min, accel_scale_check_max))
    {
        isCheckPassed = false;
    }
    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.z, accel_scale_check_min, accel_scale_check_max))
    {
        isCheckPassed = false;
    }
    
    return isCheckPassed;
}
static inline void makesure_max(int16_t* p_result, int16_t data)
{
    if(data > *p_result)
    {
        *p_result = data;
    }
}
static inline void makesure_min(int16_t* p_result, int16_t data)
{
    if(data < *p_result)
    {
        *p_result = data;
    }
}
static inline void makesure_new(dof9_element_t* p_max, dof9_element_t* p_min, dof9_element_t* p_new)
{
    makesure_max(&p_max->x, p_new->x);
    makesure_max(&p_max->y, p_new->y);
    makesure_max(&p_max->z, p_new->z);
    makesure_min(&p_min->x, p_new->x);
    makesure_min(&p_min->y, p_new->y);
    makesure_min(&p_min->z, p_new->z);
}
void radio_disable(void)
{
    NRF_RADIO->SHORTS          = 0;
    NRF_RADIO->EVENTS_DISABLED = 0;
#ifdef NRF51
    NRF_RADIO->TEST            = 0;
#endif
    NRF_RADIO->TASKS_DISABLE   = 1;
    while (NRF_RADIO->EVENTS_DISABLED == 0)
    {
        // Do nothing.
    }
    NRF_RADIO->EVENTS_DISABLED = 0;
}
/**
 * @brief Function for turning on the TX carrier test mode.
*/
void radio_tx_carrier(uint8_t txpower, uint8_t mode, uint8_t channel)
{
    radio_disable();
    NRF_RADIO->SHORTS     = RADIO_SHORTS_READY_START_Msk;
    NRF_RADIO->TXPOWER    = (txpower << RADIO_TXPOWER_TXPOWER_Pos);    
    NRF_RADIO->MODE       = (mode << RADIO_MODE_MODE_Pos);
    NRF_RADIO->FREQUENCY  = channel;
#ifdef NRF51
    NRF_RADIO->TEST       = (RADIO_TEST_CONST_CARRIER_Enabled << RADIO_TEST_CONST_CARRIER_Pos) \
                            | (RADIO_TEST_PLL_LOCK_Enabled << RADIO_TEST_PLL_LOCK_Pos);
#endif
    NRF_RADIO->TASKS_TXEN = 1;
}
/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  MsgCmdProcess
* Description   :  process cmd from usb
*
* Inputs        : @param  apCmdMsg: recieve msg buffer pointer
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : send  status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 

void BleCalibCheckPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen)
{
    Calib_check_t RxCmd_t;
    Calib_inside_Cmd_t  inside_cmd_t;
    
    memcpy(RxCmd_t.buffer,apCmdMsg,sizeof(RxCmd_t.buffer));
    memcpy(inside_cmd_t.buffer,apCmdMsg,sizeof(inside_cmd_t.buffer));
    NRF_LOG_INFO("old calib check:0x%02X, command:0x%02X", RxCmd_t.rx_Check_cmd.head, RxCmd_t.rx_Check_cmd.target);
    switch(RxCmd_t.rx_Check_cmd.head)
    {
        case calib_check_type_read_float:        //read save calib resuilt
        {
            switch(RxCmd_t.rx_Check_cmd.target)
            {
                case calib_get_accel_center_xyz:
                {
                    RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.accel.offset.x);
                    RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.accel.offset.y);
                    RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.accel.offset.z);
                    break;
                }
                case calib_get_accel_scale_xyz:
                {
                    RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.accel.scale.x);
                    RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.accel.scale.y);
                    RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.accel.scale.z);
                    break;
                }
                case calib_get_gyro_center_xyz:
                {
                    RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.gyro.offset.x);
                    RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.gyro.offset.y);
                    RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.gyro.offset.z);
                    break;
                }
                case calib_get_gyro_scale_xyz:
                {
                    RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.gyro.scale.x);
                    RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.gyro.scale.y);
                    RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.gyro.scale.z);
                    break;
                }
                case calib_get_magnet_center_xyz:
                {
                    RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.magnet.offset.x);
                    RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.magnet.offset.y);
                    RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.magnet.offset.z);
                    break;
                }
                default:
                    RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.magnet.scale.x);
                    RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.magnet.scale.y);
                    RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gSystemCfg_t.mImuCalibSet.magnet.scale.z);
                break;
            }
            if(
                 (gSystemSt_t.BlePipeNotifyEn_t.CalibCheckPipe  == SYS_CTRL_EN)
                &&(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,RxCmd_t.buffer,20)==PACKET_OPERATE_SUCCESS)
                )
            {
                //do nothing
            }
            else
            {
                NRF_LOG_INFO("send calib save result fail");
            }
            break;
        }
        case calib_check_type_calib_inside:
        {
            if(gCalibInsideProcesSt_t.mCalibRunSt != calib_inside_data_sample)
            {
                if(inside_cmd_t.rx_tx.cmd == calib_inside_cmd_prepare)
                {
                    memset(&gCalibInsideProcesSt_t,0x00,sizeof(gCalibInsideProcesSt_t));
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    gCalibInsideProcesSt_t.mCheckSt = calib_inside_ack_status_ready;
                    gCalibInsideProcesSt_t.mActionCmd = calib_inside_cmd_prepare;
                    inside_cmd_t.rx_tx.status = 0x02; // run finish 
                    NRF_LOG_INFO("calib inside prepare");
                }
                else if(inside_cmd_t.rx_tx.cmd == calib_inside_cmd_save_and_exit) //save and exit
                {
                    Imu_10msTimer_Enable(0);
                    NRF_LOG_INFO("calib inside save exit");
                    if(calculate_acc_offset_scale())
                    {
						gSystemCfg_t.mImuCalibSet.accel = gCalibInsideProcesSt_t.mCalibInsideResult.accel;
					}
//                        gSystemCfg_t.mImuCalibSet.accel = gCalibInsideProcesSt_t.mCalibInsideResult.accel;
                        gSystemCfg_t.mImuCalibSet.gyro = gCalibInsideProcesSt_t.mCalibInsideResult.gyro;
                        gSystemCfg_t.mImuCalibSet.magnet.offset = gCalibInsideProcesSt_t.mCalibInsideResult.magnet.offset;
                        gSystemCfg_t.mImuCalibSet.magnet.scale = gCalibInsideProcesSt_t.mCalibInsideResult.magnet.scale;
                        gSystemCfg_t.mImuCalibSet.times += 1u;
                        FlashDataWrite(DS_DATA_TYPE_D1,0,(uint8_t *)(&gSystemCfg_t.mImuCalibSet),sizeof(gSystemCfg_t.mImuCalibSet),DS_DATA_OPERATE_BACKUP_EN);
                        imu_set_calibration_param(gSystemCfg_t.mImuCalibSet);
                        inside_cmd_t.rx_tx.status = 0x02; // run finish
//                    }
//                    else
//                    {
//                        NRF_LOG_INFO("calib offset scale check err");
//                        inside_cmd_t.rx_tx.status = 0x00; // error
//                    }
                    Imu_10msTimer_Enable(1);
                    memset(&gCalibInsideProcesSt_t,0x00,sizeof(gCalibInsideProcesSt_t));
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_none;
                    gCalibInsideProcesSt_t.mCheckSt = calib_inside_ack_status_ready;
                    gCalibInsideProcesSt_t.mActionCmd = calib_inside_cmd_none;
                     
                }
                else if(inside_cmd_t.rx_tx.cmd == calib_inside_cmd_abort)
                {
                    NRF_LOG_INFO("calib inside abort");
                    memset(&gCalibInsideProcesSt_t,0x00,sizeof(gCalibInsideProcesSt_t));
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_none;
                    gCalibInsideProcesSt_t.mCheckSt = calib_inside_ack_status_ready;
                    gCalibInsideProcesSt_t.mActionCmd = calib_inside_cmd_none;
                    inside_cmd_t.rx_tx.status = 0x02; // run finish 
                }
                else
                {
                    NRF_LOG_INFO("calib inside action cmd =%x",inside_cmd_t.rx_tx.cmd);
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_data_sample;
                    gCalibInsideProcesSt_t.mCheckSt = calib_inside_ack_status_ready;
                    gCalibInsideProcesSt_t.mActionCmd = (calib_inside_cmd_type_t)inside_cmd_t.rx_tx.cmd;
                    inside_cmd_t.rx_tx.status = 0x01; // run finish 
                }
            }
            else
            {
                if(inside_cmd_t.rx_tx.cmd == calib_inside_cmd_abort)
                {
                    NRF_LOG_INFO("calib inside abort");
                    memset(&gCalibInsideProcesSt_t,0x00,sizeof(gCalibInsideProcesSt_t));
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_none;
                    gCalibInsideProcesSt_t.mCheckSt = calib_inside_ack_status_ready;
                    gCalibInsideProcesSt_t.mActionCmd = calib_inside_cmd_none;
                    inside_cmd_t.rx_tx.status = 0x02; // run finish 
                }
                else if(inside_cmd_t.rx_tx.cmd == calib_inside_cmd_magnet_rotate_stop)
                {
                    NRF_LOG_INFO("calib inside rotat stop");
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_data_sample;
                    gCalibInsideProcesSt_t.mCheckSt = calib_inside_ack_status_ready;
                    gCalibInsideProcesSt_t.mActionCmd = (calib_inside_cmd_type_t)inside_cmd_t.rx_tx.cmd;
                    inside_cmd_t.rx_tx.status = 0x01; // run finish 
                }
                else
                {
                    inside_cmd_t.rx_tx.status = 0x00; // none 
                }
            }
            if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,inside_cmd_t.buffer,20)==PACKET_OPERATE_SUCCESS)
            {
                //do nothing
            }
            else
            {
                NRF_LOG_INFO("send calib action ack fail");
            }
            break;
        }
        case calib_check_type_calib_mid_check:
        {
            if(RxCmd_t.rx_Check_cmd.target == calib_get_accel_center_xyz)   //acc offset
            {
                calculate_acc_offset_scale();
                RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.x);
                RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.y);
                RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.accel.offset.z);
            }
            else if(RxCmd_t.rx_Check_cmd.target == calib_get_accel_scale_xyz)  //acc scale
            {
                calculate_acc_offset_scale();
                RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.x);
                RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.y);
                RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.accel.scale.z);
            }
            else if(RxCmd_t.rx_Check_cmd.target == calib_get_gyro_center_xyz)   //gyro offset
            {
                RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.x);
                RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.y);
                RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.z);
            }
            else if(RxCmd_t.rx_Check_cmd.target == calib_get_gyro_scale_xyz)
            {
                RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.x);
                RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.y);
                RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.z);
            }
            if(RxCmd_t.rx_Check_cmd.target == calib_get_magnet_center_xyz)   //magnet offset
            {
                RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.magnet.offset.x);
                RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.magnet.offset.y);
                RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.magnet.offset.z);
            }
            else
            {
                RxCmd_t.tx_result.f0 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.magnet.scale.x);
                RxCmd_t.tx_result.f1 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.magnet.scale.y);
                RxCmd_t.tx_result.f2 = convert_from_float_to_protocol(gCalibInsideProcesSt_t.mCalibInsideResult.magnet.scale.z);
            }
            if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,RxCmd_t.buffer,20)==PACKET_OPERATE_SUCCESS)
            {
                //do nothing
            }
            else
            {
                NRF_LOG_INFO("send calib buf result fail");
            }
            break;
        }
        default:
        break;
    }
}
void CalibCheckPipeDataSample(ICM20602SensorData_str *apImuSensorData,void * apMagnetSensorData)     //<1ms
{
    static dof9_element_t mag_max = {
        .x = -0xFFF,
        .y = -0xFFF,
        .z = -0xFFF,
    };
    static dof9_element_t mag_min = {
        .x = 0xFFF,
        .y = 0xFFF,
        .z = 0xFFF,
    };
    static uint32_t   cnt_sample = 0;
    static float gyro_sum_x = 0;
    static float gyro_sum_y = 0;
    static float gyro_sum_z = 0;
    static float accel_sum_axi = 0;
    bool isCheckPassed = true;
    
    Calib_inside_Cmd_t  ImuSendBuf;
    memset(&ImuSendBuf,0x00,sizeof(ImuSendBuf));
    
    if(   (gCalibInsideProcesSt_t.mCalibRunSt == calib_inside_none)
        ||((gCalibInsideProcesSt_t.mCalibRunSt == calib_inside_wait_action_cmd)&&(gCalibInsideProcesSt_t.mActionCmd == calib_inside_cmd_prepare) )
      )  //clear
    {
        mag_max.x = -0xFFF;
        mag_max.y = -0xFFF;
        mag_max.z = -0xFFF;
        mag_min.x = 0xFFF;
        mag_min.y = 0xFFF;
        mag_min.z = 0xFFF;
        cnt_sample = 0;
        gyro_sum_x = 0;
        gyro_sum_y = 0;
        gyro_sum_z = 0;
        accel_sum_axi = 0;
    }
    else if(gCalibInsideProcesSt_t.mCalibRunSt == calib_inside_data_sample)
    {
        cnt_sample++;
        switch(gCalibInsideProcesSt_t.mActionCmd)
        {
            case calib_inside_cmd_accel_x_positive_start:
            {
                accel_sum_axi += apImuSensorData->mAcc3axisData_t.mAccX;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mAcc_max.x = accel_sum_axi /cnt_sample;
                    accel_sum_axi = 0;
                    cnt_sample = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    ImuSendBuf.rx_tx.status = 0x02;
                    NRF_LOG_INFO("calib inside Acc x+ sample finish");
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_accel_x_negative_start:
            {
                accel_sum_axi += apImuSensorData->mAcc3axisData_t.mAccX;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mAcc_min.x = accel_sum_axi /cnt_sample;
                    accel_sum_axi = 0;
                    cnt_sample = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    ImuSendBuf.rx_tx.status = 0x02;
                    NRF_LOG_INFO("calib inside Acc x- sample finish");
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_accel_y_positive_start:
            {
                accel_sum_axi += apImuSensorData->mAcc3axisData_t.mAccY;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mAcc_max.y = accel_sum_axi /cnt_sample;
                    accel_sum_axi = 0;
                    cnt_sample = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    ImuSendBuf.rx_tx.status = 0x02;
                    NRF_LOG_INFO("calib inside ACC y+ sample finish");
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_accel_y_negative_start:
            {
                accel_sum_axi += apImuSensorData->mAcc3axisData_t.mAccY;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mAcc_min.y = accel_sum_axi /cnt_sample;
                    accel_sum_axi = 0;
                    cnt_sample = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    ImuSendBuf.rx_tx.status = 0x02;
                    NRF_LOG_INFO("calib inside ACC y- sample finish");
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_accel_z_positive_start:
            {
                accel_sum_axi += apImuSensorData->mAcc3axisData_t.mAccZ;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mAcc_max.z = accel_sum_axi /cnt_sample;
                    accel_sum_axi = 0;
                    cnt_sample = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    ImuSendBuf.rx_tx.status = 0x02;
                    NRF_LOG_INFO("calib inside ACC z+ sample finish");
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_accel_z_negative_start:
            {
                accel_sum_axi += apImuSensorData->mAcc3axisData_t.mAccZ;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mAcc_min.z = accel_sum_axi /cnt_sample;
                    accel_sum_axi = 0;
                    cnt_sample = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    ImuSendBuf.rx_tx.status = 0x02;
                    NRF_LOG_INFO("calib inside ACC z- sample finish");
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_gyro_offset_start:
            {
                gyro_sum_x += apImuSensorData->mGyro3axisData_t.mGyroX;
                gyro_sum_y += apImuSensorData->mGyro3axisData_t.mGyroY;
                gyro_sum_z += apImuSensorData->mGyro3axisData_t.mGyroZ;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.x = (gyro_sum_x * 1.0f) /cnt_sample;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.y = (gyro_sum_y * 1.0f) /cnt_sample;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.z = (gyro_sum_z * 1.0f) /cnt_sample;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.x = 1.0f;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.y = 1.0f;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.z = 1.0f;
                    cnt_sample = 0;
                    gyro_sum_x = 0;
                    gyro_sum_y = 0;
                    gyro_sum_z = 0;
                    accel_sum_axi = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    NRF_LOG_INFO("calib inside gyro  sample finish");
                    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.x, gyro_offset_check_min, gyro_offset_check_max))
                    {
                        NRF_LOG_INFO("calib inside gyro x sample finish offset err");
                        isCheckPassed = false;
                    }
                    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.y, gyro_offset_check_min, gyro_offset_check_max))
                    {
                        NRF_LOG_INFO("calib inside gyro y sample finish offset err");
                        isCheckPassed = false;
                    }
                    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.z, gyro_offset_check_min, gyro_offset_check_max))
                    {
                        NRF_LOG_INFO("calib inside gyro z sample finish offset err");
                        isCheckPassed = false;
                    }
                    if(isCheckPassed)
                    {
                        ImuSendBuf.rx_tx.status = 0x02;
                    }
                    else
                    {
                        ImuSendBuf.rx_tx.status =  calib_inside_cmd_err_invalid_position ;
                    }
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_accel_z_p_gyro_offset_start:   //check
            {
                accel_sum_axi += apImuSensorData->mAcc3axisData_t.mAccZ;
                gyro_sum_x += apImuSensorData->mGyro3axisData_t.mGyroX;
                gyro_sum_y += apImuSensorData->mGyro3axisData_t.mGyroY;
                gyro_sum_z += apImuSensorData->mGyro3axisData_t.mGyroZ;
                if(cnt_sample >= CALIB_INSIDE_DATA_SAMPLE)
                {
                    gCalibInsideProcesSt_t.mAcc_max.z = accel_sum_axi /cnt_sample;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.x = (gyro_sum_x * 1.0f) /cnt_sample;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.y = (gyro_sum_x * 1.0f) /cnt_sample;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.z = (gyro_sum_x * 1.0f) /cnt_sample;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.x = 1.0f;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.y = 1.0f;
                    gCalibInsideProcesSt_t.mCalibInsideResult.gyro.scale.z = 1.0f;
                    cnt_sample = 0;
                    gyro_sum_x = 0;
                    gyro_sum_y = 0;
                    gyro_sum_z = 0;
                    accel_sum_axi = 0;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    NRF_LOG_INFO("calib inside gyro  Acc Z+ sample finish");
                    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.x, gyro_offset_check_min, gyro_offset_check_max))
                    {
                        NRF_LOG_INFO("calib inside gyro x sample finish offset err");
                        isCheckPassed = false;
                    }
                    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.y, gyro_offset_check_min, gyro_offset_check_max))
                    {
                        NRF_LOG_INFO("calib inside gyro y sample finish offset err");
                        isCheckPassed = false;
                    }
                    if(!isMinMaxCheckPassed(gCalibInsideProcesSt_t.mCalibInsideResult.gyro.offset.z, gyro_offset_check_min, gyro_offset_check_max))
                    {
                        NRF_LOG_INFO("calib inside gyro z sample finish offset err");
                        isCheckPassed = false;
                    }
                    if(isCheckPassed)
                    {
                        ImuSendBuf.rx_tx.status = 0x02;
                    }
                    else
                    {
                        ImuSendBuf.rx_tx.status =  calib_inside_cmd_err_invalid_position ;
                    }
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_magnet_rotate_start:
            {
                cnt_sample++;
                //makesure_new(&mag_max, &mag_min, apMagnetSensorData);
                if(cnt_sample >= CALIB_INSIDE_MAGNET_SAMPLE_TIMEOUT)
                {
                    cnt_sample = 0;
                    mag_max.x = -0xFFF;
                    mag_max.y = -0xFFF;
                    mag_max.z = -0xFFF;
                    mag_min.x = 0xFFF;
                    mag_min.y = 0xFFF;
                    mag_min.z = 0xFFF;
                    gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                    ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                    ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                    ImuSendBuf.rx_tx.status =  calib_inside_cmd_err_timeout ;
                    if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                    {
                        //do nothing
                    }
                    else
                    {
                        NRF_LOG_INFO("send calib action ack fail");
                    }
                }
                break;
            }
            case calib_inside_cmd_magnet_rotate_stop:
            {
                static const float magnet_full_scale = 100.0f;
                gCalibInsideProcesSt_t.mCalibInsideResult.magnet.offset.x = (mag_min.x + mag_max.x)/2;
                gCalibInsideProcesSt_t.mCalibInsideResult.magnet.offset.y = (mag_min.y + mag_max.y)/2;
                gCalibInsideProcesSt_t.mCalibInsideResult.magnet.offset.z = (mag_min.z + mag_max.z)/2;

                float range_x = (mag_max.x - mag_min.x)*0.5f;
                float range_y = (mag_max.y - mag_min.y)*0.5f;
                float range_z = (mag_max.z - mag_min.z)*0.5f;

                float scale_x = magnet_full_scale/range_x;
                float scale_y = magnet_full_scale/range_y;
                float scale_z = magnet_full_scale/range_z;

                gCalibInsideProcesSt_t.mCalibInsideResult.magnet.scale.x = scale_x;
                gCalibInsideProcesSt_t.mCalibInsideResult.magnet.scale.y = scale_y;
                gCalibInsideProcesSt_t.mCalibInsideResult.magnet.scale.z = scale_z;
                cnt_sample = 0;
                mag_max.x = -0xFFF;
                mag_max.y = -0xFFF;
                mag_max.z = -0xFFF;
                mag_min.x = 0xFFF;
                mag_min.y = 0xFFF;
                mag_min.z = 0xFFF;
                gCalibInsideProcesSt_t.mCalibRunSt = calib_inside_wait_action_cmd;
                ImuSendBuf.rx_tx.head = calib_check_type_calib_inside;
                ImuSendBuf.rx_tx.cmd = gCalibInsideProcesSt_t.mActionCmd;
                ImuSendBuf.rx_tx.status =  0x02 ;
                if(BleSendSingleFrame(BLE_BASE_UUID_CALIB_CHECK,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
                {
                    //do nothing
                }
                else
                {
                    NRF_LOG_INFO("send calib action ack fail");
                }
                break;
            }
            default:
              cnt_sample = 0;
              break;
        }
    }
}
void BleCalibPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen)
{
    CalibPipe_Rx_t RxCmd_t;
    
    memcpy(RxCmd_t.buffer,apCmdMsg,sizeof(RxCmd_t.buffer));
    NRF_LOG_INFO("old protocol:0x%02X, command:0x%02X", RxCmd_t.content.command, RxCmd_t.content.subtype);
    switch(RxCmd_t.content.command)
    {
        case calib_cmd_type_akm_calib_request:
        {
            NRF_LOG_INFO("change to akm calib");
            gMagnetCalibType = calib_datasource_type_akm_calib;
        }
        break;
        case calib_cmd_type_password:
        {
            static uint8_t const * const p_src = (void*)"Ximmerse.com";
            // password as 0xFE-58-69-6D-6D-65-72-73-65-2E-63-6F-6D-00-00-00-00-00-00-00
            //              FE58696D6D657273652E636F6D00000000000000
            if(private_cmp_isSame(p_src, RxCmd_t.calib_enable_password.password, 18))
            {
                NRF_LOG_INFO("enable calib pipe data updata ok");
                WorkModelChange(SYS_MODE_CALIB);
                gMagnetCalibType = calib_datasource_type_xim_calib;
            }
            else
            {
                NRF_LOG_INFO("enable calib pipe data updata  password err");
            }
            break;
        }
        case calib_cmd_type_save_calib_all:
        {
            #define OFFSET_AMPLIFY  100.0f
            #define SCALE_AMPLIFY   1000.0f
            dof3_float_t offset;
            dof3_float_t scale;
            if(gSystemSt_t.mDeviceSt_uni.Bits.mSysModel == SYS_MODE_CALIB)
            {
                offset.x = revert_from_protocol_to_int16(&RxCmd_t.Save_calib.offset_x)/OFFSET_AMPLIFY;
                offset.y = revert_from_protocol_to_int16(&RxCmd_t.Save_calib.offset_y)/OFFSET_AMPLIFY;
                offset.z = revert_from_protocol_to_int16(&RxCmd_t.Save_calib.offset_z)/OFFSET_AMPLIFY;
                scale.x = revert_from_protocol_to_int16(&RxCmd_t.Save_calib.scale_x)/SCALE_AMPLIFY;
                scale.y = revert_from_protocol_to_int16(&RxCmd_t.Save_calib.scale_y)/SCALE_AMPLIFY;
                scale.z = revert_from_protocol_to_int16(&RxCmd_t.Save_calib.scale_z)/SCALE_AMPLIFY;
                Imu_10msTimer_Enable(0);
                switch(RxCmd_t.Save_calib.command)
                {
                    case calib_save_cmd_accel:
                    {
                        gSystemCfg_t.mImuCalibSet.accel.offset = offset;
                        gSystemCfg_t.mImuCalibSet.accel.scale = scale;
                        gSystemCfg_t.mImuCalibSet.times += 1u;
                        FlashDataWrite(DS_DATA_TYPE_D1,0,(uint8_t *)(&gSystemCfg_t.mImuCalibSet),sizeof(gSystemCfg_t.mImuCalibSet),DS_DATA_OPERATE_BACKUP_EN);
                        imu_set_calibration_param(gSystemCfg_t.mImuCalibSet);
                        NRF_LOG_INFO("save accel offset:%x,%x,%x ", \
                                            gSystemCfg_t.mImuCalibSet.accel.offset.x,  \
                                            gSystemCfg_t.mImuCalibSet.accel.offset.y,  \
                                            gSystemCfg_t.mImuCalibSet.accel.offset.z\
                                     );
                         NRF_LOG_INFO("save accel scale:%x,%x,%x", \
                                            gSystemCfg_t.mImuCalibSet.accel.scale.x,  \
                                            gSystemCfg_t.mImuCalibSet.accel.scale.y,  \
                                            gSystemCfg_t.mImuCalibSet.accel.scale.z\
                                     );
                        MotorPwmPwrCtrl(1,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mPeriod,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mDuty,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mTimeout);  //move 1s
                        break;
                    }
                    case calib_save_cmd_gyro:
                    {
                        gSystemCfg_t.mImuCalibSet.gyro.offset = offset;
                        gSystemCfg_t.mImuCalibSet.gyro.scale = scale;
                        gSystemCfg_t.mImuCalibSet.times += 1u;
                        FlashDataWrite(DS_DATA_TYPE_D1,0,(uint8_t *)(&gSystemCfg_t.mImuCalibSet),sizeof(gSystemCfg_t.mImuCalibSet),DS_DATA_OPERATE_BACKUP_EN);
                        imu_set_calibration_param(gSystemCfg_t.mImuCalibSet);
                        NRF_LOG_INFO("save gyro calib offset:%x,%x,%x scale:%x,%x,%x", \
                                            gSystemCfg_t.mImuCalibSet.gyro.offset.x,  \
                                            gSystemCfg_t.mImuCalibSet.gyro.offset.y,  \
                                            gSystemCfg_t.mImuCalibSet.gyro.offset.z,  \
                                            gSystemCfg_t.mImuCalibSet.gyro.scale.x,  \
                                            gSystemCfg_t.mImuCalibSet.gyro.scale.y,  \
                                            gSystemCfg_t.mImuCalibSet.gyro.scale.z \
                                     );
                        MotorPwmPwrCtrl(1,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mPeriod,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mDuty,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mTimeout);  //move 1s
                        break;
                    }
                    case calib_save_cmd_magnet:
                    {
                        gSystemCfg_t.mImuCalibSet.magnet.mag_calib_type =  (magnet_source_type_t)gMagnetCalibType;
                        gSystemCfg_t.mImuCalibSet.magnet.offset = offset;
                        gSystemCfg_t.mImuCalibSet.magnet.scale = scale;
                        gSystemCfg_t.mImuCalibSet.times += 1u;
                        FlashDataWrite(DS_DATA_TYPE_D1,0,(uint8_t *)(&gSystemCfg_t.mImuCalibSet),sizeof(gSystemCfg_t.mImuCalibSet),DS_DATA_OPERATE_BACKUP_EN);
                        imu_set_calibration_param(gSystemCfg_t.mImuCalibSet);
                        NRF_LOG_INFO("save magnet calib offset:%x,%x,%x scale:%x,%x,%x", \
                                            gSystemCfg_t.mImuCalibSet.magnet.offset.x,  \
                                            gSystemCfg_t.mImuCalibSet.magnet.offset.y,  \
                                            gSystemCfg_t.mImuCalibSet.magnet.offset.z,  \
                                            gSystemCfg_t.mImuCalibSet.magnet.scale.x,  \
                                            gSystemCfg_t.mImuCalibSet.magnet.scale.y,  \
                                            gSystemCfg_t.mImuCalibSet.magnet.scale.z \
                                     );
                        MotorPwmPwrCtrl(1,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mPeriod,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mDuty,gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mTimeout);  //move 1s
                        break;
                    }
                    default:
                        break;
                }
                Imu_10msTimer_Enable(1);
            }
            break;
        }
        default:
        break;
    }
}
void CalibPipeDataUpdata(ICM20602SensorData_str *apImuSensorData,void * apMagnetSensorData)     //<1ms
{
    CalibPipe_Tx_t  ImuSendBuf;
    memset(&ImuSendBuf,0x00,sizeof(ImuSendBuf));
    if(gSystemSt_t.mDeviceSt_uni.Bits.mSysModel == SYS_MODE_CALIB)
    {
        if(gMagnetCalibType == calib_datasource_type_xim_calib)
        {
            ImuSendBuf.Tx_str.type = calib_datasource_type_xim_calib;
            ImuSendBuf.Tx_str.status = gSystemCfg_t.mImuCalibSet.times>0? 0x01:0x00;
            
            convert_from_u16_to_protocol(apImuSensorData->mAcc3axisData_t.mAccX, &ImuSendBuf.Tx_str.accel.x);
            convert_from_u16_to_protocol(apImuSensorData->mAcc3axisData_t.mAccY, &ImuSendBuf.Tx_str.accel.y);
            convert_from_u16_to_protocol(apImuSensorData->mAcc3axisData_t.mAccZ, &ImuSendBuf.Tx_str.accel.z);

            convert_from_u16_to_protocol(apImuSensorData->mGyro3axisData_t.mGyroX, &ImuSendBuf.Tx_str.gyro.x);
            convert_from_u16_to_protocol(apImuSensorData->mGyro3axisData_t.mGyroY, &ImuSendBuf.Tx_str.gyro.y);
            convert_from_u16_to_protocol(apImuSensorData->mGyro3axisData_t.mGyroZ, &ImuSendBuf.Tx_str.gyro.z);
        }
        else if(gMagnetCalibType == calib_datasource_type_akm_calib)
        {
            ImuSendBuf.Tx_str.type = calib_datasource_type_akm_calib;
            ImuSendBuf.Tx_str.status = gSystemCfg_t.mImuCalibSet.times>0? 0x01:0x00;
            
            convert_from_u16_to_protocol(apImuSensorData->mAcc3axisData_t.mAccX, &ImuSendBuf.Tx_str.accel.x);
            convert_from_u16_to_protocol(apImuSensorData->mAcc3axisData_t.mAccY, &ImuSendBuf.Tx_str.accel.y);
            convert_from_u16_to_protocol(apImuSensorData->mAcc3axisData_t.mAccZ, &ImuSendBuf.Tx_str.accel.z);

            convert_from_u16_to_protocol(apImuSensorData->mGyro3axisData_t.mGyroX, &ImuSendBuf.Tx_str.gyro.x);
            convert_from_u16_to_protocol(apImuSensorData->mGyro3axisData_t.mGyroY, &ImuSendBuf.Tx_str.gyro.y);
            convert_from_u16_to_protocol(apImuSensorData->mGyro3axisData_t.mGyroZ, &ImuSendBuf.Tx_str.gyro.z);
        }
        if(
              (gSystemSt_t.BlePipeNotifyEn_t.CalibPipe  == SYS_CTRL_EN)
            &&(BleSendSingleFrame(BLE_BASE_UUID_IMU_CALIB,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
          )
        {
            //do nothing
        }
        else
        {
            //do nothing
        }
    }
}
/***********************************************END**********************************************/
