/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : SysStartUpTaskAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef SYS_STARTUP_TASK_APP_EXT_H
#define SYS_STARTUP_TASK_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "includes.h"

/*declaration range definition*/
#ifdef SYS_STARTUP_TASK_DRV_C 
  #define SYS_STARTUP_TASK_APP_EXT 
#else 
  #define SYS_STARTUP_TASK_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define MSG_TASK_Q_USB                      0x00
#define MSG_TASK_Q_IMU                      0x01
#define MSG_TASK_Q_TP                       0x02
#define MSG_TASK_Q_CMD                      0x03
#define MSG_TASK_Q_TIMER_10MS               0x04
#define MSG_TASK_Q_TIMER_1S                 0x05
#define MSG_TASK_Q_BLE_CONN                 0x06
#define MSG_TASK_Q_BLE_DISCONN              0x07
#define MSG_TASK_Q_BLE_CONN_PARAM           0x08
#define MSG_TASK_Q_CALIB_MODE_SET           0x09
#define MSG_TASK_Q_SLEEP                    0x0a
#define MSG_TASK_Q_WAKEUP                   0x0b
#define MSG_TASK_Q_BLE_ADV_START            0x0C
#define MSG_TASK_Q_BLE_ADV_STOP             0x0D
#define MSG_TASK_Q_BLE_CONN_EVTEN           0x0E
#define MSG_TASK_Q_UART                     0x0F
#define MSG_TASK_Q_BLE_SERVICE_NOTIFY_EN    0x10
#define MSG_TASK_Q_BLE_SERVICE_NOTIFY_DIS   0x11
#define MSG_TASK_Q_USB_POWER_DETECTED       0x12
#define MSG_TASK_Q_USB_POWER_REMOVED        0x13
#define MSG_TASK_Q_USB_POWER_READY          0x14

#define MSG_TASK_Q_RUF                      0xFF
  
#define TASK_STARTUP_PRI            0x01
#define STK_SIZE_TASK_STARTUP       (64)//256KB

/* data type definiton  */
typedef struct
{
    uint32_t  mType;
    uint16_t  mMsgLength;
    uint16_t  mServiceUUID;
    void * mpBuf;
}
Task_Msg_Q_str;

/* variable definition */

/* function declaration */
SYS_STARTUP_TASK_APP_EXT  void start_Timer_1s(void);
SYS_STARTUP_TASK_APP_EXT  void stop_Timer_1s(void);
SYS_STARTUP_TASK_APP_EXT  void start_Timer_10ms(void);
SYS_STARTUP_TASK_APP_EXT  void stop_Timer_10ms(void);
SYS_STARTUP_TASK_APP_EXT  void Task_StartUp(void *pdata) ;
SYS_STARTUP_TASK_APP_EXT  void TaskStartScheduler( void );
SYS_STARTUP_TASK_APP_EXT  void bleInfoInit(void);
SYS_STARTUP_TASK_APP_EXT  char bit4_to_char(uint8_t input);
#endif   /* end of header file */
