/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : DSwSampDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef D_SW_SAMP_DRV_C
#define D_SW_SAMP_DRV_C
#endif

/* include files */
#include "DSwSampDrv.h"


/* static variable declaration */
static DSwSampBitStr iDSwSampVar[D_SW_SAMP_OBJ_NUM]; 

static uint8_t iDSwSampTimes;


uint8_t  gDSwLogicSt[D_SW_SAMP_OBJ_NUM];


/* static function declaration */
static void DSwReadVal(uint8_t ObjNum);

/* static function definition */


/* BEGIN_FUNCTION_HDR
*********************************************************************************************************
* Function Name : void DSwSampTimesSet(uint8_t aDSwSampTimes)
* Description   : The function is used to set diital switch's sample times.
*
* Inputs        : uint8_t aDSwSampTimes
*
* Outputs       : none
*
* Limitations   : nothing
*********************************************************************************************************
END_FUNCTION_HDR */ 
void DSwSampTimesSet(uint8_t aDSwSampTimes)
{
    iDSwSampTimes = aDSwSampTimes;
}

/* BEGIN_FUNCTION_HDR
*********************************************************************************************************
* Function Name : void DSwSampTimesCntClr(DSwSampBitStr *pParaDSwSampBitStrVar)
* Description   : The function is used to clear the counter of diital switch's sample times.
*
* Inputs        : DSwSampBitStr *pParaDSwSampBitStrVar
*
* Outputs       : none
*
* Limitations   : nothing
*********************************************************************************************************
END_FUNCTION_HDR */
void DSwSampTimesCntClr(void)
{
    //
}


/* BEGIN_FUNCTION_HDR
*********************************************************************************************************
* Function Name : static DSwReadVal(uint8_t ObjNum)
* Description   : The function is used to get diital switch's physical status.
*
* Inputs        : uint8_t ObjNum
*
* Outputs       : None
*
* Limitations   : nothing
*********************************************************************************************************
END_FUNCTION_HDR */ 

static void DSwReadVal(uint8_t ObjNum)
{
    uint8_t lDSwInSt = 0u;
    uint32_t value = 0;
    float tirgValue = 0.0;
    if((icDSwPortPin[ObjNum].m_PortAddr & D_SW_SAMP_EXTER_FUN_FLA) == D_SW_SAMP_EXTER_FUN_FLA)
    {
        D_SW_EXTER_SAMP_FUN(icDSwPortPin[ObjNum].m_PinOffset,value);
        
        if(TRIG_SAMPLE_JUDG_HIGH > TRIG_SAMPLE_JUDG_LOW)
        {
            tirgValue = (value > TRIG_SAMPLE_JUDG_HIGH)? TRIG_SAMPLE_JUDG_HIGH:value;
            tirgValue = (value < TRIG_SAMPLE_JUDG_LOW)? TRIG_SAMPLE_JUDG_LOW:value;
            value = (uint32_t)((tirgValue - TRIG_SAMPLE_JUDG_LOW)*256.0f /(TRIG_SAMPLE_JUDG_HIGH-TRIG_SAMPLE_JUDG_LOW));  
        }
        else
        {
            //do nothing
        }
        
        iDSwSampVar[ObjNum].m_ASwInValueSum += (value > 255u)? 255u:value;
        iDSwSampVar[ObjNum].m_DSwInStCnt++;
        if(iDSwSampVar[ObjNum].m_DSwInStCnt >iDSwSampTimes )
        {
            iDSwSampVar[ObjNum].m_DSwInSt = iDSwSampVar[ObjNum].m_ASwInValueSum /iDSwSampVar[ObjNum].m_DSwInStCnt;
            iDSwSampVar[ObjNum].m_DSwInStCnt = 0u;
            iDSwSampVar[ObjNum].m_ASwInValueSum = 0;
        }
        else
        {
            /* Do nothing */
        }
    }
    else
    {
        if( D_SW_GPIO_SAMP_FUN(icDSwPortPin[ObjNum].m_PortAddr, icDSwPortPin[ObjNum].m_PinOffset) == 0u)
        {
           lDSwInSt = 0u;
        }
        else
        {
           lDSwInSt = 1u;
        }
        if(lDSwInSt != iDSwSampVar[ObjNum].m_DSwInStRd)
        {
            iDSwSampVar[ObjNum].m_DSwInStCnt = 1u;
        }
        else
        {
            if(iDSwSampVar[ObjNum].m_DSwInStCnt >= 1u)
            {
                iDSwSampVar[ObjNum].m_DSwInStCnt++;
                if(iDSwSampVar[ObjNum].m_DSwInStCnt >iDSwSampTimes )
                {
                    iDSwSampVar[ObjNum].m_DSwInStCnt = 0u;
                    iDSwSampVar[ObjNum].m_DSwInSt    = lDSwInSt;
                }
                else
                {
                    /* Do nothing */
                }
            }
            else
            {
                /* Do nothing */
            }
        }
        iDSwSampVar[ObjNum].m_DSwInStRd = lDSwInSt;
    }
}

/* public function declaration */

/* BEGIN_FUNCTION_HDR
*********************************************************************************************************
* Function Name : void DSwSampInit(void)
* Description   : The function is used to initialize diital switch struct.
*
* Inputs        : None
*
* Outputs       : None
*
* Limitations   : nothing
*********************************************************************************************************
END_FUNCTION_HDR */ 
void DSwSampInit(void)
{
    uint8_t i;
    for(i = 0; i < D_SW_SAMP_OBJ_NUM; i++)
    {
        /* Init inner variable based on switch type */
      #if (D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN)
      
        iDSwSampVar[i].m_DSwInStRd = SW_NOEFFECT_ST;
        iDSwSampVar[i].m_DSwInSt = SW_NOEFFECT_ST; 
        iDSwSampVar[i].m_DSwInLastSt = SW_NOEFFECT_ST;
        
        gDSwLogicSt[i] = SW_NOEFFECT_ST;
        
      #else /*(D_SW_SAMP_INIT_NOEFFECT_CFG != FUN_EN) */
      
        if( ( (icDSwSampMode[i] & D_SW_SAMP_MODE_GENERAL_MASK) == D_SW_SAMP_MODE_INV)
       #if(D_SW_SAMP_SET_LOW_GOING_CFG == FUN_EN)
           ||((icDSwSampMode[i]  & D_SW_SAMP_MODE_GENERAL_MASK) == D_SW_SAMP_MODE_SET_LOW_GOING)
       #endif
       #if(D_SW_SAMP_TOGGLE_LOW_GOING_CFG == FUN_EN)
           ||((icDSwSampMode[i] & D_SW_SAMP_MODE_GENERAL_MASK)== D_SW_SAMP_MODE_TOGGLE_LOW_GOING)
       #endif
         ) 
        {  
            iDSwSampVar[i].m_DSwInStRd = D_SW_SAMP_HIGH; 
            iDSwSampVar[i].m_DSwInSt = D_SW_SAMP_HIGH;
            iDSwSampVar[i].m_DSwInLastSt = D_SW_SAMP_HIGH;  
        }
        else
        { 
            iDSwSampVar[i].m_DSwInStRd = D_SW_SAMP_LOW;
            iDSwSampVar[i].m_DSwInSt = D_SW_SAMP_LOW;
            iDSwSampVar[i].m_DSwInLastSt = D_SW_SAMP_LOW;
        }
        iDSwSampVar[i].m_ASwInValueSum = 0;

      #endif /* (D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN) */
      
    }
    
    iDSwSampTimes = D_SW_SAMP_DEFAULT_SAMP_TIMES;
}  


/* BEGIN_FUNCTION_HDR
*********************************************************************************************************
* Function Name : void DSwSamp(void)
* Description   : The function is used to get diital switch's logical status.
*
* Inputs        : None
*
* Outputs       : None
*
* Limitations   : nothing
*********************************************************************************************************
END_FUNCTION_HDR */
void DSwSamp(void)
{
    uint8_t i;
    for(i = 0; i < D_SW_SAMP_OBJ_NUM; i++)
    {
        DSwReadVal(i);/*read every port*/
        switch(icDSwSampMode[i] & D_SW_SAMP_MODE_GENERAL_MASK)
        {
            /* Under the mode,DSw's logical status will be always cleared to OFF */
          #if(D_SW_SAMP_OFF_CFG == FUN_EN)
            case D_SW_SAMP_MODE_OFF: 
               gDSwLogicSt[i] = SW_OFF_ST;
               break;
          #endif
            
            /* Under the mode,DSw's logical status will be always cleared to ON */                
          #if(D_SW_SAMP_ON_CFG == FUN_EN)
            case D_SW_SAMP_MODE_ON:  
               gDSwLogicSt[i] = SW_ON_ST;
               break;
          #endif
            
            /*Under the mode,DSw's logical status will be totally rested on DSw's physical status */
          #if(D_SW_SAMP_DIRECT_CFG == FUN_EN)
            case D_SW_SAMP_MODE_DIRECT:
               gDSwLogicSt[i] = iDSwSampVar[i].m_DSwInSt;
               break;
          #endif
            
            /*Under the mode,DSw's logical status will be set to inverse status of DSw's physical status */
          #if(D_SW_SAMP_INV_CFG == FUN_EN)
            case D_SW_SAMP_MODE_INV: 
             #if(D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN)
               if(iDSwSampVar[i].m_DSwInSt != SW_NOEFFECT_ST)
             #endif
               {
                   gDSwLogicSt[i] = (~iDSwSampVar[i].m_DSwInSt) & D_SW_SAMP_1_BITS_MASK;
               }
             #if(D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN)
               else
             #endif
               {
                   /* do nothing */
               }
               break;
          #endif
            
            /*Under the mode,if DSw's physical status sampled is low edge going, 
            DSw's logical status will be set to ON,or set to OFF.Vice versa. */                     
          #if(D_SW_SAMP_SET_LOW_GOING_CFG == FUN_EN)
            case D_SW_SAMP_MODE_SET_LOW_GOING: 
               if(  (D_SW_SAMP_HIGH == iDSwSampVar[i].m_DSwInLastSt)
                  &&(D_SW_SAMP_LOW == iDSwSampVar[i].m_DSwInSt) )
               {
                   gDSwLogicSt[i] = SW_ON_ST;
               }
               else
               {
                   /* do nothing */
               }
               if(  (D_SW_SAMP_LOW  == iDSwSampVar[i].m_DSwInLastSt)
                  &&(D_SW_SAMP_HIGH == iDSwSampVar[i].m_DSwInSt) )
               {
                   gDSwLogicSt[i] = SW_OFF_ST;
               }                               
               else
               {
                   /* do nothing */
               }
               break;
          #endif
          
            /*Under the mode,if DSw's physical status sampled is hih edge going, 
            DSw's logical status will be set to ON,or set to OFF.Vice versa. */                    
          #if(D_SW_SAMP_SET_HIGH_GOING_CFG == FUN_EN)
            case D_SW_SAMP_MODE_SET_HIGH_GOING:
               if(  (D_SW_SAMP_LOW  == iDSwSampVar[i].m_DSwInLastSt)
                  &&(D_SW_SAMP_HIGH == iDSwSampVar[i].m_DSwInSt) )
               {
                    gDSwLogicSt[i] = SW_ON_ST;
               }
               else
               {
                   /* do nothing */
               }
               if(  (D_SW_SAMP_HIGH == iDSwSampVar[i].m_DSwInLastSt)
                  &&(D_SW_SAMP_LOW  == iDSwSampVar[i].m_DSwInSt) )
               {
                   gDSwLogicSt[i] = SW_OFF_ST;
               }                               
               else
               {
                   /* do nothing */
               }
               break;
          #endif
            /*Under the mode,if DSw's physical status sampled is low edge going, 
            DSw's logical status will be toggled. */                 
          #if(D_SW_SAMP_TOGGLE_LOW_GOING_CFG == FUN_EN)
            case D_SW_SAMP_MODE_TOGGLE_LOW_GOING:
               if(  (D_SW_SAMP_HIGH == iDSwSampVar[i].m_DSwInLastSt)
                  &&(D_SW_SAMP_LOW  == iDSwSampVar[i].m_DSwInSt) )
               {
                 #if(D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN)
                   if(gDSwLogicSt[i] != SW_NOEFFECT_ST)
                 #endif
                   {
                    
                       gDSwLogicSt[i] = (~gDSwLogicSt[i]) & D_SW_SAMP_1_BITS_MASK;
                   } 
                 #if(D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN) 
                   else
                   {
                       gDSwLogicSt[i] = SW_ON_ST; /* take NOEFFECT_ST as OFF_ST*/
                   }
                 #endif
               }
               else
               {
                   /* do nothing */
               }                        
               break;
          #endif
            /*Under the mode,if DSw's physical status sampled is hih edge going, 
            DSw's logical status will be toggled. */
          #if(D_SW_SAMP_TOGGLE_HIGH_GOING_CFG == FUN_EN)
            case D_SW_SAMP_MODE_TOGGLE_HIGH_GOING:
               if(  (D_SW_SAMP_LOW  == iDSwSampVar[i].m_DSwInLastSt)
                  &&(D_SW_SAMP_HIGH == iDSwSampVar[i].m_DSwInSt) )
               {
                 #if(D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN)
                   if(gDSwLogicSt[i] != SW_NOEFFECT_ST)
                 #endif
                   {                    
                       gDSwLogicSt[i] = (~gDSwLogicSt[i]) & D_SW_SAMP_1_BITS_MASK;
                   }
                 #if(D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_EN) 
                   else
                   {
                       gDSwLogicSt[i] = SW_ON_ST; /* take NOEFFECT_ST as OFF_ST*/
                   }
                 #endif
                   
               }
               else
               {
                   /* do nothing */
               }
               break; 
          #endif
            default:
               break;
        } 

        /********* hjcao added **************/
      #if (D_SW_SAMP_NOEF_TO_ON_AS_OFF == FUN_EN)
        if( (gDSwLogicSt[i] == SW_NOEFFECT_ST) && 
            ((icDSwSampMode[i] & DSS_MODE_NOEF_TO_ON_PROC_MASK) == DSS_NOEF_TO_ON_AS_OFF_YES))
        {
            gDSwLogicSt[i] = SW_OFF_ST;  /*take NOEFFECT_ST as OFF_ST*/
        } 
        else
        {
            /* do nothing */
        }
      #endif

        iDSwSampVar[i].m_DSwInLastSt = iDSwSampVar[i].m_DSwInSt;

    }
}


void GetTrig_GripValue(uint8_t *apTrigValue, const int16_t aRawTrig)
{
    static uint16_t iLastTrigValue = 0u;
    static uint8_t iSampCnt = 0;
    
    static uint16_t iTrigInValueSum  = 0u;
    uint32_t value = 0;
    float tirgValue = 0.0f;
    
    
    if(TRIG_SAMPLE_JUDG_HIGH > TRIG_SAMPLE_JUDG_LOW)
    {
        tirgValue = (aRawTrig - TRIG_SAMPLE_JUDG_LOW)*256.0f /(TRIG_SAMPLE_JUDG_HIGH - TRIG_SAMPLE_JUDG_LOW);
        if(tirgValue > 0.0f)
        {
            value = (uint32_t)(tirgValue);  
        }
        else
        {
            value = 0u;  
        }
    }
    else
    {
        tirgValue = (TRIG_SAMPLE_JUDG_LOW - aRawTrig)*256.0f /(TRIG_SAMPLE_JUDG_LOW - TRIG_SAMPLE_JUDG_HIGH);
        if(tirgValue > 0.0f)
        {
            value = (uint32_t)(tirgValue);  
        }
        else
        {
            value = 0u;  
        }
    }
    iTrigInValueSum += value;
    iSampCnt++;
    
    if(iSampCnt >iDSwSampTimes )
    {
        iLastTrigValue = (iTrigInValueSum / iSampCnt);
        
        iTrigInValueSum = 0u;
        iSampCnt = 0;
    }
    else
    {
        /* Do nothing */
    }
    *apTrigValue = (iLastTrigValue > 255)? 255:iLastTrigValue;
}

void ClearSwSampFilterCnt(void)
{
   uint8_t i;
    for(i = 0; i < D_SW_SAMP_OBJ_NUM; i++)
    {
        iDSwSampVar[i].m_DSwInStCnt = 0u;
    }
}




