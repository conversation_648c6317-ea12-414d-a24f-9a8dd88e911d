/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Ist8801CtrDrv.h
************************************************************************************************
*   Project/Product :  
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     :  Ist8801
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2022/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
    
#ifndef IST8801_CTRL_DRV_H
#define IST8801_CTRL_DRV_H

/*Include files*/
#include "Ist8801CtrlAppExt.h"

/* support module */
#include "includes.h"

/*****following definitions can not be used outside this driver******/
/* macro definition */
#define IST8801_I2C_HANDLE                 (I2C_MASTER_TWI0_INST)
#define IST8801_I2C_ADDR_TRIG               0x30    //8bit
#define IST8801_I2C_ADDR_GRIP               0x36    //8bit



#define IST8801_LOW_THRESHOLD               0
#define IST8801_HIGH_THRESHOLD              1

//Defines a register address of the IST8801
#define IST8801_REG_PERSINT                 0x00
#define IST8801_REG_INTSRS                  0x01
#define IST8801_REG_LTHL                    0x02
#define IST8801_REG_LTHH                    0x03
#define IST8801_REG_HTHL                    0x04
#define IST8801_REG_HTHH                    0x05
#define IST8801_REG_I2CDIS                  0x06
#define IST8801_REG_SRST                    0x07
#define IST8801_REG_CNTL1                   0x08
//CNTL1 value
#define IST8801_CNTL1_STANDBY                    0x00
#define IST8801_CNTL1_ODR_10HZ                     0x10
#define IST8801_CNTL1_ODR_6_7HZ                 0x20
#define IST8801_CNTL1_ODR_5HZ                    0x30
#define IST8801_CNTL1_ODR_80HZ                     0x40    
#define IST8801_CNTL1_ODR_40HZ                    0x50
#define IST8801_CNTL1_ODR_26_7HZ                0x60
#define IST8801_CNTL1_ODR_20HZ                     0x70
#define IST8801_CNTL1_SINGLE_MEASUREMENT        0x80
#define IST8801_CNTL1_ODR_100HZ                 0x90
#define IST8801_CNTL1_ODR_50HZ                  0xA0
#define IST8801_CNTL1_ODR_1HZ                    0xB0
#define IST8801_CNTL1_ODR_200HZ                 0xC0
#define IST8801_CNTL1_ODR_250HZ                 0xD0
#define IST8801_CNTL1_ODR_320HZ                 0xE0
#define IST8801_CNTL1_ODR_500HZ                 0xF0
#define IST8801_REG_WMI                     0x09
#define IST8801_REG_CNTL2                   0x0D
#define IST8801_REG_STAT1                   0x10
#define IST8801_REG_DATAL                   0x11
#define IST8801_REG_DATAH                   0x12
#define IST8801_REG_TEMPL                   0x13
#define IST8801_REG_TEMPH                   0x14
#define IST8801_REG_ACTR                    0x20
#define IST8801_REG_IFCNTL                    0x40
#define IST8801_REG_OSRCNTL                    0x6C
#define IST8801_REG_TSTCNTL                 0x76
#define IST8801_REG_INFO                    0x87

#define IST8801_DEVICE_ID                    0x81

#define IST8801_SLA_1                         0x0C
#define IST8801_SLA_2                         0x0F

#define IST8801_CNTL1_SETTING                IST8801_CNTL1_ODR_50HZ

#define GAIN_1_TIME                         (0x0 << 5) //Reserved
#define GAIN_2_TIME                         (0x1 << 5) //for 40mT
#define GAIN_4_TIME                         (0x2 << 5) //for 20mT
#define GAIN_8_TIME                         (0x3 << 5) //for 10mT
#define ADC_RES_16_BIT                      (0x0 << 1)
#define ADC_RES_15_BIT                      (0x1 << 1)
#define ADC_RES_14_BIT                      (0x2 << 1)
#define ADC_RES_13_BIT                      (0x3 << 1)
#define ADC_RES_12_BIT                      (0x4 << 1)
#define ADC_RES_11_BIT                      (0x5 << 1)
#define ADC_RES_10_BIT                      (0x6 << 1)
#define ADC_RES_9_BIT                       (0x7 << 1)
#define ADC_RES_8_BIT                       (0x8 << 1)    

/* function declar */
#define HALL_CTRL_DELAY_US(time)             nrf_delay_us(time)
#define HALL_CTRL_DELAY_MS(time)             nrf_delay_ms(time)
#define HALL_CTRL_PRINTF(...)                NRF_LOG_INFO( __VA_ARGS__)
#endif




