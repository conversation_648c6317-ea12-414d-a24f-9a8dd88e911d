/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : I2cRWAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef I2C_RW_APP_EXT_H
#define I2C_RW_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "nrf_drv_twi.h"

/*declaration range definition*/
#ifdef  I2C_RW_DRV_C
#define I2C_RW_DRV_APP_EXT
#else 
#define I2C_RW_DRV_APP_EXT extern
#endif

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define I2C_RW_ST_SUCCESS     (0x00u)
#define I2C_RW_ST_ERROR       (0x01u)

#define I2C_MASTER_TWI0_INST     (0x00u)
#define I2C_MASTER_TWI1_INST     (0x01u)
/* data type definiton  */


/* variable definition */
 
/* function declaration */
I2C_RW_DRV_APP_EXT uint8_t I2C_Write(uint8_t twi_instance_index, uint8_t aSlaveAddr,uint8_t* apRegAddr,uint8_t aRegAddrLen, uint8_t *pData,uint16_t aDataNum);
I2C_RW_DRV_APP_EXT uint8_t I2C_Read(uint8_t twi_instance_index, uint16_t aSlaveAddr,uint8_t* apRegAddr,uint8_t aRegAddrLen,uint8_t *pData, uint16_t aDataNum);
I2C_RW_DRV_APP_EXT uint8_t I2C_WriteOneReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint8_t aRegAddr,uint8_t *pData,  uint16_t aDataLen);
I2C_RW_DRV_APP_EXT uint8_t I2C_WriteTwoReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint16_t aRegAddr,uint8_t *pData,  uint16_t aDataLen);
I2C_RW_DRV_APP_EXT uint8_t I2C_ReadOneReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint8_t aRegAddr,uint8_t *pData,  uint16_t aDataLen);
I2C_RW_DRV_APP_EXT uint8_t I2C_ReadTwoReg(uint8_t twi_instance_index,uint8_t aSlaveAddr, uint16_t aRegAddr,uint8_t *pData,  uint16_t aDataLen);
I2C_RW_DRV_APP_EXT void I2c_init(void);
I2C_RW_DRV_APP_EXT void I2c_uninit(void);

#endif	/* end of header file */
