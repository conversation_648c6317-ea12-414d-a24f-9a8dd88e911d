/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : I2cRWAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/29    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef I2C_RW_DRV_H
#define I2C_RW_DRV_H


/*Include files*/
#include "I2cRWAppExt.h"

/* support module */
#include "includes.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
#define I2C_TIMEOUT              1u    /*2 * system tick = 2ms*/
#define I2C_ERR_MAX_NUM          1u
#define I2C_SEND_MAX_NUM         64u  /*data number one times*/
#define I2C_REG_ADDR_MAX_NUM     4u

/*config enable I2C  number*/

#define  I2C_INSTANCE_ENABLE_1    1u    /*1: enable ,0: disable*/
#if SYS_CFG_USE_TP == true
    #define  I2C_INSTANCE_ENABLE_2    1u    /*1: enable ,0: disable*/
#else
    #define  I2C_INSTANCE_ENABLE_2    0u    /*1: enable ,0: disable*/  
#endif
#define  I2C_INSTANCE_ENABLE_3    0u    /*1: enable ,0: disable*/


#define I2C_1_MASTER_TWI_INST     0
#define I2C_1_SDA_PIN             SYS_CFG_PIN_HALL_I2C_SDA
#define I2C_1_SCK_PIN             SYS_CFG_PIN_HALL_I2C_SCL

#define I2C_1_FREQUENCY           NRF_DRV_TWI_FREQ_400K
#define I2C_1_IRQ_PRIORITY        APP_IRQ_PRIORITY_MID
#define I2C_1_CLEAR_BUS_INIT_EN   false
#define I2C_1_HOLD_BUS_UNINIT_EN  false

#define I2C_2_MASTER_TWI_INST     1
#if SYS_CFG_USE_TP == true
    #define I2C_2_SDA_PIN             SYS_CFG_PIN_TP_I2C_SDA
    #define I2C_2_SCK_PIN             SYS_CFG_PIN_TP_I2C_SCL
#endif

#define I2C_2_FREQUENCY           NRF_DRV_TWI_FREQ_400K
#define I2C_2_IRQ_PRIORITY        APP_IRQ_PRIORITY_MID
#define I2C_2_CLEAR_BUS_INIT_EN   false
#define I2C_2_HOLD_BUS_UNINIT_EN  false



/*  structure definiton  */

      


#endif
