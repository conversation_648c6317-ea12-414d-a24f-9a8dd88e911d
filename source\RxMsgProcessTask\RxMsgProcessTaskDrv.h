/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : RxMsgProcessTaskDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/06    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef RX_MSG_PROCESS_TASK_DRV_H
#define RX_MSG_PROCESS_TASK_DRV_H

/*Include files*/
#include "RxMsgProcessTaskAppExt.h"

/* support module */
#include "includes.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
#define SYS_RUN_MODEL_NORMAL                0x00
#define SYS_RUN_MODEL_MANAGE                0x01
/*ack status code*/
#define MSG_ACK_BUF_SIZE                    64u

/*ack status code*/
#define MSG_ACK_ST_SUCC                     0x00
#define MSG_ACK_ST_FAIL                     0x01
#define MSG_ACK_ST_CHECK_ERR                0x02
#define MSG_ACK_ST_NO_AUTH                  0x03
#define MSG_ACK_ST_CMD_ERR                  0xFF

#define SYS_GUN_CONN_COUNT_DOWN_MAX         300U
#define SYS_GUN_TRIG_COUNT_DOWN_MAX         30u
#define SYS_GUN_BAT_PER_RELOAD_CNT_MAX		2u	
#define SYS_GUN_BAT_SWITCH_RELOAD_CNT_MAX	2u		
/* data type definiton  */
//typedef enum
//{
//    uart_cmd_get_magazine_clip_id = 0x04,
//    uart_cmd_set_magazine_clip_id = 0x05,
//}UartCmdType;


typedef enum
{
    cmd_type_set_notify_type    = 0x00,
    cmd_type_bsp                = 0x01,
    cmd_type_ble                = 0x02,
    cmd_type_manufature         = 0x03,
    cmd_type_blobs              = 0x04,
    cmd_type_gun                = 0x05,
}DataPipeCmdType;

typedef enum{
    Gun_SubCmd_Temp_Bullet_Num_Set = 0x01,
    Gun_SubCmd_Perpetual_Bullet_Num_Set = 0x02,
    Gun_SubCmd_Life_Time_Set = 0x03,
	Gun_SubCmd_Debug_Bullet_set = 0xff,
}DataPipeSubCmd_gun;

typedef enum{
    Notify_SubCmd_none = 0x00,
    Notify_SubCmd_ypr = 0x01,
    Notify_SubCmd_q4 = 0x02,
    Notify_SubCmd_acc_and_gyro = 0x03, 
		Notify_SubCmd_trig_grip_raw = 0x04,
    Notify_SubCmd_gun_magazine_clip = 0x05,
    Notify_SubCmd_ypr_accel = 0x10,
    Notify_SubCmd_ypr_gyro = 0x11,
    Notify_SubCmd_ypr_magnet = 0x12,
    Notify_SubCmd_ypr_accel_gyro = 0x20,
    Notify_SubCmd_TLY = 0x80,
    Notify_SubCmd_time_syn_test = 0xFF,
}DataPipeSubCmd_notify;

typedef enum{
    Bsp_SubCmd_none = 0x00,
    Bsp_SubCmd_motor_off = 0x10,
    Bsp_SubCmd_motor_on = 0x11,
    Bsp_SubCmd_led_off = 0x20,
    Bsp_SubCmd_led_red = 0x21,
    Bsp_SubCmd_led_blue = 0x22,
    Bsp_SubCmd_led_white = 0x23,
    Bsp_SubCmd_buzzer_off = 0x30,
    Bsp_SubCmd_buzzer_on = 0x31,
}DataPipeSubCmd_bsp;

typedef enum{
    Ble_SubCmd_type_none = 0x00,
    Ble_SubCmd_all_service = 0x10,
    Ble_SubCmd_lease_service = 0x11,
    Ble_SubCmd_reboot = 0x20,
    Ble_SubCmd_erase_bond = 0x21,
    Ble_SubCmd_shutdown = 0x22,
    Ble_SubCmd_sleep = 0x23,
    Ble_SubCmd_wakeup = 0x24,
    Ble_SubCmd_host_android = 0x30,
    Ble_SubCmd_host_ios = 0x31,
    Ble_SubCmd_host_wp = 0x32,
    Ble_SubCmd_host_other = 0x33,
    Ble_SubCmd_enter_dfu = 0x80,
}DataPipeSubCmd_ble;

typedef enum{
    Manufature_SubCmd_none = 0x00,
    Manufature_SubCmd_setsn = 0x01,
    Manufature_SubCmd_setmac = 0x02,
    Manufature_SubCmd_sethands = 0x03,
    Manufature_SubCmd_setdevicename = 0x04,
    Manufature_SubCmd_setmodelname = 0x05,
    Manufature_SubCmd_setTrigSwJudg = 0x06,
    Manufature_SubCmd_setRFRadio2440MHz = 0x10,
    Manufature_SubCmd_resetIMUMagCalib = 0x20,
    Manufature_SubCmd_enter_aging = 0x80,
    Manufature_SubCmd_enter_aging_Exit = 0x81,
    Manufature_SubCmd_change_adv_type = 0xAA,
}DataPipeSubCmd_Manufature;


/* data type definiton  */
#pragma pack(1)
typedef struct
{
    uint8_t b31_24;
    uint8_t b23_16;
    uint8_t b15_8;
    uint8_t b7_0;
} protocol_uint32_t;

typedef struct
{
    uint8_t mostByte;
    uint8_t midByte;
    uint8_t lastByte;
} protocol_uint24_t;

typedef struct
{
    uint8_t mostByte;
    uint8_t lastByte;
} protocol_int16_t;

typedef protocol_int16_t protocol_uint16_t;

typedef union
{
    uint8_t buffer[20];
    struct
    {
        uint8_t type;
        uint8_t id;
        protocol_uint24_t yaw;
        protocol_uint24_t pitch;
        protocol_uint24_t roll;
        protocol_uint24_t cnt;
        uint8_t trigger;    //
        union
        {
            uint8_t byte;
            struct
            {
                uint8_t Hand_right_bit :     1u;
                uint8_t Hand_left_bit :      1u; 
                uint8_t TouchPad_bit :       1u; 
                uint8_t Power_Home_bit :     1u; 
                uint8_t App_bit :            1u; 
                uint8_t Reserved_1_bit :     1u; 
                uint8_t Reserved_2_bit :     1u; 
                uint8_t Reserved_3_bit :     1u; 
            }bits;
        }IoKeys_uni;
        union
        {
            protocol_uint16_t Bytesxy[2u];
            uint16_t  HalfWordxy[2];
           struct
           {
               uint16_t Axis_x :               10u;
               uint16_t RockerX_flg:           2u;
               uint16_t reserved_x:            2u;
               uint16_t Event:                 2u;
            
               uint16_t Axis_y :               10u;
               uint16_t RockerY_flg:            2u;
               uint16_t reserved_y:            4u;
           }bits;
        }gamepad_t;
    }Euler_0x01_str;
    struct
    {
        uint8_t type;
        uint8_t id;
        protocol_uint24_t q0;
        protocol_uint24_t q1;
        protocol_uint24_t q2;
        protocol_uint24_t q3;
        uint8_t trigger;    //
        union
        {
            uint8_t byte;
            struct
            {
                uint8_t Hand_right_bit:     1u; 
                uint8_t Hand_left_bit:      1u; 
                uint8_t TouchPad_bit:       1u; 
                uint8_t Power_Home_bit:     1u; 
                uint8_t App_bit:            1u; 
                uint8_t Reserved_1_bit:     1u; 
                uint8_t Reserved_2_bit:     1u; 
                uint8_t Reserved_3_bit:     1u; 
            }bits;
        }IoKeys_uni;
        union
        {
            protocol_uint16_t xy[2u];
            uint16_t  HalfWordxy[2];
           struct
           {
               uint16_t Axis_x :               10u;
               uint16_t RockerX_flg:           2u;
               uint16_t reserved_x:            2u;
               uint16_t Event:                 2u;
            
               uint16_t Axis_y :               10u;
               uint16_t RockerY_flg:            2u;
               uint16_t reserved_y:            4u;
           }bits;
        }gamepad_t;
    }Q4_0x02_str;
    struct
    {
        uint8_t type;
        int16_t Acc_x;    
        int16_t Acc_y;      
        int16_t Acc_z;

        int16_t Gyro_x;
        int16_t Gyro_y;
        int16_t Gyro_z;    

        uint8_t  mGrip;        
        uint8_t  mTrig;
        union
        {
            uint8_t byte;
            struct
            {
                uint8_t Hand_right_bit:     1u; 
                uint8_t Hand_left_bit:      1u; 
                uint8_t TouchPad_bit:       1u; 
                uint8_t Power_Home_bit:     1u; 
                uint8_t App_bit:            1u; 
                uint8_t Reserved_1_bit:     1u; 
                uint8_t Reserved_2_bit:     1u; 
                uint8_t Reserved_3_bit:     1u; 
            }bits;
        }IoKeys_uni;
        union
        {
            protocol_uint16_t Bytesxy[2u];
            uint16_t  HalfWordxy[2];
            struct
            {
                uint16_t Axis_x :               10u;
                uint16_t RockerX_flg:           2u;
                uint16_t reserved_x:            2u;
                uint16_t Event:                 2u;

                uint16_t Axis_y :               10u;
                uint16_t RockerY_flg:           2u;
                uint16_t reserved_y:            4u;
            }bits;
        }gamepad_t;
    }Euler_0x03_str;
    struct
    {
        uint8_t type;
        protocol_uint16_t trigRawdata;    
        protocol_uint16_t GripRawdata;      
        protocol_uint16_t JoystickRawData_X;
        protocol_uint16_t JoystickRawData_Y;
    }Euler_0x04_str;
    struct
    {
        uint8_t             type;
        union
        {
            protocol_uint16_t Bytes;
            uint16_t  HalfWord;
        }MagazineClipId;
        uint8_t             MagazineClipBatPer;
        uint8_t             reserved[17];
    }MagazineClip_0x05_str;
    struct
    {
        uint8_t type;
        // struct
        // {
        //     uint16_t Axis_y :               12u;
        //     uint16_t Axis_x :               12u;
        // }gamepad_t;
        uint16_t   mLifeTimeRemain;
        uint8_t    mBulletNumRemain;
        union
        {
            uint8_t byte;
            struct
            {
                uint8_t Hand_right_bit:     1u; 
                uint8_t Hand_left_bit:      1u; 
                uint8_t TouchPad_bit:       1u; 
                uint8_t Power_Home_bit:     1u; 
                uint8_t App_bit:            1u; 
                uint8_t Reserved_1_bit:     1u; 
                uint8_t Reserved_2_bit:     1u; 
                uint8_t Reserved_3_bit:     1u; 
            }__attribute__((packed))bits;
        }__attribute__((packed))IoKeys_uni;
        uint8_t trigger;    //
        int16_t Gyro_z:12;
        int16_t Gyro_y:12;
        int16_t Gyro_x:12;
        
        int16_t Acc_z:12;
        int16_t Acc_y:12;
        int16_t Acc_x:12;
        int16_t roll:12;
        int16_t pitch:12;
        int16_t yaw:12;
        uint16_t id:4;
    }__attribute__((packed))Raw_0x20_str;
    struct 
    {
        uint8_t type;
        uint32_t time;
        uint8_t keys;
        signed gz:16;
        signed gy:16;
        signed gx:16;
        
        signed az:16;
        signed ay:16;
        signed ax:16;
        unsigned reserve_byte:12;
        unsigned id:4;
    }Raw_0x20_TsyncTest;
} SingleFrame_Protocol_Tx_t;
   
typedef union
{
    uint8_t buffer[20];
    struct
    {
        uint8_t command;
        uint8_t subtype;
        uint8_t data[18];
    }content;
}SingleFrame_Protocol_Rx_t;

typedef  struct
{
    uint8_t     mFormatType        :4u;
    uint8_t     mRfu               :4u;
    uint8_t     mMsgCode;
    uint16_t    mMsgLen;
    uint8_t     mCmdType;
    uint8_t     mCmdCode;
    uint8_t     mData[MSG_ACK_BUF_SIZE - 8u];
}AckFormat_str;

#pragma pack()
/* const variable definiton  */
const SysCfgdata_str gcSysDefaultCfg_t  = 
{
    100,        //Prefabricated bullet count
    0,          //Remain bullet count
    {0,0x00,0x00,0x00,0x00,0x00,0x00},//local mac cfg
    {0,0x00,0x00,0x00,0x00,0x00,0x00},//Host mac cfg
    {
        0,
        '0','1','2','3','4','5','6','7','8','9','0','A','B','\0'
    }, //PCBA SN
	#if SYS_CFG_92G_DEVICE == FUN_EN
    { 
        13,
        'A','I','O','-','E','l','e','c','-','9','2','G','\0'
    }, //devices name
    { 
        13,
        'A','I','O','-','E','l','e','c','-','9','2','G','\0'
    }, //dfu name
	#else
    { 
        14,
        'A','I','O','-','E','l','e','c','-','9','5','-','1','\0'
    }, //devices name
    { 
        14,
        'A','I','O','-','E','l','e','c','-','9','5','-','1','\0'
    }, //dfu name
	#endif
    { 
        12,
        '0','1','2','3','4','5','6','7','8','9','0','A','B','\0'
    }, //mode name  product SN
    SYS_CTRL_DIS,
    21, //mark ID
	#if SYS_CFG_92G_DEVICE == FUN_EN
    {0x31,0x47},
	#else
	{0x31,0x45},
	#endif
#ifdef MIRAGE2B_BOARD
    0x00, //dry batty
#else
    0x01, //li batty
#endif
    0x00, //old protocol
    data_type_ypr,
    0x01, //enable old calib service ,disable sys cfg service
    0x09,
    {2500,100,1000},  //motor pwm
    {100,50,0},   //blob r pwm
    {100,50,0},   //blob g pwm
    {100,50,0},   //blob b pwm
    0x0007,     //trigger raw data min
    0x0024,     //trigger raw data max
	0x0011,     //trigger raw data max
    20000         //gun life time
};

const dof9_calib_t gcSysDefaultImuCalibSet_t = 
{
    0,       //times
    0,   // accel offset x
    0,   // accel offset y
    0,   //accel offset  z
    1.000,   // accel scale x
    1.0,   // accel scale y
    1.0,   //accel scale  z
    0,   // gyro offset x
    0,   // gyro offset y
    0,   //gyro offset  z
    1,   // gyro scale x
    1,   // gyro scale y
    1,   //gyro scale  z
    magnet_source_xim_max_mean,
    0,   // magnet offset x
    0,   // magnet offset y
    0,   //magnet offset  z
    1.0,   // magnet scale x
    1.0,   // magnet scale y
    1.0,   //magnet scale  z
};
const ProducInfo_str ProducInfoDefault  =        //(BOOTLOADER_ADDRESS - 3*4096)
{
    {0xAA,0x55},
    {PROD_NAME},
    {PROD_HARD_VER},
    {PROD_PCBA_SN},
    {PROD_BT_MAC},
    {PROD_MANUFACTURE}
};
#endif /* End of header file */
