/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : BlePeripCtrlDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2018/07/25    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef BLE_PERIP_CTRL_DRV_C
#define BLE_PERIP_CTRL_DRV_C
#endif

/* include files */
#include "BlePeripCtrlDrv.h"

/* static function declaration */


/* static variable definition */
uint16_t   m_conn_handle          = BLE_CONN_HANDLE_INVALID;
BLE_BAS_DEF(m_bas);                                                 /**< Battery service instance. */
#if BLE_HRS_ENABLED
BLE_HTS_DEF(m_hts);                                                 /**< Heart rate service instance. */
#endif
NRF_BLE_GATT_DEF(m_gatt);                                           /**< GATT module instance. */
NRF_BLE_QWR_DEF(m_qwr);                                             /**< Context for the Queued Write module.*/

BLE_XIM_DEF(mImuNorService_t,NRF_SDH_BLE_TOTAL_LINK_COUNT);
BLE_XIM_DEF(mSysCfgService_t,NRF_SDH_BLE_TOTAL_LINK_COUNT);
BLE_XIM_DEF(mImuCalibService_t,NRF_SDH_BLE_TOTAL_LINK_COUNT);
BLE_XIM_DEF(mCalibCheckService_t,NRF_SDH_BLE_TOTAL_LINK_COUNT);


NRF_BLE_GQ_DEF(m_ble_gatt_queue,                                                    /**< BLE GATT Queue instance. */
               NRF_SDH_BLE_PERIPHERAL_LINK_COUNT,
               NRF_BLE_GQ_QUEUE_SIZE);
               
ble_Evt_handler_t  iBleEvtCallbackHandler = NULL;  

/**@brief Function for handling Queued Write Module errors.
 *
 * @details A pointer to this function will be passed to each service which may need to inform the
 *          application about an error.
 *
 * @param[in]   nrf_error   Error code containing information about what went wrong.
 */

static void nrf_qwr_error_handler(uint32_t nrf_error)
{
    APP_ERROR_HANDLER(nrf_error);
}
/**@brief Function for handling BLE events.
 *
 * @param[in]   p_ble_evt   Bluetooth stack event.
 * @param[in]   p_context   Unused.
 */
static void ble_evt_handler(ble_evt_t const * p_ble_evt, void * p_context)
{
    uint32_t err_code;

    switch (p_ble_evt->header.evt_id)
    {
        case BLE_GAP_EVT_CONNECTED:
            NRF_LOG_INFO("Connected handle =%x",p_ble_evt->evt.gap_evt.conn_handle);
            m_conn_handle = p_ble_evt->evt.gap_evt.conn_handle;
            err_code = nrf_ble_qwr_conn_handle_assign(&m_qwr, m_conn_handle);
            APP_ERROR_CHECK(err_code);
            set_conn_params_level(XIM_CONN_PARAMS_UPDATA_LEVEL);
            break;

        case BLE_GAP_EVT_DISCONNECTED:

            m_conn_handle = BLE_CONN_HANDLE_INVALID;
            break;

        case BLE_GAP_EVT_PHY_UPDATE_REQUEST:
        {
            NRF_LOG_INFO("PHY update request phys: %x",p_ble_evt->evt.gap_evt.params.phy_update.tx_phy);
            ble_gap_phys_t const phys =
            {
                .rx_phys = BLE_GAP_PHY_AUTO,
                .tx_phys = BLE_GAP_PHY_AUTO,
            };
            err_code = sd_ble_gap_phy_update(p_ble_evt->evt.gap_evt.conn_handle, &phys);
            APP_ERROR_CHECK(err_code);
        } break;

        case BLE_GAP_EVT_SEC_PARAMS_REQUEST:
            // Pairing not supported
            err_code = sd_ble_gap_sec_params_reply(m_conn_handle, BLE_GAP_SEC_STATUS_PAIRING_NOT_SUPP, NULL, NULL);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTS_EVT_SYS_ATTR_MISSING:
            // No system attributes have been stored.
            err_code = sd_ble_gatts_sys_attr_set(m_conn_handle, NULL, 0, 0);
            APP_ERROR_CHECK(err_code);
            break;

        case BLE_GATTC_EVT_TIMEOUT:
            // Disconnect on GATT Client timeout event.
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gattc_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
        break;
        case BLE_GATTS_EVT_TIMEOUT:
            // Disconnect on GATT Server timeout event.
            NRF_LOG_DEBUG("GATT Server Timeout.");
            err_code = sd_ble_gap_disconnect(p_ble_evt->evt.gatts_evt.conn_handle,
                                             BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
            APP_ERROR_CHECK(err_code);
            break;

        default:
            // No implementation needed.
            break;
    }
    if(iBleEvtCallbackHandler != NULL)
    {
        iBleEvtCallbackHandler(p_ble_evt);
    }
}
/* public function definition */
/**@brief Function for performing battery measurement and updating the Battery Level characteristic
 *        in Battery Service.
 */
 void battery_level_update(uint8_t  battery_level)
{
    ret_code_t err_code;

    err_code = ble_bas_battery_level_update(&m_bas, battery_level, BLE_CONN_HANDLE_ALL);
    if ((err_code != NRF_SUCCESS) &&
        (err_code != NRF_ERROR_INVALID_STATE) &&
        (err_code != NRF_ERROR_RESOURCES) &&
        (err_code != NRF_ERROR_BUSY) &&
        (err_code != BLE_ERROR_GATTS_SYS_ATTR_MISSING)
       )
    {
        APP_ERROR_HANDLER(err_code);
    }
}
void battery_temperature_update(float temperature_degree)
{
#if BLE_HRS_ENABLED
    ble_hts_meas_t meas;
    memset(&meas, 0, sizeof(ble_hts_meas_t));
    meas.temp_in_celcius.exponent = -3;
    meas.temp_in_celcius.mantissa = (int32_t)(temperature_degree*1000);
    ble_hts_measurement_send(&m_hts, &meas);
#endif
}

void bas_service_init(void)
{
    uint32_t err_code;
    ble_bas_init_t   bas_init;
    // Initialize Battery Service.
    memset(&bas_init, 0, sizeof(bas_init));

    bas_init.bl_rd_sec        = SEC_OPEN;
    bas_init.bl_cccd_wr_sec   = SEC_OPEN;
    bas_init.bl_report_rd_sec = SEC_OPEN;

    bas_init.evt_handler          = NULL;
    bas_init.support_notification = true;
    bas_init.p_report_ref         = NULL;
    bas_init.initial_batt_level   = 100;

    err_code = ble_bas_init(&m_bas, &bas_init);
    APP_ERROR_CHECK(err_code);

}

void health_thermometer_service(void)
{
#if BLE_HRS_ENABLED
    uint32_t err_code;
    ble_hts_init_t hts_init;
    memset(&hts_init, 0, sizeof(ble_hts_init_t));
     hts_init.evt_handler                 = NULL;
    hts_init.p_gatt_queue                = &m_ble_gatt_queue;
    hts_init.error_handler               = NULL;
    hts_init.temp_type_as_characteristic = 0;
    hts_init.temp_type                   = BLE_HTS_TEMP_TYPE_BODY;

    // Here the sec level for the Health Thermometer Service can be changed/increased.
    hts_init.ht_meas_cccd_wr_sec = SEC_JUST_WORKS;
    hts_init.ht_type_rd_sec      = SEC_OPEN;

    err_code = ble_hts_init(&m_hts, &hts_init);
    APP_ERROR_CHECK(err_code);
#endif
}

void Ble_dis_service_init(ble_srv_utf8_str_t manufact_name_t,ble_srv_utf8_str_t model_num_t,ble_srv_utf8_str_t serial_num_t,ble_srv_utf8_str_t hw_rev_t,ble_srv_utf8_str_t fw_rev_t,ble_srv_utf8_str_t sw_rev_t)
{
    ret_code_t         err_code;
    ble_dis_init_t     dis_init;

    memset(&dis_init, 0, sizeof(dis_init)); 
    dis_init.dis_char_rd_sec = SEC_OPEN;
    
    dis_init.manufact_name_str = manufact_name_t;
    dis_init.model_num_str = model_num_t;
    dis_init.serial_num_str = serial_num_t;
    dis_init.hw_rev_str = hw_rev_t;
    dis_init.fw_rev_str = fw_rev_t;
    dis_init.sw_rev_str = sw_rev_t;

    err_code = ble_dis_init(&dis_init);
    APP_ERROR_CHECK(err_code);
}
uint32_t Ble_xim_service_init(ble_xim_data_handler_t  BleServiceEvtHandler,uint8_t isOldCalibEn,uint8_t isSysCfgEn)
{
    ret_code_t                 err_code = NRF_SUCCESS;
    
    mImuNorService_t.notify_enable = 1u;
    mImuNorService_t.uuid_serves = BLE_BASE_UUID_IMU_NOR;
    mImuNorService_t.uuid_tx = BLE_UUID_TX_CHARACTERISTIC_IMU_NOR;
    mImuNorService_t.uuid_rx =  BLE_UUID_RX_CHARACTERISTIC_IMU_NOR;
    mImuNorService_t.uuid_type = BLE_UUID_TYPE_BLE;
    err_code = ble_xim_Serves_init(&mImuNorService_t,BleServiceEvtHandler);
    if (err_code != NRF_SUCCESS)
    {
         NRF_LOG_INFO("mImuNorService_t init fail %x \n",err_code);
        return err_code;
    }
    if(isSysCfgEn)
    {
        mSysCfgService_t.notify_enable = 1u;
        mSysCfgService_t.uuid_serves = BLE_BASE_UUID_SYS_CFG;
        mSysCfgService_t.uuid_tx = BLE_UUID_TX_CHARACTERISTIC_SYS_CFG;
        mSysCfgService_t.uuid_rx =  BLE_UUID_RX_CHARACTERISTIC_SYS_CFG;
        mSysCfgService_t.uuid_type = BLE_UUID_TYPE_BLE;
        err_code = ble_xim_Serves_init(&mSysCfgService_t,BleServiceEvtHandler);
        if (err_code != NRF_SUCCESS)
        {
             NRF_LOG_INFO("mSysCfgService_t init fail %x\n",err_code);
            return err_code;
        } 
    }
    if(isOldCalibEn)
    {
        mImuCalibService_t.notify_enable = 1u;
        mImuCalibService_t.uuid_serves = BLE_BASE_UUID_IMU_CALIB;
        mImuCalibService_t.uuid_tx = BLE_UUID_TX_CHARACTERISTIC_IMU_CALIB;
        mImuCalibService_t.uuid_rx =  BLE_UUID_RX_CHARACTERISTIC_IMU_CALIB;
        mImuCalibService_t.uuid_type = BLE_UUID_TYPE_BLE;
        err_code = ble_xim_Serves_init(&mImuCalibService_t,BleServiceEvtHandler);
        if (err_code != NRF_SUCCESS)
        {
             NRF_LOG_INFO("mImuCalibService_t init fail %x\n",err_code);
            return err_code;
        }
        
        mCalibCheckService_t.notify_enable = 1u;
        mCalibCheckService_t.uuid_serves = BLE_BASE_UUID_CALIB_CHECK;
        mCalibCheckService_t.uuid_tx = BLE_UUID_TX_CHARACTERISTIC_CALIB_CHECK;
        mCalibCheckService_t.uuid_rx =  BLE_UUID_RX_CHARACTERISTIC_CALIB_CHECK;
        mCalibCheckService_t.uuid_type = BLE_UUID_TYPE_BLE;
        err_code = ble_xim_Serves_init(&mCalibCheckService_t,BleServiceEvtHandler);
        if (err_code != NRF_SUCCESS)
        {
             NRF_LOG_INFO("mCalibCheckService_t init fail %x\n",err_code);
            return err_code;
        }
    }
    return err_code;
}
 /**@brief Function for initializing services that will be used by the application.
 *
 * @details Initialize the Heart Rate, Battery and Device Information services.
 */
void QueuedModule_init(void)
{

    ret_code_t         err_code;
    nrf_ble_qwr_init_t qwr_init = {0};
    // Initialize Queued Write Module.
    qwr_init.error_handler = nrf_qwr_error_handler;

    err_code = nrf_ble_qwr_init(&m_qwr, &qwr_init);
    APP_ERROR_CHECK(err_code);

}
/* static function definition */
/**@brief Function for handling events from the GATT library. */
void gatt_evt_handler(nrf_ble_gatt_t * p_gatt, nrf_ble_gatt_evt_t const * p_evt)
{
    if ((m_conn_handle == p_evt->conn_handle) && (p_evt->evt_id == NRF_BLE_GATT_EVT_ATT_MTU_UPDATED))
    {
        NRF_LOG_INFO("Data len is set to 0x%x", p_evt->params.att_mtu_effective - OPCODE_LENGTH - HANDLE_LENGTH);
    }
    NRF_LOG_INFO("ATT MTU exchange completed. central 0x%x peripheral 0x%x",
                  p_gatt->att_mtu_desired_central,
                  p_gatt->att_mtu_desired_periph);
    if(get_conn_params_level() == XIM_CONN_PARAMS_UPDATA_LEVEL)  //init params
    {
        conn_params_updata_start(0); //  best config
    }
}
/**@brief Function for initializing the GATT module. */
 void gatt_init(void)
{
    ret_code_t err_code;

    err_code = nrf_ble_gatt_init(&m_gatt, gatt_evt_handler);
    APP_ERROR_CHECK(err_code);

    err_code = nrf_ble_gatt_att_mtu_periph_set(&m_gatt,NRF_SDH_BLE_GATT_MAX_MTU_SIZE);
    APP_ERROR_CHECK(err_code);
}
uint16_t gerCurrentMtuSize(void)
{
    return nrf_ble_gatt_eff_mtu_get(&m_gatt, m_conn_handle) - 3u;
}

/**@brief Function for initializing the BLE stack.
 *
 * @details Initializes the SoftDevice and the BLE event interrupt.
 */
void ble_stack_init(ble_Evt_handler_t  BleEvtHandler)
{
    ret_code_t err_code;

    iBleEvtCallbackHandler = BleEvtHandler;
    err_code = nrf_sdh_enable_request();
    APP_ERROR_CHECK(err_code);

    // Configure the BLE stack using the default settings.
    // Fetch the start address of the application RAM.
    uint32_t ram_start = 0;
    err_code = nrf_sdh_ble_default_cfg_set(APP_BLE_CONN_CFG_TAG, &ram_start);
    NRF_LOG_INFO("SD Ram address =0x%x\n",ram_start);
    APP_ERROR_CHECK(err_code);

    // Enable BLE stack.
    err_code = nrf_sdh_ble_enable(&ram_start);
    APP_ERROR_CHECK(err_code);

    // Register a handler for BLE events.
    NRF_SDH_BLE_OBSERVER(m_ble_observer, APP_BLE_OBSERVER_PRIO, ble_evt_handler, NULL);
}

uint32_t XimService_data_send(uint16_t serviceBaseUUID,uint8_t   * p_data,uint16_t  length)
{
    ble_xim_t * p_xim = NULL;
    uint16_t  p_length =length;
    
    if(BLE_BASE_UUID_IMU_NOR == serviceBaseUUID)
    {
        p_xim = &mImuNorService_t;
    }
    else if(BLE_BASE_UUID_SYS_CFG == serviceBaseUUID)
    {
        p_xim = &mSysCfgService_t;
    }
    else if(BLE_BASE_UUID_IMU_CALIB == serviceBaseUUID)
    {
        p_xim = &mImuCalibService_t;
    }
    else if(BLE_BASE_UUID_CALIB_CHECK == serviceBaseUUID)
    {
        p_xim = &mCalibCheckService_t;
    }
    else
    {
        return  NRF_ERROR_INVALID_DATA;
    }
    return ble_xim_data_send(p_xim,p_data,&p_length,m_conn_handle);
}



/***********************************************END**********************************************/
