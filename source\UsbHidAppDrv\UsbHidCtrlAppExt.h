/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : UsbHidCtrlAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef USB_HID_CTRL_APP_EXT_H
#define USB_HID_CTRL_APP_EXT_H

/*Include files*/
#include "XimStdint.h"

/*declaration range definition*/
#ifdef USB_HID_CTRL_DRV_C 
  #define USB_HID_CTRL_APP_EXT 
#else 
  #define USB_HID_CTRL_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define USB_RW_ST_OK         (0x00u)
#define USB_RW_ST_BUSY       (0x01u)
#define USB_RW_ST_FAIL       (0x02u)

/* data type definiton  */
typedef  void   (*USBD_RX_CallbackTypeDef )(uint8_t * , uint16_t );

/* variable definition */
  
 
/* function declaration */
USB_HID_CTRL_APP_EXT uint8_t USBD_CUSTOM_HID_SendDataMsg(const uint8_t *report,uint16_t len);
USB_HID_CTRL_APP_EXT uint8_t USBD_CUSTOM_HID_SendAckMsg(const uint8_t *Ack,uint16_t len);
USB_HID_CTRL_APP_EXT  void USB_DEVICE_Init(USBD_RX_CallbackTypeDef apUsbCallback);
USB_HID_CTRL_APP_EXT  void StartUsb(void);
USB_HID_CTRL_APP_EXT  void StopUsb(void);
USB_HID_CTRL_APP_EXT  uint8_t GetUsbEnumSt(void);
#endif   /* end of header file */
