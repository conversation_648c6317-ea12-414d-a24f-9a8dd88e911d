//////////////////////////////////////////////////////////////////////////////////
////Function: IMU Fusion
////Author:   <PERSON>
////Project:  AR HMD IMU Fusion
////Version:  9.3.0.2
//////////////////////////////////////////////////////////////////////////////////
#include "xim_fusion.h"

#define  IMU_ALG_VER_STR   "XAGIA-Feasibility-AE-IMU-V9.3.0.2"
#define  M_GRAVITY        (float)1024.0f    //32G 
 
#define  FIX_PAI_TO_ANGLE_UNIT  (float)8.2f
//#define  M_GRAVITY       (float)16384.0f
#define  LEN_FILTER       ((int)20)
#define  LEN_NO_CAL       ((int)1500)
#define  M_PI                ((double)3.1415926535)
#define  M_RAD2DEG        ((double)57.295779513)
#define  M_DEG2RAD        ((double)0.0174532925)
#define  M_LSB2DPS        ((double)0.0010642251)

#define TWO_KP_SLIDETIME  ((double)5.0) //Decay time from Max to Min for proportional gain
#define TWO_KP_MAX        ((double)20.0)
#define TWO_KP_MIN        ((double)1.0)

// define
static dof9_calib_t     gDefualtConfigCalib;
//static dof3_float_t     default_mag_offset;
//static matrix_33_t      default_mag_matrix;
static dof9_calib_t     default_m_fix_params;
static dof3_float_t     prev_gyr[LEN_FILTER];
static float            g_magnetic_yaw = 0;
//static float            fast_yaw_list[LEN_FILTER];
//static uint16_t         yaw_cnt=0;
static uint16_t         pre_cnt=0;
//static uint16_t         simple_cnt = 0;
static quaternion_t     g_quat;
static ypr_t            g_ypr;


/* This puts magnetometer back in the mix depending on the state of mag selection
 *
* For example:  MAG_ON_ALWAYS means mag will always be used - if calibration is off on mags, this may make things drift depending on user orientation
*                                MAG_ON_FOR_SATURATION_CORRECT_ONLY will only be used until the estimator gain drops to the minimum.  Then it reverts to accel correction only.
*                                MAG_ON_NEVER will be like what Ximmerse had as of July 24 2017, i.e. no magnetometer in use
*/
typedef enum
{
    MAG_ON_NEVER = 0,
    MAG_ON_FOR_SATURATION_CORRECT_ONLY,
    MAG_ON_ALWAYS
} magnet_use_t;

//static magnet_use_t mag_use = MAG_ON_FOR_SATURATION_CORRECT_ONLY;


/**************************实现函数********************************************
*函数原型:  快速数据计算函数
*功　　能:  快速计算 1/Sqrt(x) sqrt(x) asin(x) safe_asin(x) atan2(x,y)
*输入参数： 要计算的值
*输出参数： 结果
*******************************************************************************/
#if 0
static float invSqrt(float x)
{
    float halfx = 0.5f * x;
    float y = x;
    long i = *(long*)&y;
    i = 0x5f3759df - (i >> 1);
    y = *(float*)&i;
    y = y * (1.5f - (halfx * y * y));
    return y;
}

static float fast_sqrt(const float x)
{
    const float xhalf = 0.5f*x;
    union // get bits for floating value
    {
        float x;
        int i;
    } u;
    u.x = x;
    u.i = 0x5f375a86  - (u.i >> 1);  // gives initial guess y0
    return x*u.x*(1.5f - xhalf*u.x*u.x);// Newton step, repeating increases accuracy 
}  

static float fast_asin(float x)
{
    int ret = 1;
    if (x >= 0) {
        ret = 1;
    }
    else
    {
        x = -x;
        ret = -1;
    }
    
    double xt = 2*x -1;
    double xt2 = xt*xt;
    double xt3 = xt2*xt;
    double denominator = ((double)0.5689111419 - (double)0.2644381021*x - (double)0.4212611542*xt2 + (double)0.1475622352*xt3);
    double numerator = ((double)2.006022274 - (double)2.343685222*x + (double)0.3316406750*xt2 + (double)0.02607135626*xt3);
    return (ret)*M_RAD2DEG*denominator/numerator;
}

static float fast_atan2( float y, float x )  
{  
    float ax = fabs(x), ay = fabs(y);//首先不分象限，求得一个锐角角度  
    float a, c, c2;  
    if( ax >= ay )  
    {  
        c = ay/(ax + (float)2e-16);  
        c2 = c*c;  
        a = (((-2.53972455324444456436073664*c2 + 8.9140001331170051762562153)*c2 + -18.6674461045867986935345175)*c2 + 57.2836237137273659983388991)*c;  
    }  
    else  
    {  
        c = ax/(ay + (float)2e-16);  
        c2 = c*c;  
        a = 90.f - (((-2.53972455324444456436073664*c2 + 8.9140001331170051762562153)*c2 + -18.6674461045867986935345175)*c2 + 57.2836237137273659983388991)*c;  
    }  
    if( x < 0 )
    {
        a = 180.f - a;  
    }
    if( y < 0 )
    {
        a = 360.f - a;
    }
    if (a >= 180)
    {
        a -= 360.f;
    }   
    return a;  
}  

static float anglePlus(float angle1, float angle2)
{
    float angle_plus = angle1 + angle2;
    if (angle_plus<-180) {
        angle_plus += 360;
    }
    else if (angle_plus>180) {
        angle_plus -= 360;
    }
    return angle_plus;
}

static float angleMinus(float angle1, float angle2)
{
    float angle_minus = angle1 - angle2;
    if (angle_minus<-180) {
        angle_minus += 360;
    }
    else if (angle_minus>180) {
        angle_minus -= 360;
    }
    return angle_minus;
}


#else
static float safe_asin(float v)
{
    if (isnan(v))
    {
        return 0.0f;
    }

    if (v >= 1.0f)
    {
        return 90.0f;
    }

    if (v <= -1.0f)
    {
        return -90.0f;
    }
    return asin(v)*M_RAD2DEG;
}
static float angleDifference(float angle1, float angle2 )
{
    float diff = angle1 - angle2;
    if (diff<-180) {
        diff += 360;
    }
    else if (diff>180) {
        diff -= 360;
    }
    return diff;
}    
#endif
/**************************实现函数********************************************
*函数原型:  void IMU_GetYawPitchRoll(float * angles)
*功　　能:  更新四元数 返回当前解算后的姿态数据
*输入参数： 将要存放姿态角的数组首地址
*输出参数： 没有
*******************************************************************************/

static void IMU_GetYawPitchRoll(quaternion_t* q4_in, ypr_t* ypr_out)
{
    static volatile float lastypr[3];
    static volatile float IMU_Pitch, IMU_Roll, IMU_Yaw;
    static float q[4];      //　Quaternion
    q[0] = q4_in->q0;
    q[1] = q4_in->q1;
    q[2] = q4_in->q2;
    q[3] = q4_in->q3;

    IMU_Roll =(atan2(2.0f * (q[0] * q[1] + q[2] * q[3]),
                  1 - 2.0f * (q[1] * q[1] + q[2] * q[2]))) * 180 / M_PI;

    // we let safe_asin() handle the singularities near 90/-90 in pitch
    IMU_Pitch = -safe_asin(2.0f * (q[0] * q[2] - q[3] * q[1]));

    IMU_Yaw = -atan2(2.0f * (q[0] * q[3] + q[1] * q[2]),
                     1 - 2.0f * (q[2] * q[2] + q[3] * q[3]))* 180 / M_PI;

    lastypr[0] = IMU_Roll;
    lastypr[1] = IMU_Pitch;
    lastypr[2] = IMU_Yaw;

    ypr_out->roll = IMU_Roll;
    ypr_out->pitch = IMU_Pitch;
    ypr_out->yaw = IMU_Yaw;
}
/**************************实现函数********************************************
*函数原型:   bool checkState(float ax, float ay, float az, float wx, float wy, float wz)  
*功　　能:   Motion check state
*输入参数：  Accelerator reading ax ay az and Gyroscopes reading wx wy wz
*输出参数：  bool state true for motion and false for static
*******************************************************************************/

bool checkState(float ax, float ay, float az, float wx, float wy, float wz)
{
    static const float kGravity = M_GRAVITY;
    static const float kAngularVelocityThreshold = 0.1f;
    static const float kAccelerationThreshold = 0.03f;
//    static const float kDeltaAngularVelocityThreshold = 0.01f;
    float acc_magnitude = sqrt(ax*ax + ay*ay + az*az);
    if (fabs(acc_magnitude - kGravity)/kGravity < kAccelerationThreshold)
    {
        return false;
    }

//  if (fabs(wx - wx_prev_) < kDeltaAngularVelocityThreshold ||
//      fabs(wy - wy_prev_) < kDeltaAngularVelocityThreshold ||
//      fabs(wz - wz_prev_) < kDeltaAngularVelocityThreshold)
//    return false;

    if ((wx < kAngularVelocityThreshold) ||
        (wy < kAngularVelocityThreshold) ||
        (wz < kAngularVelocityThreshold))
    {
    return false;
    }

  return true;
}

bool GyrState(motions_float_t* p_m9)
{
    if(fabs(p_m9->gx) > 30000 || fabs(p_m9->gy) > 30000|| fabs(p_m9->gz) > 30000 )
    {
        return false;
    }
    float mean_x=0,mean_y=0,mean_z=0;
    for(int i=0;i<LEN_FILTER;i++)
    {
        mean_x += prev_gyr[i].x/LEN_FILTER;
        mean_y += prev_gyr[i].y/LEN_FILTER;
        mean_z += prev_gyr[i].z/LEN_FILTER;
    }
    if (fabs(p_m9->gx - mean_x) > 4.01f ||
        fabs(p_m9->gy - mean_y) > 3.01f ||
        fabs(p_m9->gz - mean_z) > 3.01f)
    {
        return false;
    }

    if (fabs(p_m9->gx - default_m_fix_params.gyro.offset.x) > 10.0f ||
        fabs(p_m9->gy - default_m_fix_params.gyro.offset.y) > 10.0f ||
        fabs(p_m9->gz - default_m_fix_params.gyro.offset.z) > 10.0f)
    {
        return false;
    }

    return true;
}

bool updateGyroBiases(motions_float_t* p_m9)
{
    bool steady_state_ = GyrState(p_m9);
    bool ret  = true;
    float multi_x = 0.0f,multi_y=0.0f,multi_z=0.0f;
    if (steady_state_)
    {
        float delta_x = fabs(p_m9->gx - default_m_fix_params.gyro.offset.x);
        float delta_y = fabs(p_m9->gy - default_m_fix_params.gyro.offset.y);
        float delta_z = fabs(p_m9->gz - default_m_fix_params.gyro.offset.z);
        if            (delta_x < 2.0f)                                        multi_x = 0.1f;
        else if (delta_x >2.0f && delta_x < 5.0f )     multi_x = 0.01f;
        else if (delta_x >5.0f && delta_x < 10.0f ) multi_x = 0.001f;
        else                                                                                 multi_x = 0;

        if            (delta_y < 2.0f)                                         multi_y = 0.1f;
        else if (delta_y >2.0f && delta_y < 5.0f )     multi_y = 0.01f;
        else if (delta_y >5.0f && delta_y < 10.0f ) multi_y = 0.001f;
        else                                                                                 multi_y = 0;

        if            (delta_z < 2.0f)                                      multi_z = 0.1f;
        else if (delta_z >2.0f && delta_z < 5.0f )     multi_z = 0.01f;
        else if (delta_z >5.0f && delta_z < 10.0f ) multi_z = 0.001f;
        else                                                                                 multi_z = 0;

        if(multi_x ==0 ||multi_y ==0|| multi_z ==0)
        {
            default_m_fix_params = gDefualtConfigCalib;//p_usr_info->config.calib;
        }
        else
        {
            default_m_fix_params.gyro.offset.x += multi_x * (p_m9->gx - default_m_fix_params.gyro.offset.x);
            default_m_fix_params.gyro.offset.y += multi_y * (p_m9->gy - default_m_fix_params.gyro.offset.y);
            default_m_fix_params.gyro.offset.z += multi_z * (p_m9->gz - default_m_fix_params.gyro.offset.z); 
        }
        ret = false;
    }

    if(pre_cnt == LEN_FILTER)
    {
        prev_gyr[0].x = p_m9->gx; 
        prev_gyr[0].y = p_m9->gy; 
        prev_gyr[0].z = p_m9->gz;
        pre_cnt=0;
    }
    else
    {
        prev_gyr[pre_cnt].x = p_m9->gx; 
        prev_gyr[pre_cnt].y = p_m9->gy; 
        prev_gyr[pre_cnt].z = p_m9->gz;
        pre_cnt++;
    }
    return ret;
}


static inline float Get_Sliding_Proportional_Gain(float dt, bool restart, bool big_error)
{
    if(big_error)return TWO_KP_MIN; // eliminate error
    
    static float slidetime = 0.0f;
    float Kp = TWO_KP_MAX;    
    if(restart)
    {
        Kp = TWO_KP_MAX;
        slidetime = 0.0f;
    }else{
        if(Kp>TWO_KP_MIN) //don't waste time doing these calcs if we are already at the min
        {
            slidetime += dt;
            Kp = ( (TWO_KP_MIN - TWO_KP_MAX)/(TWO_KP_SLIDETIME) * slidetime ) + TWO_KP_MAX; //Slides from max to min linearly over the duration twoKP_SLIDETIME
            
            //Tailoring for play...let's leave the gain very high for 2 seconds of the duration
            if(slidetime < 0.2f)  Kp = TWO_KP_MAX;
            
            if(Kp<TWO_KP_MIN) Kp = TWO_KP_MIN; //cap it at the min
        }
    }
    
    
    return Kp;    
}

/**************************实现函数********************************************
*函数原型:   void MahonyAHRSupdate(motions_float_t* m9_in, quaternion_t* q4_out)
*功　　能:   Orientation calculation
*输入参数：  Accelerator, Gyroscopes and Magnetiometer reading
*输出参数：  no output
*******************************************************************************/
static void MahonyAHRSupdate(uint8_t resetEn,motions_float_t* m9_in, quaternion_t* q4_out)
{
    static volatile uint32_t lastUpdate;
    static volatile float fast_q0 = 1.0f, fast_q1 = 0.0f, fast_q2 = 0.0f, fast_q3 = 0.0f;
    static volatile int start_counter=0;
    static volatile int i_fast_start = 0;
    static volatile int delay_cnt=100;
    static volatile float last_yaw=0,fast_yaw;
    static volatile bool b_mag_update=true;

    // 全局四元数
    static volatile float q0 = 1.0f, q1 = 0.0f, q2 = 0.0f, q3 = 0.0f;

    // 2 * integral gain (Ki)
    static volatile float twoKi = 0.0f;

    // integral error terms scaled by Ki
    static volatile float integralFBx = 0.0f,  integralFBy = 0.0f, integralFBz = 0.0f;      

    // 2 * proportional gain (Kp)
//    static volatile float twoKp = TWO_KP_MAX; //MATT NEW:  changed to allow sliding gain later on and added bool
    static volatile float twoKp = 1.0f; //MATT NEW:  changed to allow sliding gain later on and added bool
    static bool b_restart_Kp = false;
    static bool b_big_error = false;
    //END MATT NEW

    volatile uint32_t now;
    volatile register float recipNorm, halfT;
    volatile float q0q0, q0q1, q0q2, q0q3, q1q1, q1q2, q1q3, q2q2, q2q3, q3q3;
    volatile register float hx, hy, bx, bz;
    volatile float halfvx, halfvy, halfvz, halfwx, halfwy, halfwz;
    volatile float halfex, halfey, halfez;
        
    volatile float halfmex,halfmey,halfmez,halfaex,halfaey,halfaez;
    volatile float qa, qb, qc;

    volatile float sampleFreq;
    volatile float ax = m9_in->ax;
    volatile float ay = m9_in->ay;
    volatile float az = m9_in->az;

    volatile float gx = m9_in->gx;
    volatile float gy = m9_in->gy;
    volatile float gz = m9_in->gz;

    volatile float mx = m9_in->mx;
    volatile float my = m9_in->my;
    volatile float mz = m9_in->mz;
    
    volatile bool  b_fast_renew =false;
    volatile float gx_fast = 0;
    volatile float gy_fast = 0;
    volatile float gz_fast = 0;
    volatile float temp_vx,temp_vy,temp_vz,temp_wx,temp_wy,temp_wz;
    volatile float fast_vx,fast_vy,fast_vz,fast_wx,fast_wy,fast_wz;
    volatile register float fast_hx, fast_hy, fast_bx, fast_bz;
    volatile float fast_ex,fast_ey,fast_ez;

    volatile float acc_len=sqrt(ax * ax + ay * ay + az * az);

    volatile float enlarge_gain=1.0;

//    bool b_stop_check =checkState(ax,ay,az,gx,gy,gz);
    volatile bool b_gyro_fast = false;
    const float gyro_threshold = 0.1745f;
    
    if(resetEn)
    {
        uint32_t lastUpdate =0;
        fast_q0 = 1.0f;
        fast_q1 = 0.0f;
        fast_q2 = 0.0f;
        fast_q3 = 0.0f;
        start_counter=0;
        i_fast_start = 0;
        delay_cnt=100;
        last_yaw=0;
        fast_yaw;
        b_mag_update=true;

        // 全局四元数
        q0 = 1.0f;
        q1 = 0.0f;
        q2 = 0.0f;
        q3 = 0.0f;

        // 2 * integral gain (Ki)
        twoKi = 0.0;

        // integral error terms scaled by Ki
        integralFBx = 0.0f;
        integralFBy = 0.0f;
        integralFBz = 0.0f;

        // 2 * proportional gain (Kp)
        //    static volatile float twoKp = TWO_KP_MAX; //MATT NEW:  changed to allow sliding gain later on and added bool
        twoKp = 1.0f; //MATT NEW:  changed to allow sliding gain later on and added bool
        b_restart_Kp = false;
        b_big_error = false;
    }
    else
    {
        if (fabs(gx) > gyro_threshold ||
        fabs(gy) > gyro_threshold ||
        fabs(gz) > gyro_threshold)
        {
            b_gyro_fast = true;
        }
        else
        {
            b_gyro_fast = false;
        }
        volatile bool b_gyro_over = false;
        if (fabs(gx) > 34.8f ||
            fabs(gy) > 34.8f ||
            fabs(gz) > 34.8f)
        {
            b_gyro_over = true;
            delay_cnt=0;
        }
        
        if(delay_cnt < 50)
        {
            delay_cnt++;
        }
        else if(delay_cnt == 50) 
        {
            delay_cnt++;
            start_counter = 0;
        }
        if(b_gyro_over)
        {
            b_gyro_over = false;
            
        }
    //        
    //    #define THRESH_FOR_TRIGGERING_GAIN_CHANGE (1000.0f * 3.14159/180.0f)
    //    if (fabs(gx) > THRESH_FOR_TRIGGERING_GAIN_CHANGE ||
    //        fabs(gy) > THRESH_FOR_TRIGGERING_GAIN_CHANGE ||
    //        fabs(gz) > THRESH_FOR_TRIGGERING_GAIN_CHANGE)
    //    {
    //        simple_cnt=0;
    //        twoKp = TWO_KP_MIN; //Set it fully low as we are now saturating and this likely means bad accel also
    //        b_restart_Kp = false; //Not yet, let the saturation end
    //        b_big_error = true;
    //    }
    //    else
    //    {
    //        b_big_error = false;
    //    }

    //    if(simple_cnt <= 30)simple_cnt++;
    //    if(simple_cnt ==30) //This is a one shot
    //    {
    //        simple_cnt++;
    //        b_restart_Kp = true; //Saturation ended
    //    }

            
        //now = micros();          //读取时间
        now = m9_in->timestamp;
        halfT =  ((float)(now - lastUpdate) / 1000000.0f);

        lastUpdate = now;           //更新时间

        if(halfT > 10)
        {
            b_restart_Kp = true;
            start_counter = 0;
            return;
        }
        sampleFreq = 1.0f / halfT;

        // Compute feedback only if accelerometer measurement valid (avoids NaN in accelerometer normalisation)
        if(!((ax == 0.0f) && (ay == 0.0f) && (az == 0.0f)))
        {
            if(acc_len > 0.9f*M_GRAVITY && acc_len < 1.1f*M_GRAVITY)
            {
                enlarge_gain = 1;
            }
            else
            {
                enlarge_gain = 0;
            }
                
            // Normalise accelerometer measurement
            recipNorm = 1/sqrt(ax * ax + ay * ay + az * az);
            ax *= recipNorm;
            ay *= recipNorm;
            az *= recipNorm;

            // Normalise magnetometer measurement
            recipNorm = 1/sqrt(mx * mx + my * my + mz * mz);
            mx *= recipNorm;
            my *= recipNorm;
            mz *= recipNorm;

            // Auxiliary variables to avoid repeated arithmetic
            q0q0 = q0 * q0;
            q0q1 = q0 * q1;
            q0q2 = q0 * q2;
            q0q3 = q0 * q3;
            q1q1 = q1 * q1;
            q1q2 = q1 * q2;
            q1q3 = q1 * q3;
            q2q2 = q2 * q2;
            q2q3 = q2 * q3;
            q3q3 = q3 * q3;

            // Reference direction of Earth's magnetic field
            hx = 2.0f * (mx * (0.5f - q2q2 - q3q3) + my * (q1q2 - q0q3) + mz * (q1q3 + q0q2));
            hy = 2.0f * (mx * (q1q2 + q0q3) + my * (0.5f - q1q1 - q3q3) + mz * (q2q3 - q0q1));
            bx = sqrt(hx * hx + hy * hy);
            bz = 2.0f * (mx * (q1q3 - q0q2) + my * (q2q3 + q0q1) + mz * (0.5f - q1q1 - q2q2));

            halfvx = q1q3 - q0q2;
            halfvy = q0q1 + q2q3;
            halfvz = q0q0 - 0.5f + q3q3;

            halfwx = bx * (0.5f - q2q2 - q3q3) + bz * (q1q3 - q0q2);
            halfwy = bx * (q1q2 - q0q3) + bz * (q0q1 + q2q3);
            halfwz = bx * (q0q2 + q1q3) + bz * (0.5f - q1q1 - q2q2);
                                            
            fast_hx = 2.0f * (mx * (0.5f - fast_q2*fast_q2 - fast_q3*fast_q3) + my * (fast_q1*fast_q2 - fast_q0*fast_q3) + mz * (fast_q1*fast_q3 + fast_q0*fast_q2));
            fast_hy = 2.0f * (mx * (fast_q1*fast_q2 + fast_q0*fast_q3) + my * (0.5f - fast_q1*fast_q1 - fast_q3*fast_q3) + mz * (fast_q2*fast_q3 - fast_q0*fast_q1));
            fast_bx = sqrt(fast_hx * fast_hx + fast_hy * fast_hy);
            fast_bz = 2.0f * (mx * (fast_q1*fast_q3 - fast_q0*fast_q2) + my * (fast_q2*fast_q3 + fast_q0*fast_q1) + mz * (0.5f - fast_q1*fast_q1 - fast_q2*fast_q2));

            fast_vx = fast_q1*fast_q3 - fast_q0*fast_q2;
            fast_vy = fast_q0*fast_q1 + fast_q2*fast_q3;
            fast_vz = fast_q0*fast_q0 - 0.5f + fast_q3*fast_q3;

            fast_wx = fast_bx * (0.5f - fast_q2*fast_q2 - fast_q3*fast_q3) + fast_bz * (fast_q1*fast_q3 - fast_q0*fast_q2);
            fast_wy = fast_bx * (fast_q1*fast_q2 - fast_q0*fast_q3) + fast_bz * (fast_q0*fast_q1 + fast_q2*fast_q3);
            fast_wz = fast_bx * (fast_q0*fast_q2 + fast_q1*fast_q3) + fast_bz * (0.5f - fast_q1*fast_q1 - fast_q2*fast_q2);


            // Estimated direction of gravity and magnetic field
            temp_vx = halfvx;
            temp_vy = halfvy;
            temp_vz = halfvz;

            temp_wx = halfwx;
            temp_wy = halfwy;
            temp_wz = halfwz;

            // Error is sum of cross product between estimated direction and measured direction of field vectors
    //        if( (mag_use == MAG_ON_ALWAYS)|| ( (mag_use == MAG_ON_FOR_SATURATION_CORRECT_ONLY) && (twoKp != TWO_KP_MIN) ) )
    //        {
    //            // Error is sum of cross product between estimated direction and measured direction of field vectors
    //            halfex = (ay * halfvz - az * halfvy) + (my * halfwz - mz * halfwy);
    //            halfey = (az * halfvx - ax * halfvz) + (mz * halfwx - mx * halfwz);
    //            halfez = (ax * halfvy - ay * halfvx) + (mx * halfwy - my * halfwx);
    //        }else{ //MAG_ON_NEVER
    //            //Original Ximmerse, only accelerometer correction
    //            halfex = ay * temp_vz - az * temp_vy;
    //            halfey = az * temp_vx - ax * temp_vz;
    //            halfez = ax * temp_vy - ay * temp_vx;         
    //        }

    //        if(b_gyro_fast)
    //        {
    //          halfex = (ay * halfvz - az * halfvy) + (my * halfwz - mz * halfwy);
    //          halfey = (az * halfvx - ax * halfvz) + (mz * halfwx - mx * halfwz);
    //          halfez = (ax * halfvy - ay * halfvx) + (mx * halfwy - my * halfwx);
    //        }
    //        else
    //        {
    //          halfex = ay * temp_vz - az * temp_vy;
    //          halfey = az * temp_vx - ax * temp_vz;
    //          halfez = ax * temp_vy - ay * temp_vx; 
    //        }
            halfex = ay * temp_vz - az * temp_vy;
            halfey = az * temp_vx - ax * temp_vz;
            halfez = ax * temp_vy - ay * temp_vx; 

            
            fast_ex = (ay * fast_vz - az * fast_vy) + (my * fast_wz - mz * fast_wy);
            fast_ey = (az * fast_vx - ax * fast_vz) + (mz * fast_wx - mx * fast_wz);
            fast_ez = (ax * fast_vy - ay * fast_vx) + (mx * fast_wy - my * fast_wx);

           // Compute and apply integral feedback if enabled
//            if(twoKi > 0.0f && !b_gyro_fast)
//            {
//                    integralFBx += twoKi * halfex * (1.0f / sampleFreq);    // integral error scaled by Ki
//                    integralFBy += twoKi * halfey * (1.0f / sampleFreq);
//                    integralFBz += twoKi * halfez * (1.0f / sampleFreq);
//                    gx += integralFBx;  // apply integral feedback
//                    gy += integralFBy;
//                    gz += integralFBz;
//            }
//            else
//            {
//                    integralFBx = 0.0f; // prevent integral windup
//                    integralFBy = 0.0f;
//                    integralFBz = 0.0f;
//            }
            
            //lead to problems if not calibrated well
    //        if(enlarge_gain ==1)
    //        {
    //            //sends time and a flag to the routine, returns proportional gain
    //            twoKp = enlarge_gain*Get_Sliding_Proportional_Gain(halfT, b_restart_Kp,b_big_error); 
    //        }
    //        else 
    //        {
    //            if (b_gyro_fast && (!b_big_error))
    //            {
    //                twoKp = 1;
    //            }
    //            else
    //            {
    //                twoKp = 0;
    //            }
    //        }
                
            
           // if(b_restart_Kp) b_restart_Kp = false; //turn off the restart of the gain curve
        }
            
        float delta_x =   gx  - halfex;
        float delta_y =   gy  - halfey;
        float delta_z =   gz  - halfez;
        // Apply proportional feedback
        gx += twoKp * halfex;
        gy += twoKp * halfey;
        gz += twoKp * halfez;

        gx_fast += 180.0f*fast_ex;
        gy_fast += 180.0f*fast_ey;
        gz_fast += 180.0f*fast_ez;
     
        // Integrate rate of change of quaternion
        gx *= (0.5f * (1.0f / sampleFreq));     // pre-multiply common factors
        gy *= (0.5f * (1.0f / sampleFreq));
        gz *= (0.5f * (1.0f / sampleFreq));

        qa = q0;
        qb = q1;
        qc = q2;

        q0 += (-qb * gx - qc * gy - q3 * gz);
        q1 += (qa * gx + qc * gz - q3 * gy);
        q2 += (qa * gy - qb * gz + q3 * gx);
        q3 += (qa * gz + qb * gy - qc * gx);

        // Normalise quaternion
        recipNorm = 1/sqrt(q0 * q0 + q1 * q1 + q2 * q2 + q3 * q3);
        q0 *= recipNorm;
        q1 *= recipNorm;
        q2 *= recipNorm;
        q3 *= recipNorm;

        gx_fast *= (0.5f * (1.0f / sampleFreq));     // pre-multiply common factors
        gy_fast *= (0.5f * (1.0f / sampleFreq));
        gz_fast *= (0.5f * (1.0f / sampleFreq));

        fast_q0 += (-fast_q1 * gx_fast - fast_q2 * gy_fast - fast_q3 * gz_fast);
        fast_q1 += (fast_q0 * gx_fast + fast_q2 * gz_fast - fast_q3 * gy_fast);
        fast_q2 += (fast_q0 * gy_fast - fast_q1 * gz_fast + fast_q3 * gx_fast);
        fast_q3 += (fast_q0 * gz_fast + fast_q1 * gy_fast - fast_q2 * gx_fast);

        recipNorm = 1/sqrt(fast_q0 * fast_q0 + fast_q1 * fast_q1 + fast_q2 * fast_q2 + fast_q3 * fast_q3);
        fast_q0 *= recipNorm;
        fast_q1 *= recipNorm;
        fast_q2 *= recipNorm;
        fast_q3 *= recipNorm;

    //    q4_out->q0 = fast_q0;
    //    q4_out->q1 = fast_q1;
    //    q4_out->q2 = fast_q2;
    //    q4_out->q3 = fast_q3;
        
        q4_out->q0 = q0;
        q4_out->q1 = q1;
        q4_out->q2 = q2;
        q4_out->q3 = q3;
        
    //    fast_yaw = -atan2(2.0f * (fast_q0 * fast_q3 + fast_q1 * fast_q2),1 - 2.0f * (fast_q2 * fast_q2 + fast_q3 * fast_q3))* 180 / M_PI;
    //    last_yaw = -atan2(2.0f * (q0 * q3 + q1 * q2),1 - 2.0f * (q2 * q2 + q3 * q3))* 180 / M_PI;
    //    //LOGPF("mag_yaw: %f  gyro_yaw  %f", fast_yaw, last_yaw);
    //    if(yaw_cnt == LEN_FILTER)
    //    {
    //        fast_yaw_list[0] = fast_yaw; 
    //        yaw_cnt=0;
    //    }
    //    else
    //    {
    //        fast_yaw_list[yaw_cnt] = fast_yaw; 
    //        yaw_cnt++;
    //    }
    //    
    //    float dif_yaw=0;
    //    for(int i =0;i<LEN_FILTER;i++)
    //    {
    //        dif_yaw+=fabs(angleDifference(fast_yaw_list[i],last_yaw))/LEN_FILTER;
    //    }
    //    //float dif_yaw = fabs(mean_yaw - last_yaw);
    //    if (dif_yaw > 10 && dif_yaw< 180 && !b_gyro_fast)
    //    {
    //        b_restart_Kp = true;
    //    }


        if( start_counter < 50 && !b_gyro_fast)
        {
    //        q0 = fast_q0;
    //        q1 = fast_q1;
    //        q2 = fast_q2;
    //        q3 = fast_q3;
            start_counter++;
        }
        else if(start_counter == 50 && !b_gyro_fast)
        {
            q0 = fast_q0;
            q1 = fast_q1;
            q2 = fast_q2;
            q3 = fast_q3;
            start_counter++;
        }
    //    m_last_fast_yaw = fast_yaw;
    }
}


static  void modify_filter_accel(uint8_t resetEn,motions_float_t* p_m9)
{
    static const float new_weight_a = 0.35;
    static volatile float m_ax = 1.0f;
    static volatile float m_ay = 1.0f;
    static volatile float m_az = 1.0f;

    if(resetEn)
    {
        m_ax = 1.0f;
        m_ay = 1.0f;
        m_az = 1.0f;
    }
    else
    {
        m_ax = m_ax * (1 - new_weight_a) + p_m9->ax * new_weight_a;
        m_ay = m_ay * (1 - new_weight_a) + p_m9->ay * new_weight_a;
        m_az = m_az * (1 - new_weight_a) + p_m9->az * new_weight_a;

        p_m9->ax = m_ax;
        p_m9->ay = m_ay;
        p_m9->az = m_az;
    }
}

static void accel_scale_fix_min_max(motions_float_t* p_m9)
{
    dof9_calib_t* p_param = &default_m_fix_params;

    p_m9->ax = (p_m9->ax - p_param->accel.offset.x) * p_param->accel.scale.x;
    p_m9->ay = (p_m9->ay - p_param->accel.offset.y) * p_param->accel.scale.y;
    p_m9->az = (p_m9->az - p_param->accel.offset.z) * p_param->accel.scale.z;
}
static void gyro_scale_fix_min_max(motions_float_t* p_m9)
{
    dof9_calib_t* p_param = &default_m_fix_params;

    p_m9->gx = (p_m9->gx - p_param->gyro.offset.x) * p_param->gyro.scale.x;
    p_m9->gy = (p_m9->gy - p_param->gyro.offset.y) * p_param->gyro.scale.y;
    p_m9->gz = (p_m9->gz - p_param->gyro.offset.z) * p_param->gyro.scale.z;
}

static inline void magnet_direction_fix(motions_float_t* p_m9)
{
    volatile float tmpx = p_m9->mx;
    volatile float tmpy = p_m9->my;
    volatile float tmpz = p_m9->mz;

    // IST8315
    p_m9->my = -tmpy;
}

static inline void gyro_scale_fix_pai_to_angle(motions_float_t* p_m9)
{
    p_m9->gx = p_m9->gx / FIX_PAI_TO_ANGLE_UNIT * M_PI / 180;
    p_m9->gy = p_m9->gy / FIX_PAI_TO_ANGLE_UNIT * M_PI / 180;
    p_m9->gz = p_m9->gz / FIX_PAI_TO_ANGLE_UNIT * M_PI / 180;
}
static inline void ypr_direction_fix(ypr_t* p_ypr)
{
//    float tmp_y = p_ypr->yaw;
    float tmp_p = p_ypr->pitch;
    float tmp_r = p_ypr->roll;
    
    p_ypr->pitch = -tmp_p;
    p_ypr->roll = -tmp_r;
}

static inline void convert_from_i9_to_float(m9_sensor_integer_t* p_i9_in, motions_float_t* p_f9_out)
{
    p_f9_out->timestamp = p_i9_in->timestamp;
    p_f9_out->ax = p_i9_in->accel.x;
    p_f9_out->ay = p_i9_in->accel.y;
    p_f9_out->az = p_i9_in->accel.z;
    p_f9_out->gx = p_i9_in->gyro.x;
    p_f9_out->gy = p_i9_in->gyro.y; 
    p_f9_out->gz = p_i9_in->gyro.z;
    p_f9_out->mx = p_i9_in->magnet.x;
    p_f9_out->my = p_i9_in->magnet.y;
    p_f9_out->mz = p_i9_in->magnet.z;
}
static inline void convert_from_f9_to_int(m9_sensor_integer_t* p_i9_out, motions_float_t* p_f9_in)
{
    p_i9_out->timestamp = p_f9_in->timestamp;
    p_i9_out->accel.x = p_f9_in->ax;
    p_i9_out->accel.y = p_f9_in->ay;
    p_i9_out->accel.z = p_f9_in->az;

    p_i9_out->gyro.x = p_f9_in->gx;
    p_i9_out->gyro.y = p_f9_in->gy;
    p_i9_out->gyro.z = p_f9_in->gz;

    p_i9_out->magnet.x = p_f9_in->mx;
    p_i9_out->magnet.y = p_f9_in->my;
    p_i9_out->magnet.z = p_f9_in->mz;

}

static void magnet_filter(m9_sensor_integer_t* p_i9, motions_float_t* p_m9)
{
    dof9_calib_t* p_param = &default_m_fix_params;

    p_m9->mx = (p_m9->mx - p_param->magnet.offset.x) * p_param->magnet.scale.x;
    p_m9->my = (p_m9->my - p_param->magnet.offset.y) * p_param->magnet.scale.y;
    p_m9->mz = (p_m9->mz - p_param->magnet.offset.z) * p_param->magnet.scale.z;
       
//    switch(gDefualtConfigCalib.magnet.mag_calib_type)
//    {
//        case magnet_source_xim_max_mean:
//        {
//            dof9_calib_t* p_param = &default_m_fix_params;

//            p_m9->mx = (p_m9->mx - p_param->magnet.offset.x) * p_param->magnet.scale.x;
//            p_m9->my = (p_m9->my - p_param->magnet.offset.y) * p_param->magnet.scale.y;
//            p_m9->mz = (p_m9->mz - p_param->magnet.offset.z) * p_param->magnet.scale.z;
//            
//            magnet_direction_fix(p_m9);
//        }
//            break;
//        case magnet_source_akm:
//        {
//            float mag_out_values[3];
//            //akm_calib_m9_to_mag(p_i9, mag_out_values);
//            p_m9->mx = mag_out_values[0];
//            p_m9->my = mag_out_values[1];
//            p_m9->mz = mag_out_values[2];
//        }
//            break;
//        case magnet_source_xim_ellipse:
//        {
//            dof3_float_t* p_mag_offset = &default_mag_offset;
//            matrix_33_t* p_mag_matrix = &default_mag_matrix;

//            float mx =  p_mag_matrix->v00*(p_m9->mx - p_mag_offset->x) + 
//                       p_mag_matrix->v01*(p_m9->my - p_mag_offset->y) +
//                       p_mag_matrix->v02*(p_m9->mz - p_mag_offset->z);
//            float my =  p_mag_matrix->v10*(p_m9->mx - p_mag_offset->x) + 
//                       p_mag_matrix->v11*(p_m9->my - p_mag_offset->y) +
//                       p_mag_matrix->v12*(p_m9->mz - p_mag_offset->z);
//            float mz =  p_mag_matrix->v20*(p_m9->mx - p_mag_offset->x) + 
//                       p_mag_matrix->v21*(p_m9->my - p_mag_offset->y) +
//                       p_mag_matrix->v22*(p_m9->mz - p_mag_offset->z);
//            p_m9->mx = mx;
//            p_m9->my = my;
//            p_m9->mz = mz;

//            magnet_direction_fix(p_m9);
//        }
//            break;
//        case magnet_source_no_magnet:
//        {
//            p_m9->mx = 0;
//            p_m9->mx = 0;
//            p_m9->mx = 0;
//        }
//            break;
//    }
}
void imu_fusion_reset(void)
{
    motions_float_t m9;
    motions_float_t* p_m9 = &m9;
    
    modify_filter_accel(1,NULL);
    MahonyAHRSupdate(0,p_m9, &g_quat);  
    
    memset(&gDefualtConfigCalib,0x00,sizeof(gDefualtConfigCalib));

    memset(&default_m_fix_params,0x00,sizeof(default_m_fix_params));
    memset(&prev_gyr[0],0x00,sizeof(prev_gyr));

    g_magnetic_yaw = 0;
    pre_cnt=0;
    memset(&g_quat,0x00,sizeof(g_quat));
    memset(&g_ypr,0x00,sizeof(g_ypr));
}
// calculate all data fusion process
m9_sensor_integer_t imu_fusion_calculation(m9_sensor_integer_t* p_i16_m9)
{
    m9_sensor_integer_t p_i9;
    motions_float_t m9;
    convert_from_i9_to_float(p_i16_m9, &m9);    
    motions_float_t* p_m9 = &m9;
    motions_float_t mRaw9 = m9;

    /*calculate raw data return*/
    accel_scale_fix_min_max(&mRaw9);
    gyro_scale_fix_min_max(&mRaw9);
    convert_from_f9_to_int(&p_i9, &mRaw9);
    
    
    /*emuler &q4 calculate*/
    accel_scale_fix_min_max(p_m9);
    modify_filter_accel(0,p_m9);
    

    bool ret = updateGyroBiases(p_m9);
    gyro_scale_fix_min_max(p_m9);
    gyro_scale_fix_pai_to_angle(p_m9);

    magnet_filter(p_i16_m9, p_m9);
    //magnet_direction_fix(p_m9);
    
    MahonyAHRSupdate(0,p_m9, &g_quat);   
    IMU_GetYawPitchRoll(&g_quat, &g_ypr);
    ypr_direction_fix(&g_ypr);
    
    return p_i9;
}

// set calibration parameters to algorithm
void imu_set_calibration_param(dof9_calib_t calib_param )
{
    gDefualtConfigCalib = calib_param;
    default_m_fix_params = gDefualtConfigCalib;
}

ypr_t imu_get_current_ypr()
{
    return g_ypr;
}

quaternion_t imu_get_current_quaternion()
{
    static quaternion_t temp;
    temp.q0 =  g_quat.q0;
    temp.q1 =  g_quat.q2;
    temp.q2 =  g_quat.q3;
    temp.q3 =  -g_quat.q1;
    return temp;
}

float imu_get_magnetic_yaw(void)
{
    return g_magnetic_yaw;
}

void get_imu_alg_version(uint8_t* p_str, uint32_t* p_len)
{
    uint32_t len= strlen(IMU_ALG_VER_STR);
    memcpy(p_str,  IMU_ALG_VER_STR, len);
    *p_len = len;
}

