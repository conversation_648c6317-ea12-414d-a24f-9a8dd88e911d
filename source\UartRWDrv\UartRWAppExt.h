/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : UartRWAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/07/31    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef UART_RW_APP_EXT_H
#define UART_RW_APP_EXT_H

/*Include files*/
#include "XimStdint.h"

/*declaration range definition*/
#ifdef  UART_RW_DRV_C
#define UART_RW_DRV_APP_EXT
#else 
#define UART_RW_DRV_APP_EXT extern
#endif

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define UART_RW_ST_SUCCESS     (0x00u)
#define UART_RW_ST_ERROR       (0x01u)
/* data type definiton  */

typedef  void   (*Uart_RX_CallbackTypeDef )(uint8_t * , uint16_t );

/* variable definition */
 
/* function declaration */
UART_RW_DRV_APP_EXT  void Uart_Init(Uart_RX_CallbackTypeDef apRxHandle);
UART_RW_DRV_APP_EXT  void UartRxFrameTimeCnt_ISR(void);
UART_RW_DRV_APP_EXT  void UartSendCmd(uint8_t aCmd, uint8_t * apData, uint8_t aLen);
UART_RW_DRV_APP_EXT  void uartStringSend(uint8_t * apData, uint8_t aLen);
UART_RW_DRV_APP_EXT  void UartParamSet(uint8_t myAddr, uint8_t remoteAddr, uint8_t addr0, uint8_t addr1);
#endif	/* end of header file */
