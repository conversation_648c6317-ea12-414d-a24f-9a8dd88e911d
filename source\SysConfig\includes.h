/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : includes.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef  __INCLUDES_H__
#define  __INCLUDES_H__

#include <stdbool.h>
#include <stddef.h>
#include "Common.h"
#include "CommonMacroDefine.h"

#include <string.h>
#include "nordic_common.h"
#include "nrf.h"
#include "nrf_delay.h"
#include "nrfx_clock.h"
#include "nrfx_gpiote.h"
#include "nrf_gpio.h"
#include "nrfx_power.h"
#include "nrfx_uart.h"
#include "nrfx_uarte.h"
#include "nrf_drv_clock.h"
#include "nrf_drv_wdt.h"
#include "nrf_drv_gpiote.h"
#include "nrf_pwr_mgmt.h"
#include "nrf_fstorage_sd.h"
#include "nrf_fstorage.h"
#include "nrf_drv_twi.h"
#include "nrf_drv_spi.h"
#include "low_power_pwm.h"
#include "nrf_drv_pwm.h"
#include "nrf_drv_saadc.h"
#include "nrf_drv_ppi.h"
#include "nrf_drv_timer.h"
#include "nrf_drv_rng.h"
#ifdef NRF52833_XXAA
#include "nrf_drv_usbd.h"
#endif
#include "nrf_drv_power.h"

#include "app_util.h"
#include "app_uart.h"
#include "app_error.h"
#include "app_timer.h"
#include "app_scheduler.h"
#include "crc16.h"

#include "app_error.h"
#include "ble.h"
#include "ble_hci.h"
#include "ble_srv_common.h"
#include "ble_advdata.h"
#include "ble_advertising.h"
#include "ble_bas.h"
#include "ble_hts.h"
#include "ble_dis.h"
#include "ble_conn_params.h"
#include "sensorsim.h"
#include "nrf_sdh.h"
#include "nrf_sdh_soc.h"
#include "nrf_sdh_ble.h"
#include "app_timer.h"
#include "nrf_dfu_ble_svci_bond_sharing.h"
#include "nrf_svci_async_function.h"
#include "nrf_svci_async_handler.h"
#include "led_softblink.h"


#include "fds.h"
#include "ble_conn_state.h"
#include "nrf_drv_clock.h"
#include "nrf_ble_gatt.h"
#include "nrf_ble_qwr.h"

#include "nrf_log.h"
#include "nrf_log_ctrl.h"
#include "nrf_log_default_backends.h"

#include "SystemCfg.h"
#include "ProductInfoAppExt.h"
#include "BleXimServiceAppExt.h"
#include "BlePeripCtrlAppExt.h"
#include "time_sync_ext.h"

#include "BspInitAppExt.h"
#include "NrfDfuCtrlAppExt.h"
#include "FlashManageAppExt.h"
#include "I2cRWAppExt.h"
#include "ICM20602CtrlAppExt.h"
#include "ICM426XXCtrlAppExt.h"
#include "TouchCtrlAppExt.h"
#include "PwmCtrlAppExt.h"
#include "LedCtrlAppExt.h"
#include "UartRWAppExt.h"
#include "DSwSampAppExt.h"
#include "PowerCtrlAppExt.h"
#include "PacketProcessAppExt.h"
#include "IntCallBackProcessAppExt.h"
#include "xim_fusion.h"
#include "UsbHidCtrlAppExt.h"
#include "SysStartUpTaskAppExt.h"
#include "EvtProcessTaskAppExt.h"
#include "RxMsgProcessTaskAppExt.h"
#include "ProductCalibAppExt.h"
#include "Ist8801CtrlAppExt.h"


#endif   /* end of header file */
