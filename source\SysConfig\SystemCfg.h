/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : SystemCfg.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
#ifndef  __SYSTEM_CFG_H__
#define  __SYSTEM_CFG_H__

#include "Common.h"

#ifdef DEBUG_TEST_MSG_EN
    #define DEBUG_TEST_PIN_EN
    #define WDT_ENABLE                  FUN_EN
    #define APP_BOOT_ENABLE             FUN_EN
    #define MCO2_CLK_OUT_ENABLE         FUN_DIS
    #define FRAME_RATE_STATISTICS       FUN_DIS
    #define FPGA_ALG_DATA_CHECK         FUN_DIS
    #define DEBUG_PRINTF(...)           NRF_LOG_INFO( __VA_ARGS__)
#else
    #define DEBUG_TEST_PIN_EN
    #define WDT_ENABLE                  FUN_EN
    #define APP_BOOT_ENABLE             FUN_EN
    #define MCO2_CLK_OUT_ENABLE         FUN_DIS
    #define FRAME_RATE_STATISTICS       FUN_DIS
    #define FPGA_ALG_DATA_CHECK         FUN_DIS
    #define DEBUG_PRINTF(...)                
#endif

#ifdef DEBUG_TEST_PIN_EN
    #define DEBUG_TEST_POINT_PORT               
    #ifdef MIRAGE2B_BOARD
        #define DEBUG_TEST_POINT_PIN                    NRF_GPIO_PIN_MAP(0,24)
    #else
        #define DEBUG_TEST_POINT_PIN                    NRF_GPIO_PIN_MAP(0,24)   //short gun
    #endif
    #define TEST(pinLevel)                              nrf_gpio_pin_write(DEBUG_TEST_POINT_PIN, pinLevel)
    #define TEST_TOGGLE()                               nrf_gpio_pin_toggle(DEBUG_TEST_POINT_PIN)
    #define TEST_PIN_HIGH()                             nrf_gpio_pin_set(DEBUG_TEST_POINT_PIN)
    #define TEST_PIN_LOW()                              nrf_gpio_pin_clear(DEBUG_TEST_POINT_PIN)
#else
    #define TEST(pinLevel)
    #define TEST_TOGGLE()
    #define TEST_PIN_HIGH()
    #define TEST_PIN_LOW()
#endif

    #define SYS_CFG_USE_TP_SWITCH                           FUN_DIS
    #define SYS_CFG_92G_DEVICE                              FUN_EN
    // #define SYS_CFG_95_1_DEVICE                             FUN_DIS
/*******************************************GPIO config******************************************************/
    /*uart config*/
    #define SYS_CFG_PIN_UART_TX                             NRF_GPIO_PIN_MAP(1, 13)
    #define SYS_CFG_PIN_UART_RX                             NRF_GPIO_PIN_MAP(0, 2)
    #define SYS_CFG_PIN_UART_CTS                            0xFF
    #define SYS_CFG_PIN_UART_RTS                            0xFF
    #define SYS_CFG_UART_HWFC                               APP_UART_FLOW_CONTROL_DISABLED
    #define SYS_CFG_UART_BAUD                               UART_BAUDRATE_BAUDRATE_Baud19200

    /*indicator led config*/
    #define SYS_CFG_PIN_CTRL_LED_GREEN                      NRF_GPIO_PIN_MAP(1, 10)
    #define SYS_CFG_PIN_CTRL_LED_RED                        0xFF
	
	#define SYS_CFG_PIN_SUBSYSTEM_PWR						NRF_GPIO_PIN_MAP(0, 31)

    //IMU sensor config
    // #define SYS_CFG_PIN_IMU_RESET                          
    // #define SYS_CFG_PIN_IMU_INT                             25
    // #define SYS_CFG_PIN_IMU_PULLDOWN_CFG                    NRF_GPIO_PIN_PULLUP
    // #define SYS_CFG_PIN_IMU_DETECT_SIGNAL                   NRF_GPIOTE_POLARITY_HITOLO
    // #define SYS_CFG_PIN_IMU_I2C_SDA                         8
    // #define SYS_CFG_PIN_IMU_I2C_SCL                         9
    // #define SYS_CFG_PIN_IMU_CS                              30
    // #define SYS_CFG_PIN_IMU_CLK                             35
    // #define SYS_CFG_PIN_IMU_MISO                            3
    // #define SYS_CFG_PIN_IMU_MOSI                            29

    //TP gpio config
    // #if SYS_CFG_USE_TP_SWITCH == FUN_EN
    //     #define SYS_CFG_PIN_TP_I2C_SDA                         4
    //     #define SYS_CFG_PIN_TP_I2C_SCL                         5
    //     #define SYS_CFG_PIN_TP_RESET                           0
    //     #define SYS_CFG_PIN_TP_INT                             26
    //     #define SYS_CFG_PIN_TP_PULLDOWN_CFG                    NRF_GPIO_PIN_PULLUP
    //     #define SYS_CFG_PIN_TP_DETECT_SIGNAL                   NRF_GPIOTE_POLARITY_HITOLO
    //     #define SYS_CFG_PIN_TP_TPQ0                            1
    //     #define SYS_CFG_PIN_TP_TPQ1                            2
    //     #define SYS_CFG_PIN_TP_AOG                             1
    //     #define SYS_CFG_PIN_TP_AHLB                            2
    // #endif
    //HALL
    // #define SYS_CFG_PIN_HALL_OUT_1                         41
    // #define SYS_CFG_PIN_HALL_OUT_2                         12

    // #define SYS_CFG_PIN_HALL_I2C_SDA                       14
    // #define SYS_CFG_PIN_HALL_I2C_SCL                       17

    //button
    #define SYS_CFG_PIN_BUTTON_POWER                       NRF_GPIO_PIN_MAP(1, 9)
    // #define SYS_CFG_PIN_BUTTON_APP                         0
    // #define SYS_CFG_PIN_BUTTON_TRIG                        0


    // #define SYS_CFG_PIN_BUTTON_INTO_HOLSTER                SYS_CFG_PIN_HALL_OUT_1  //HALL      INTO HOLSTER
    // #define SYS_CFG_PIN_BUTTON_BULLET_LOADED               SYS_CFG_PIN_HALL_OUT_2  //HALL      BULLET LOADED   

    // #define SYS_CFG_PIN_BULLET_LOADED_INT                   SYS_CFG_PIN_BUTTON_BULLET_LOADED
    // #define SYS_CFG_PIN_BULLET_LOADED_PULLDOWN_CFG          NRF_GPIO_PIN_PULLUP
    // #define SYS_CFG_PIN_BULLET_LOADED_DETECT_SIGNAL         NRF_GPIOTE_POLARITY_HITOLO


    //3.3v power out
    // #define SYS_CFG_PIN_BLOB_LED_R                         37
    // #define SYS_CFG_PIN_BLOB_LED_G                         38
    // #define SYS_CFG_PIN_BLOB_LED_B                         38

    // //MOTO 
    // #define SYS_CFG_PIN_MOTOR                              16

    //POWER control config
    // #define SYS_CFG_PIN_CTRL_POWER_ELEC_MAGNET             34
    // #define SYS_CFG_PIN_CTRL_POWER_IMU                     7
    // #define SYS_CFG_PIN_CTRL_POWER_HALL                    4
    // #define SYS_CFG_PIN_CTRL_POWER_TP                      8
    // #define SYS_CFG_PIN_CTRL_POWER_AIN                     0
    // #define SYS_CFG_PIN_CTRL_BAT_DETECT                    2
    // #define SYS_CFG_BAT_DETECT_ADC_CHANNEL                 NRF_SAADC_INPUT_AIN0
    // #define SYS_CFG_BAT_DETECT_ADC_NUM                     0
    // #define SYS_CFG_PIN_CTRL_POWER_HOLD                    23
    // #define SYS_CFG_PIN_CTRL_POWER_DCDC_PS                 0

    //charge
    // #define SYS_CFG_PIN_CTRL_POWER_CHG_EN                  27
    // #define SYS_CFG_PIN_CTRL_POWER_CHG_PG                  40
    // #define SYS_CFG_PIN_CTRL_POWER_CHG_DET                 26
    

#endif   /* end of header file */
