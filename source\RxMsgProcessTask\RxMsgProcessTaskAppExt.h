/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : RxMsgProcessTaskAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef RX_MSG_PROCESS_TASK_APP_EXT_H
#define RX_MSG_PROCESS_TASK_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "BleXimServiceAppExt.h"
#include "BlePeripCtrlAppExt.h"
#include "TouchCtrlAppExt.h"
#include "ICM20602CtrlAppExt.h"
#include "imu_types.h"
/*declaration range definition*/
#ifdef RX_MSG_PROCESS_TASK_DRV_C 
  #define RX_MSG_PROCESS_TASK_APP_EXT 
#else 
  #define RX_MSG_PROCESS_TASK_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
  /*************************ForMat type**********************/
#define MSG_FORMAT_TYPE_DATA_EVT            0x00
#define MSG_FORMAT_TYPE_CMD_ACK             0x01
  
//USB Type
#define MSG_FORMAT_TYPE_ACK                 0x0F
#define MSG_FORMAT_TYPE_CAM_DATA            0x00
#define MSG_FORMAT_TYPE_CAM_CMD             0x01
#define MSG_FORMAT_TYPE_COBRA_DATA          0x02
#define MSG_FORMAT_TYPE_COBRA_CMD           0x03
#define MSG_FORMAT_TYPE_DONGLE_CMD          0x04
#define MSG_FORMAT_TYPE_EVT_DATA            0x05
#define MSG_FORMAT_TYPE_BOOT_CMD            0x0D
#define MSG_FORMAT_TYPE_PROD_TEST_CMD       0x0E
  
//#define MSG_CODE_SYS_CFG_WRITE              0x12
//#define MSG_CODE_SYS_CFG_READ               0x13  

/*********************message Code ************************/
#define MSG_CODE_ACK                        0x7F

/*TLV  msg type*/
#define MSG_CODE_TLV_DATA_SYS_ST            0x00
#define MSG_CODE_TLV_DATA_EULEE             0x01
#define MSG_CODE_TLV_DATA_4Q                0x02
#define MSG_CODE_TLV_DATA_RAW_DATA          0x03
#define MSG_CODE_TLV_DATA_IMU_SENSOR        0x04
#define MSG_CODE_TLV_DATA_CONFIG            0x06
#define MSG_CODE_TLV_DATA_IMU_CALIB         0x07
#define MSG_CODE_TLV_DATA_MARK              0x08
#define MSG_CODE_TLV_DATA_TIME_SYN          0x09
#define MSG_CODE_TLV_DATA_RFID              0x06
#define MSG_CODE_TLV_DATA_PHYSIOLOGY        0x07
#define MSG_CODE_TLV_DATA_AIR_PRESSURE      0x09
#define MSG_CODE_TLV_DATA_RAW_DATA_MAGENET  0x0A
/*CMD message code*/
#define MSG_CODE_EVT                        0x00
#define MSG_CODE_CMD_MOTOR                  0x01
#define MSG_CODE_CMD_LED_SET                0x02
#define MSG_CODE_CMD_SYN_EXP                0x03
#define MSG_CODE_CMD_RESET                  0x04
#define MSG_CODE_CMD_AGING_TEST_IN          0x05
#define MSG_CODE_CMD_AGING_TEST_OUT         0x06
#define MSG_CODE_CMD_RF_CARRIER_CTRL        0x07
#define MSG_CODE_CMD_MARK_ID_SET            0x08
#define MSG_CODE_CMD_DEVICE_NAME_SET        0x09
#define MSG_CODE_CMD_MODE_NAME              0x0A
#define MSG_CODE_CMD_SN_SET                 0x0B
#define MSG_CODE_CMD_READ_IMU_CALIB         0x0C
#define MSG_CODE_CMD_WRITE_IMU_CALIB        0x0D
#define MSG_CODE_CMD_TRIG_MARK_CALIB        0x0E
#define MSG_CODE_CMD_TRIG_SYS_CFG           0x0F
#define MSG_CODE_CMD_SET_PIPE_TYPE          0x10
#define MSG_CODE_CMD_SET_TLV_TYPE           0x11
#define MSG_CODE_CMD_CAM_SYN_EXP_START      0x12

/*cammera and dongle CMD message code*/
#define MSG_CODE_LOAD_DATA                  0x00
#define MSG_CODE_RND_GET                    0x01
#define MSG_CODE_AUTH_CTRL                  0x02
#define MSG_CODE_READ_SYS_ST                0x03
#define MSG_CODE_SYS_RESET                  0x10
#define MSG_CODE_SYS_CFG_WRITE              0x12
#define MSG_CODE_SYS_CFG_READ               0x13  
#define MSG_CODE_SYS_IMU_CALIB_CTRL         0x19
#define MSG_CODE_READ_SN                    0x1D
#define MSG_CODE_CHANGE_REPORT_EVENT        0x1E
#define MSG_CODE_RF_CARRIER_CTRL            0x20
#define MSG_CODE_TEST_MODE_CTRL             0x29
#define MSG_CODE_PWR_STATUS_CTRL            0x2A

#define MSG_CODE_SYS_DEBUG_CTRL             0xEE
/*Fault status flg*/
#define SYS_FAULT_ST_ERR                    0x00
#define SYS_FAULT_ST_OK                     0x01

/*system enable flg*/
#define SYS_CTRL_EN                         0x01
#define SYS_CTRL_DIS                        0x00
  
/*system work model*/
#define SYS_MODE_INIT                       0x00
#define SYS_MODE_WAIT_POWERON               0x01
#define SYS_MODE_NOR                        0x02
#define SYS_MODE_STANDBY                    0x03
#define SYS_MODE_AGING_TEST                 0x04
#define SYS_MODE_CALIB                      0x05
#define SYS_MODE_PCBA_TEST                  0x06

/*Ble conncet st*/
#define SYS_BLE_CONN_ST_CONN_IDLE           0x02     //
#define SYS_BLE_CONN_ST_CONN                0x01
#define SYS_BLE_CONN_ST_DISCONN             0x00

//syn time  st
#define SYS_BLE_TIME_SYN_DISABLE            0x00     //
#define SYS_BLE_TIME_SYN_ENABLE             0x01
#define SYS_BLE_TIME_SYN_SEND               0x02

/*Usb conncet st*/
#define SYS_USB_CONN_ST_ENUM_OK             0x02     //
#define SYS_USB_CONN_ST_CONN                0x01
#define SYS_USB_CONN_ST_DISCONN             0x00

#define SYS_DEVICE_INFO_MAX_LEN             32

#define SYS_PROTOCOL_NEW_FLAG               0x80


#define TRIGGER_MAX_VAL                     255u
#define TRIGGER_MIN_VAL                     0u
/* data type definiton  */
typedef enum
{
    data_type_none              = 0x00,
    data_type_ypr               = 0x01,
    data_type_q4                = 0x02,
    data_type_acc_and_gyro      = 0x03,
    data_type_trig_grip_raw     = 0x04,
    data_type_magazine_clip_id      = 0x05,
    data_type_ypr_accel_gyro    = 0x20,
    data_type_time_sync_test    = 0xff,
}SingleDataType;
 
typedef enum
{
    gun_wait_magazine_clip_release,
    gun_wait_bullet_load,
    gun_replace_magazine_clip_success,
}ChangeMagazineClipType;

typedef enum
{
    uart_cmd_allowed_shot = 0x54,
    uart_cmd_not_allowed_shot = 0x55,
    uart_cmd_simulated_shot = 0x56,
    uart_cmd_stop_shot = 0x57,
    uart_cmd_bat_per_get = 0x59,
    uart_cmd_num_of_bullets_set = 0x62, 
    uart_cmd_led_ligting = 0x5B,

    uart_cmd_heart_beat = 0xB0,
    uart_cmd_trig_on = 0xB1,
    uart_cmd_trig_off = 0xB2,
    uart_cmd_bullet_load = 0xB3,
    uart_cmd_magazine_clip_load = 0xB5,
    uart_cmd_magazine_clip_release = 0xB6,
    uart_cmd_status_safety = 0xB7,
    uart_cmd_status_single_shot = 0xB8,
    uart_cmd_status_continuous_shot = 0xB9,

	uart_cmd_heartbeat_reply = 0xA0,
    uart_cmd_bat_per_reply = 0xA2,
}UartDataPipeCmdType;

typedef  struct
{
    uint8_t     mMsgCode        :7u;
    uint8_t     mTypeChnl       :1u;
    uint8_t     mMsgLen;
    uint8_t     mData[67];
}BleTlvMsgFormat_str;


typedef struct
{
    union
    {
        uint8_t byte;
        struct
        {
            uint8_t mMsgCode:     7u; 
            uint8_t mTypeChnl:      1u; 
        }bits;
    }cmd;
    uint8_t     mMsgLen;
    uint8_t     mData[NRF_SDH_BLE_GATT_MAX_MTU_SIZE];
}BleCmdMsgFormat_str;
typedef  struct
{
    uint8_t     mFormatType        :4u;
    uint8_t     mRfu               :4u;
    uint8_t     mMsgCode;
    uint16_t    mMsgLen;
    uint8_t     mData[64];
}UsbMsgFormat_str;

typedef struct
{
    uint8_t  mTrig;
    union
    {
        uint8_t byte;
        struct
        {
            uint8_t Hand_right_bit:     1u; 
            uint8_t Hand_left_bit:      1u; 
            uint8_t TouchPad_bit:       1u; 
            uint8_t Power_Home_bit:     1u; 
            uint8_t App_bit:            1u; 
            uint8_t Holster_check_bit:      1u; 
            uint8_t Tiger_tp_check_bit:     1u; 
            uint8_t finger_tp_check_bit:    1u;
        }bits;
    }mDSW_t;
}ButtonSt_str;

typedef struct
{
    struct
    {
        uint8_t  mTrig;
        int16_t  mRawTrig;
        union
        {
            uint8_t byte;
            struct
            {
                uint8_t Hand_right_bit:         1u; 
                uint8_t Hand_left_bit:          1u; 
                uint8_t TouchPad_bit:           1u; 
                uint8_t Power_Home_bit:         1u; 
                uint8_t Speed_Govern0_bit:      1u; 
                uint8_t Speed_Govern1_bit:      1u; 
                uint8_t Tiger_tp_check_bit:     1u; 
                uint8_t finger_tp_check_bit:    1u;
            }bits;
        }mDSW_t;
    }mButtonSt_t; 
    union
    {
        uint8_t  byte;
        struct
        {
            uint8_t    mAcc_GyroSt          :1u;
            uint8_t    mBatTempSt           :1u;
            uint8_t    mMagnSt              :1u;
            uint8_t    mTpSt                :1u;
            uint8_t    mTrigHallSt          :1u;
			uint8_t    mGripHallSt          :1u;
            uint8_t    mGunConnSt           :1u;
            uint8_t    mReserved            :1u;
        }Bits;
    }mFaultSt_uni;

    union
    {
        uint8_t  byte;
        struct
        {
            uint8_t    mAuthCtrl            :1u;
            uint8_t    mSysModel            :3u;
            uint8_t    mBleConnect          :2u;
            uint8_t    mUsbConnect          :1u;
            uint8_t    mPairStatus          :1u;
        }Bits;
    }mDeviceSt_uni;
    struct
    {
        uint8_t  DataPipe               :1u;
        uint8_t  CfgPipe                :1u;
        uint8_t  BattyPipe              :1u;
        uint8_t  CalibPipe              :1u;
        uint8_t  CalibCheckPipe         :1u;
        uint8_t  GapTriggerEn           :1u;
        uint8_t  Timer10msEn            :1u;
        uint8_t  reserved               :2u;
    }BlePipeNotifyEn_t;
    uint8_t                             mOldImuPipeType; // single protocol data type;
    union
    {
        uint8_t byte;
        struct
        {
            uint8_t   mButton_Tp_en:    1u;
            uint8_t   mEuler_en:        1u;
            uint8_t   mQ4_en:           1u;
            uint8_t   mImuRaw_en:       1u;
            uint8_t   mImuSensorData:   1u;
            uint8_t   mReserved:        3u;
        }bits;
    }PipeTlvTypeEn_t;
    uint8_t             BleMtuSize; 
    uint16_t            mBatPercent;
    uint16_t            mBatTemp;
    uint16_t            mBatVolt;
    uint8_t             mImuSendHz;
    uint8_t             mImuSendCnt;
    uint8_t             mSetMarkID;
    uint8_t             mBleHostMac[6];
    uint8_t             mLastBleHostMac[6];
    uint8_t             mPCBATestTimeoutEn;
    uint8_t             mRfCarrierEn;
    uint8_t             mIndicateLedForceCloseEn;
    touchpad_motion_t   mTpSampleData_t;
    bool                mTouchRelease;
    bool                mIndicatorForcClose;
    bool                isAllServiceEnabled;
    uint8_t             mSynTimEnable;
    struct
    { 
        ChangeMagazineClipType     mChangeMagazineClipState;
        uint8_t                 mBulletNumPrefab;
        uint8_t                 mBulletNumRemain;
        uint8_t                 mBulletNumRemainBackup;
        uint8_t                 mTrigSendCountDonw;
		uint16_t                mConnCountDown;
    }Gun_t;    
    struct
    {
        uint32_t TimerInterval_us;
        uint64_t timestamp;
        ICM20602SensorData_str   SensorData_t;
        m9_sensor_integer_t      ImuRawData_t;
        ypr_t                    ImuYpr_t;
        quaternion_t             ImuQ4_t;
    }ImutSampleData_t;
}SysStatus_str;

typedef struct
{
    uint8_t  mLen;
    uint8_t  mdata[SYS_DEVICE_INFO_MAX_LEN];
}info_str;

typedef struct
{
    uint8_t  mEnable;
    uint8_t  mMac[6u];
}MacInfo_str;

typedef struct
{
    uint16_t  mPeriod;
    uint16_t  mDuty;
    uint32_t  mTimeout;
}PwmCfg_str;
typedef struct
{
    uint8_t                  mBulletNumPrefab;          //Prefabricated bullet count
    uint8_t                  mBulletNumRemain;
    MacInfo_str              mLocalMac_t;
    MacInfo_str              mHostMac_t;
    info_str                 mProdSN;                   //PCBA SN
    info_str                 mDeviceName;
    info_str                 mDfuName;
    info_str                 mModelName;                //product SN
    uint8_t                  isSuffixEnabled;           //enable  mac suffix
    uint16_t                 mMarkID;
    uint8_t                  mDeviceTYpe[2];
    uint8_t                  mPowerType;                //power supply type: 0: dry batty, 1: li batty
    uint8_t                  mProtocolType;             // 0: single frame old type, mutilpacket type
    uint8_t                  mDefaultSingleDataType;    //single protocol default send type;
    union
    {
        uint8_t byte;
        struct
        {
            uint8_t   mOldCalib_En:     1u;
            uint8_t   mSysCfg_En:       1u;
            uint8_t   mReserved:        6u;
        }bits;
    }BleServiceEn_t;
    union
    {
        uint8_t byte;
        struct
        {
            uint8_t   mButton_Tp_en:    1u;
            uint8_t   mEuler_en:        1u;
            uint8_t   mQ4_en:           1u;
            uint8_t   mImuRaw_en:       1u;
            uint8_t   mImuSensorData:   1u;
            uint8_t   mReserved:        3u;
        }bits;
    }PipeTlvTypeEn_t;
    PwmCfg_str               mMotorPwmDefCfg_t;
    PwmCfg_str               mBlobPwmCfg_R;
    PwmCfg_str               mBlobPwmCfg_G;
    PwmCfg_str               mBlobPwmCfg_B;
    uint16_t                 mTrigSwCalibLow; //trig switch calibration threshold low
    uint16_t                 mTrigSwCalibHigh;
    uint16_t                 mTrigSwCalibRelease;
    uint16_t                 mGunLifeTime;      
}SysCfgdata_str;

typedef struct
{
    SysCfgdata_str           mDataCfgSet;
    dof9_calib_t             mImuCalibSet;
    ProducInfo_str           mProducInfo;
    uint16_t                 mTpVer;
    uint8_t                  mImuAlgVer[4u];
}SysConfig_str;

/* variable definition */
RX_MSG_PROCESS_TASK_APP_EXT   SysStatus_str          gSystemSt_t; 
RX_MSG_PROCESS_TASK_APP_EXT   SysConfig_str          gSystemCfg_t; 
/* function declaration */
RX_MSG_PROCESS_TASK_APP_EXT void RxMsgProcessTaskInit(void);
RX_MSG_PROCESS_TASK_APP_EXT void Task_RxMsgProcess_Ble(void *pData,uint16_t data_size);
RX_MSG_PROCESS_TASK_APP_EXT void Task_RxMsgProcess_Uart(void *pData,uint16_t data_size);
RX_MSG_PROCESS_TASK_APP_EXT void Task_RxMsgProcess_Usb(void *pData,uint16_t data_size);
RX_MSG_PROCESS_TASK_APP_EXT void ImuNorPipeDataSend(void);

#endif   /* end of header file */
