/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : UartRWAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/07/31    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/


#ifndef UART_RW_DRV_C
#define UART_RW_DRV_C
#endif

/********************************************************************************
**  Include files
********************************************************************************/
#include "UartRWDrv.h"

/* static variable definition */
static uint8_t TxStatus = UART_TX_ST_IDLE;
static Uart_RX_CallbackTypeDef   gRxDataHandle = NULL;

static uint8_t      gUartFrameRxBuf[UART_TX_BUF_SIZE];
static uint8_t      gUartFrameTxBuf[UART_TX_BUF_SIZE];
static uint8_t      gRxOneFrameSt = UART_RX_ONE_FRAME_DIS;
static uint8_t      gRxOneFrameTimeCnt  = 0u;
static uint8_t      gRecvDataCnt = 0u;
static uint8_t      gRecvDataLen = 0u;
static uint8_t      gUartSendHead[] = {0xA1, 0xA0, 0x95, 0x00};
/* static function declaration */

/* static function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  uart_process_handle
* Description   :  Tx and Rx  interrupt process
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */ 
static void uartRxFrameProcess(uint8_t aRxData)
{
    uint8_t CrcTemp = 0;
    if(gRxOneFrameSt == UART_RX_ONE_FRAME_DIS)
    {
        gUartFrameRxBuf[gRecvDataCnt++] = aRxData;
        if(gRecvDataCnt >= 1u)
        {
            if(UART_FRAME_HEAD == gUartFrameRxBuf[0u])
            {
                gRxOneFrameTimeCnt  = 0u;
                gRxOneFrameSt = UART_RX_ONE_FRAME_EN;
            }
            else
            {
                gRecvDataCnt = 0u;
            }
        }
    }
    else if(gRxOneFrameSt == UART_RX_ONE_FRAME_EN)
    {
        gRxOneFrameTimeCnt     = 0u;
        gUartFrameRxBuf[gRecvDataCnt++] = aRxData;
        gRecvDataLen = gUartFrameRxBuf[1];
        if(gRecvDataCnt >= (gRecvDataLen + 3u))
        {
            gRxOneFrameSt = UART_RX_ONE_FRAME_DIS;
            if(gUartFrameRxBuf[gRecvDataLen+2u] == UART_FRAME_END)
            {
                gRxDataHandle(&gUartFrameRxBuf[2u],gRecvDataLen);
            }
            else
            {
                NRF_LOG_INFO("Uart receive end fail = 0x%.2x.", gUartFrameRxBuf[gRecvDataLen+2u]);
            }
            gRecvDataCnt = 0u;
            gRecvDataLen  = 0u;
        }
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  uart_process_handle
* Description   :  Tx and Rx  interrupt process
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */ 
static void uart_process_handle(app_uart_evt_t * p_event)
{
    uint8_t Rx_Buf[1u] = {0};
    if (p_event->evt_type == APP_UART_COMMUNICATION_ERROR)
    {
        //DEBUG_PRINTF("APP_UART_COMMUNICATION_ERROR \r\n");
    }
    else if (p_event->evt_type == APP_UART_FIFO_ERROR)
    {
        DEBUG_PRINTF("APP_UART_FIFO_ERROR \r\n");
    }
    else if (p_event->evt_type == APP_UART_DATA_READY)
    {
        (void)app_uart_get(Rx_Buf);
        uartRxFrameProcess(Rx_Buf[0]);
    }
    else if(p_event->evt_type == APP_UART_TX_EMPTY)
    {
        TxStatus = UART_TX_ST_IDLE;
    }
    else
    {
        //do nothing
    }
}
/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Uart_Send
* Description   :  Uart_Send data
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */ 
void UartRxFrameTimeCnt_ISR(void)
{
    if(  (gRxOneFrameTimeCnt < UART_RX_ONE_FRAME_TIMEOUT)
       &&(gRxOneFrameSt == UART_RX_ONE_FRAME_EN)
      )
    {
        gRxOneFrameTimeCnt++;
        if(gRxOneFrameTimeCnt >= UART_RX_ONE_FRAME_TIMEOUT)
        {
            gRxOneFrameSt = UART_RX_ONE_FRAME_DIS;
            gRecvDataCnt = 0u;
            gRecvDataLen  = 0u;
        }
        else
        {
            /*do nothing*/
        }
    }
    else
    {
        /*do nothing*/
    }
}
void uartStringSend(uint8_t * apData, uint8_t aLen)
{
    uint8_t timeout = UART_TX_IDEL_TIMEOUT;
    uint8_t ret = UART_RW_ST_SUCCESS;
    while((TxStatus != UART_TX_ST_IDLE)&&(--timeout))
    {
        nrf_delay_ms(1);
    }
    if(TxStatus == UART_TX_ST_IDLE)
    {
        // app_uart_put(UART_FRAME_HEAD);
        for(uint8_t i = 0u; i< aLen; i++)
        {
            app_uart_put(apData[i]);
        }
        // app_uart_put(UART_FRAME_END);
    }
    else
    {
        ret = UART_RW_ST_ERROR;
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Uart_Send
* Description   :  Uart_Send data
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  send status
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */
static uint8_t _UartSend(uint8_t* apBuf,uint16_t aLength)  //send 15byte need 25us
{
    uint8_t timeout = UART_TX_IDEL_TIMEOUT;
    uint8_t ret = UART_RW_ST_SUCCESS;
    uint8_t i = 0u;
    if(aLength <= UART_TX_BUF_SIZE)
    {
        while((TxStatus != UART_TX_ST_IDLE)&&(--timeout))
        {
            nrf_delay_ms(1);
        }
        if(TxStatus == UART_TX_ST_IDLE)
        {
            app_uart_put(UART_FRAME_HEAD);
            for(i = 0u; i< aLength  ;i++)
            {
                app_uart_put(apBuf[i]);
            }
            app_uart_put(UART_FRAME_END);
        }
        else
        {
            ret = UART_RW_ST_ERROR;
        }
    }
    else
    {
        ret = UART_RW_ST_ERROR;
    }
    return ret;
}

void UartParamSet(uint8_t myAddr, uint8_t remoteAddr, uint8_t addr0, uint8_t addr1)
{
	gUartSendHead[0] = myAddr;
	gUartSendHead[1] = remoteAddr;
	gUartSendHead[2] = addr0;
	gUartSendHead[3] = addr1;
}

void UartSendCmd(uint8_t aCmd, uint8_t * apData, uint8_t aLen)
{
    if (aLen >0 && apData == NULL)
    {
        return;
    }
    gUartFrameTxBuf[0u] = 5+aLen;
    memcpy(&gUartFrameTxBuf[1u], gUartSendHead, sizeof(gUartSendHead));
    gUartFrameTxBuf[5u] = aCmd;
    memcpy(&gUartFrameTxBuf[6u], apData, aLen);
    _UartSend(gUartFrameTxBuf, gUartFrameTxBuf[0u]+1u);
}

/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Uart_Init
* Description   :  Uart init
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : None
* Limitations   : only used in nrf52823 
*************************************************************************************************
END_FUNCTION_HDR */
void Uart_Init(Uart_RX_CallbackTypeDef apRxHandle)
{
    uint32_t err_code;
    
    (void)app_uart_close();
    gRxDataHandle = apRxHandle;
    const app_uart_comm_params_t comm_params =
      {
          PIN_NUM_UART_RX,
          PIN_NUM_UART_TX,
          PIN_NUM_UART_RTS,
          PIN_NUM_UART_CTS,
          UART_HWFC,
          false,
          UART_BAUD
      };

    APP_UART_FIFO_INIT(&comm_params,
                         UART_RX_BUF_SIZE,
                         UART_TX_BUF_SIZE,
                         uart_process_handle,
                         APP_IRQ_PRIORITY_LOW,
                         err_code);

    APP_ERROR_CHECK(err_code);
}
/***********************************************END**********************************************/
