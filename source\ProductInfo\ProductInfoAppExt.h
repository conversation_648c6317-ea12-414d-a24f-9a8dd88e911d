/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : ProductInfoAppExt.h
************************************************************************************************
*   Project/Product : Produce Information Application
*   Title           : Produce Information Application head file
*   Author          : guotai.wang
************************************************************************************************
*   Description     : Head file 
*
************************************************************************************************
*   Limitations     : NONE
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*   Version       Date         Initials        CR#             Descriptions
*   ---------   -----------  ------------   ----------       ---------------
*     1.0        16/04/06       gtwang        N/A                 Original
*
************************************************************************************************
*   END_FILE_HDR*/

#ifndef  PRODU_EXT_APP_H
#define  PRODU_EXT_APP_H

/*Include files*/
#include "XimStdint.h"

/*declaration range definition*/
#ifdef  PRODU_INFO_C
#define PRODU_INFO_APP_EXT
#else 
#define PRODU_INFO_APP_EXT extern
#endif

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
/**************************************************************************************
*    (AAAA) ASCII: This Is The Production Name
***************************************************************************************/
#define PROD_NAME                   'A','G','U','N'

/**************************************************************************************
*    (A.A.A.A) ASCII:   This Is The HardWare Edition Number
***************************************************************************************/
#define PROD_HARD_VER               '0','0','1','0'

/**************************************************************************************
*    (A.A.A.A) ASCII:   This Is The SoftWare Edition Number
***************************************************************************************/
#define PROD_SOFT_VER               '0','0','1','8'

/**************************************************************************************
*    ASCII:   This Is The Sequence Number Of The Product
***************************************************************************************/
#define PROD_PCBA_SN                '2','0','2','0','1','2','0','1',\
                                    '8','9',\
                                    '0','1','\0','\0','\0','\0','\0','\0','\0','\0',\
                                    '\0','\0','\0','\0','\0','\0','\0','\0','\0','\0',\
                                    '\0','\0'
#define PROD_BT_MAC                 0xee,0xee,0xee,0xee,0xee,0xee

#define PROD_MANUFACTURE            'X','i','m','m','e','r','s','e','\0','\0',  \
                                    '\0','\0','\0','\0','\0','\0','\0','\0','\0','\0'

#define PROD_NAME_LEN               (4u)             /*The Lenght Of The Production Name */
#define PROD_HARD_LEN               (4u)         /*The Lenght Of The Production HardWare Number*/
#define PROD_SOFT_LEN               (4u)         /*The Lenght Of The Production SoftWare Number*/
#define PROD_SN_LEN                 (32u)               /*The Lenght Of The Product Information*/
#define PROD_MAC_LEN                (6u)               /*The Lenght Of The Product Information*/
#define PROD_MANU_LEN               (20u) 
/* data type definiton  */
typedef struct
{    
    uint8_t  mSaveFlg[2u];
    uint8_t  ProdName[PROD_NAME_LEN];
    uint8_t  ProdHardVer[PROD_HARD_LEN];
    uint8_t  ProdPCBASN[PROD_SN_LEN];
    uint8_t  ProdBtMac[PROD_MAC_LEN];
    uint8_t  ProdManuFacture[PROD_MANU_LEN];
}ProducInfo_str;

typedef struct
{
    uint8_t  ProdSoftVer[PROD_SOFT_LEN];
    uint8_t  CompileTime[8u];
    uint8_t  CompileDate[11u];
}SoftVer_str;

/* variable definition */
#ifdef FACTORY_BURN_FLASH
PRODU_INFO_APP_EXT const ProducInfo_str ProducInfo;
#endif
PRODU_INFO_APP_EXT const SoftVer_str SoftInfo_t;
/* function declaration */


#endif	/* end of header file */
