/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : PacketProcessDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/06    guotai        N/A            Original                
*   1.1         2018/03/27    guotai                       UVC application
************************************************************************************************
* END_FILE_HDR*/

#ifndef PACKET_PROCESS_DRV_H
#define PACKET_PROCESS_DRV_H

/*Include files*/
#include "PacketProcessAppExt.h"

/* support module */
#include "includes.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
#define FRAME_SEND_CNT_MAX          3u

#define FRAM_SEND_DELAY_TIME        1u // 1ms
/*Packet*/
#define USB_DATA_PACKET_HEAD            0x00
#define USB_DATA_PACKET_END             0x20

#define BLE_DATA_PACKET_HEAD            0x80
#define BLE_DATA_PACKET_END             0xA0

#define RX_PACKET_START             0x00
#define RX_PACKET_WAIT              0x01
#define RX_PACKET_FINISH            0x02


/*Message*/
#define USB_RX_BUF_SIZE             256

#define BLE_RX_BUF_SIZE             256


/* data type definiton  */

#define TIME_DELAY(time)            nrf_delay_ms((time))


#endif /* End of header file */
