// @@ -1,82 +1,91 @@
/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Main.C
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
#ifndef TIME_SYNC_EXT_H
#define TIME_SYNC_EXT_H

/**/
#include <stdbool.h>
#include <stdint.h>
#include <string.h>
#include "app_error.h"
#include "nrf.h"
#include "nrf_error.h"
#include "nrf_soc.h"
#include "nrf_sdm.h"

#include "app_scheduler.h"
#ifdef TIME_SYNC_C
#define TIME_SYNC_EXT   
 
#else
#define TIME_SYNC_EXT  extern  
#endif

typedef struct {
    uint32_t radio_start_count;
    uint32_t time0_0_count;
    uint32_t time0_1_count;
    uint32_t radio_extend_count;
    uint32_t radio_extend_fail_count;
    uint32_t recv_count;
}count_t;

 typedef struct
{
    uint64_t stamp_local;
    uint64_t stamp_remote;
    double accuracyUs;
}sync_snapshot_t;
/*typedef define*/
typedef struct
{
    uint8_t          rf_chn;          /** RF Channel [0-80] */
    uint8_t          rf_addr[5];      /** 5-byte RF address */
    uint8_t          ppi_chns[6];     /** PPI channels */
    NRF_TIMER_Type * high_freq_timer[3]; /** 16 MHz timer (e.g. NRF_TIMER2) */
    int64_t          stamp_offset;         //offset of miniseconds
    double           stamp_ppm;       //
    NRF_EGU_Type   * egu;
    IRQn_Type        egu_irq_type;
} ts_params_t;



/*function declar*/
//TIME_SYNC_EXT uint32_t ts_init(const ts_params_t * p_params);
//TIME_SYNC_EXT uint32_t ts_enable(void);
//TIME_SYNC_EXT uint64_t get_stamp64_sync_finshed(void);
TIME_SYNC_EXT count_t SynTestRecord_t;
TIME_SYNC_EXT void time_sync_init(uint8_t *rfAddr,uint8_t RfChanle);
TIME_SYNC_EXT void time_sync_start(uint8_t* host_mac);
TIME_SYNC_EXT uint64_t get_stamp64_sync_finshed(void);
TIME_SYNC_EXT void ts_disable(void);

#endif
