/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : EvtProcessTaskAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/06/20    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef EVT_PROCESS_TASK_APP_EXT_H
#define EVT_PROCESS_TASK_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "BleXimServiceAppExt.h"
#include "BlePeripCtrlAppExt.h"
/*declaration range definition*/
#ifdef EVT_PROCESS_TASK_DRV_C 
  #define EVT_PROCESS_TASK_APP_EXT 
#else 
  #define EVT_PROCESS_TASK_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define BULLET_LOADER_SEND_MAX_TIME         0x5u
#define MAGAZINE_CLIP_STATE_SEND_MAX_TIME       0x32u

#define ELECTRO_MAGNET_CTRL_MAX_TIME        500u
/* data type definiton  */


/* variable definition */

/* function declaration */
EVT_PROCESS_TASK_APP_EXT  void Task_EvtProcess_Timer(void *pData,uint16_t data_size);
EVT_PROCESS_TASK_APP_EXT  void Task_EvtProcess_Ble(void *pData,uint16_t data_size);
EVT_PROCESS_TASK_APP_EXT  void Task_EvtProcess_Usb(void *pData,uint16_t data_size);
EVT_PROCESS_TASK_APP_EXT  void EvtProcessTaskInit(void);
EVT_PROCESS_TASK_APP_EXT  void ButtonDataSample(void);
EVT_PROCESS_TASK_APP_EXT  void WorkModelChange(uint8_t model);
EVT_PROCESS_TASK_APP_EXT  void Imu_10msTimer_Enable(uint8_t enable);
#endif   /* end of header file */
