/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : ICM426XXCtrlDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2017/10/24    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef ICM20602_CTRL_DRV_H
#define ICM20602_CTRL_DRV_H

/*Include files*/
#include "ICM426XXCtrlAppExt.h"

/* support module */
#include "includes.h"
#include "Icm426xxDefs.h"
#include "Icm426xxExtFunc.h"
#include "Icm426xxDriver_HL.h"
#include "Icm426xxTransport.h"
#include "Icm426xxVersion.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
#define IS_LOW_NOISE_MODE    1
#define HZUSE_CLK_IN           0

#if defined( MIRAGE2B_BOARD)
    #define IMU_SPI_EN  0
#else
    #define IMU_SPI_EN  1     //
#endif
#if  IMU_SPI_EN
#define IMU_CTRL_SPI_NUM_CFG               (spi_Instance2)
#define IMU_CTRL
#else
#define ICM426XX_I2C_HANDLE               (I2C_MASTER_TWI0_INST)
#endif

#define ICM426XX_TXRX_LEN_MAX             64
#define IICM426XX_WAIT_DRDY_TIMEOUT       20u    /*20 * 100US = 2ms*/
#define ICM426XX_DELAY_US(time)          nrf_delay_us(time)
#define ICM426XX_DELAY_MS(time)          nrf_delay_ms(time)
/*
SAD0  address(7bit)     address(8bit)
VSS   68H               D0H
VDD   69H               D2H
*/ 
#define ICM426XX_I2C_ADDRESS                 0xD0


/*regiser value*/


#define ICM426XX_PIN_RESET_HIGH()
#define ICM426XX_PIN_RESET_LOW()

#define ICM426XX_PIN_SPI_CS_HIGH()    nrf_gpio_pin_set(SYS_CFG_PIN_IMU_CS)
#define ICM426XX_PIN_SPI_CS_LOW()     nrf_gpio_pin_clear(SYS_CFG_PIN_IMU_CS);

#endif /* End of header file */
