/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : DSwSampHWExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef D_SW_SAMP_HW_EXT_H
#define D_SW_SAMP_HW_EXT_H

#include "DSwSampCfgExt.h"
#include "includes.h"
#include "nrf_gpio.h"
/*Include files*/
/************************************************************************
* This file include defination related with MCU                         *
*************************************************************************/



/* Port and pin config for every switch */  
#if (D_SW_SAMP_OBJ_NUM >= 1)
#define D_SW_SAMP_SW_PORT_0              0             
#define D_SW_SAMP_SW_PIN_0               SYS_CFG_PIN_BUTTON_POWER 
#endif                                   
#if (D_SW_SAMP_OBJ_NUM >= 2)             
#define D_SW_SAMP_SW_PORT_1              0 
#define D_SW_SAMP_SW_PIN_1               //SYS_CFG_PIN_CTRL_POWER_CHG_PG  
#endif                                   
                                         
#if (D_SW_SAMP_OBJ_NUM >= 3)             
#define D_SW_SAMP_SW_PORT_2              0        
#define D_SW_SAMP_SW_PIN_2               //SYS_CFG_PIN_CTRL_POWER_CHG_PG   
#endif                                   
                                         
#if (D_SW_SAMP_OBJ_NUM >= 4)             
#define D_SW_SAMP_SW_PORT_3              0       
#define D_SW_SAMP_SW_PIN_3               //SYS_CFG_PIN_BUTTON_INTO_HOLSTER
#endif                                   

#if (D_SW_SAMP_OBJ_NUM >= 5)             
#define D_SW_SAMP_SW_PORT_4              0          
#define D_SW_SAMP_SW_PIN_4               //SYS_CFG_PIN_UART_RX
#endif

#if (D_SW_SAMP_OBJ_NUM >= 6)
#define D_SW_SAMP_SW_PORT_5              0
#define D_SW_SAMP_SW_PIN_5               //SYS_CFG_PIN_BUTTON_INTO_HOLSTER
#endif                                    
                                          
#if (D_SW_SAMP_OBJ_NUM >= 7)              
#define D_SW_SAMP_SW_PORT_6              0
#define D_SW_SAMP_SW_PIN_6               //SYS_CFG_PIN_BUTTON_INTO_HOLSTER
#endif                                  
                                        
#if (D_SW_SAMP_OBJ_NUM >= 8)            
#define D_SW_SAMP_SW_PORT_7             0
    #if SYS_CFG_USE_TP_SWITCH == FUN_EN
        #define D_SW_SAMP_SW_PIN_7              SYS_CFG_PIN_TP_TPQ0
    #else
        #define D_SW_SAMP_SW_PIN_7              SYS_CFG_PIN_BUTTON_INTO_HOLSTER
    #endif
#endif

#if (D_SW_SAMP_OBJ_NUM >= 9)
#define D_SW_SAMP_SW_PORT_8             0
    #if SYS_CFG_USE_TP_SWITCH == FUN_EN
        #define D_SW_SAMP_SW_PIN_8              SYS_CFG_PIN_TP_TPQ1
    #else
        #define D_SW_SAMP_SW_PIN_8              SYS_CFG_PIN_BUTTON_INTO_HOLSTER
    #endif
#endif

#if (D_SW_SAMP_OBJ_NUM >= 10)
#define D_SW_SAMP_SW_PORT_9            0
#define D_SW_SAMP_SW_PIN_9             SYS_CFG_PIN_BUTTON_INTO_HOLSTER
#endif

#define TRIG_SAMPLE_JUDG_LOW        gSystemCfg_t.mDataCfgSet.mTrigSwCalibLow
#define TRIG_SAMPLE_JUDG_HIGH       gSystemCfg_t.mDataCfgSet.mTrigSwCalibHigh

#define D_SW_SAMP_ADDR_OFFSET           1U            /* Offset of PTxD and PTxDD                  */
                                                      /* &PTxDD == (&PTxD + D_SW_SAMP_ADDR_OFFSET) */

#define D_SW_SAMP_DDR_IN                SET_DDR_OUT    /* Input-direction define  */
#define D_SW_SAMP_DDR_OUT               SET_DDR_IN   /* Output-direction define */

#define D_SW_SAMP_EXTER_FUN_FLA         0xFF  // adc read


#define D_SW_EXTER_SAMP_FUN(reg,value)  value = (GetAdcSample(reg) *256u) / 13654u
#define D_SW_GPIO_SAMP_FUN(PORT,PIN)    nrf_gpio_pin_read(PIN)

#endif	/* end of header file */
