/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : SysStartUpTaskDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef SYS_STARTUP_TASK_DRV_H
#define SYS_STARTUP_TASK_DRV_H

/*Include files*/
#include "SysStartUpTaskAppExt.h"

/* support module */
#include "includes.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
#define STK_SIZE_TASK_CMD_RPOCESS        (512)//2KB
#define STK_SIZE_TASK_EVT_PROCESS        (512)//2KB 

/*msg Q size*/
#define TASK_MSG_Q_SIZE_CMD_PROCESS     20
#define TASK_MSG_Q_SIZE_EVT             20

/**/



/* data type definiton  */
typedef enum
{
    PRI_TASK_EVT_PROCESS = 2u,
    PRI_TASK_CMD_PROCESS,    
    PRI_TASK_SYSTEM0 = TASK_STARTUP_PRI,        // 0x05, RFU,max
}TASK_PRI;



#endif /* End of header file */
