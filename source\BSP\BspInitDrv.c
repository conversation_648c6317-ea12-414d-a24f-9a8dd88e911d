/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : BspInitDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef BSP_INIT_DRV_C
#define BSP_INIT_DRV_C
#endif

/* include files */
#include "BspInitDrv.h"

/* static variable definition */
const uint32_t UICR_NFCPINS __attribute__((at(0x1000120C))) __attribute__((used)) = 0xFFFFFFFE;
#ifndef  MIRAGE2B_BOARD
nrf_drv_spi_t spi_Instance2 = NRF_DRV_SPI_INSTANCE(2);
#endif
nrf_drv_wdt_channel_id m_channel_id;

/* static function declaration */

/* static function definition */
#ifdef DEBUG_TEST_MSG_EN
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  CompileDataTimeDisp
* Description   :  Printf compile data and time
*
* Inputs        : @param  None:
*                          
*                 @global val  None:
*                  
* Outputs       : @param   None:
*                          
*                 @global val  None:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
static void CompileDataTimeDisp(void)
{
    DEBUG_PRINTF("Compile date and time is %s",&SoftInfo_t.CompileTime[0]);
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  log_resetreason
* Description   :  printf system reset reason.
*
* Inputs        : @param  None:
*                          
*                 @global val  None:
*                  
*
* Outputs       : @param   None:
*                          
*                 @global val  None:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
static void log_resetreason(void)
{
    /* Reset reason */
    uint32_t rr = nrf_power_resetreas_get();
    NRF_LOG_INFO("Reset reasons:");
    if (0 == rr)
    {
        NRF_LOG_INFO("- NONE");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_RESETPIN_MASK))
    {
        NRF_LOG_INFO("- RESETPIN");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_DOG_MASK     ))
    {
        NRF_LOG_INFO("- DOG");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_SREQ_MASK    ))
    {
        NRF_LOG_INFO("- SREQ");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_LOCKUP_MASK  ))
    {
        NRF_LOG_INFO("- LOCKUP");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_OFF_MASK     ))
    {
        NRF_LOG_INFO("- OFF");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_LPCOMP_MASK  ))
    {
        NRF_LOG_INFO("- LPCOMP");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_DIF_MASK     ))
    {
        NRF_LOG_INFO("- DIF");
    }
    if (0 != (rr & NRF_POWER_RESETREAS_NFC_MASK     ))
    {
        NRF_LOG_INFO("- NFC");
    }
#ifdef NRF52833_XXAA
    if (0 != (rr & NRF_POWER_RESETREAS_VBUS_MASK     ))
    {
        NRF_LOG_INFO("- NFC");
    }
#endif
    gResetreason = rr;
    nrf_power_resetreas_clear(gResetreason);
    NRF_LOG_FLUSH();
}
#endif
static void SystemClock_Config(void)
{
    ret_code_t err_code = nrf_drv_clock_init();
    APP_ERROR_CHECK(err_code);
}
void init_rfclk(void)
{
    NRF_RNG->TASKS_START = 1;
    
    // Start 16 MHz crystal oscillator
    NRF_CLOCK->EVENTS_HFCLKSTARTED  = 0;
    NRF_CLOCK->TASKS_HFCLKSTART     = 1;

    // Wait for the external oscillator to start up
    while (NRF_CLOCK->EVENTS_HFCLKSTARTED == 0)
    {
        // Do nothing.
    }  
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  GPIO_InitConfig
* Description   :  config Gpio
*
* Inputs        : @param  None:
*                          
*                 @global val  None:
*                  
* Outputs       : @param   None:
*                          
*                 @global val  None:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void GPIO_InitConfig(void)
{
   //disenable USB wakeup
    nrf_gpio_pin_sense_t sense =  NRF_GPIO_PIN_NOSENSE;
    nrf_gpio_cfg_sense_set(SYS_CFG_PIN_BUTTON_POWER, sense);
    // #if SYS_CFG_USE_TP == true
    //     nrf_gpio_cfg_sense_set(SYS_CFG_PIN_BUTTON_TP, sense);
    // #endif
    // nrf_gpio_cfg_sense_set(SYS_CFG_PIN_CTRL_POWER_CHG_PG, sense);
    // nrf_gpio_cfg_sense_set(SYS_CFG_PIN_HALL_OUT_1, sense);
    nrf_gpio_cfg_sense_set(SYS_CFG_PIN_UART_RX, sense);
    //LED
    nrf_gpio_pin_clear(SYS_CFG_PIN_CTRL_LED_GREEN);  //close
    nrf_gpio_pin_clear(SYS_CFG_PIN_CTRL_LED_RED);  //close
    nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_LED_GREEN);
    nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_LED_RED);
	
	nrf_gpio_pin_set(SYS_CFG_PIN_SUBSYSTEM_PWR);
	nrf_gpio_cfg_output(SYS_CFG_PIN_SUBSYSTEM_PWR);
    
    /*IMU */
    // nrf_gpio_pin_clear(SYS_CFG_PIN_IMU_CS);  //low
    // nrf_gpio_cfg_output(SYS_CFG_PIN_IMU_CS);
    
    
    /*TP */
// #if SYS_CFG_USE_TP == true
//     #ifndef  MIRAGE2B_BOARD
//         nrf_gpio_pin_clear(SYS_CFG_PIN_TP_AOG);  //low
//         nrf_gpio_pin_clear(SYS_CFG_PIN_TP_AHLB);  //low
//         nrf_gpio_cfg_output(SYS_CFG_PIN_TP_AOG);
//         nrf_gpio_cfg_output(SYS_CFG_PIN_TP_AHLB);
//     #else
//         nrf_gpio_pin_set(SYS_CFG_PIN_TP_RESET);  //high
//         nrf_gpio_cfg_output(SYS_CFG_PIN_TP_RESET);
//     #endif
// #endif
    
    /*button*/
// #ifndef  MIRAGE2B_BOARD
     nrf_gpio_cfg_input(SYS_CFG_PIN_BUTTON_POWER,NRF_GPIO_PIN_NOPULL);
//     nrf_gpio_cfg_input(SYS_CFG_PIN_UART_RX,NRF_GPIO_PIN_PULLUP);
//     #if SYS_CFG_USE_TP == true
//         nrf_gpio_cfg_input(SYS_CFG_PIN_BUTTON_TP,NRF_GPIO_PIN_NOPULL);
//     #endif
//     nrf_gpio_cfg_input(SYS_CFG_PIN_HALL_OUT_1, NRF_GPIO_PIN_NOPULL);
// #else
//     nrf_gpio_cfg_input(SYS_CFG_PIN_BUTTON_POWER,NRF_GPIO_PIN_PULLUP);
//     #if SYS_CFG_USE_TP == true
//         nrf_gpio_cfg_input(SYS_CFG_PIN_BUTTON_TP,NRF_GPIO_PIN_PULLUP);
//     #endif
//     nrf_gpio_cfg_input(SYS_CFG_PIN_HALL_OUT_1, NRF_GPIO_PIN_NOPULL);
// #endif
    
    /*Blob led*/
    /*nrf_gpio_pin_set(SYS_CFG_PIN_BLOB_LED_R);  //close
    nrf_gpio_pin_set(SYS_CFG_PIN_BLOB_LED_G);  //close
    nrf_gpio_pin_set(SYS_CFG_PIN_BLOB_LED_B);  //close
    nrf_gpio_cfg_output(SYS_CFG_PIN_BLOB_LED_R);
    nrf_gpio_cfg_output(SYS_CFG_PIN_BLOB_LED_G);
    nrf_gpio_cfg_output(SYS_CFG_PIN_BLOB_LED_B); */


    //motor
//     nrf_gpio_pin_clear(SYS_CFG_PIN_MOTOR);  //close
//     nrf_gpio_cfg_output(SYS_CFG_PIN_MOTOR);
    
//     //power control
//     nrf_gpio_pin_set(SYS_CFG_PIN_CTRL_POWER_IMU);  //close
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_IMU);
//     nrf_gpio_pin_clear(SYS_CFG_PIN_CTRL_POWER_ELEC_MAGNET);  //close
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_ELEC_MAGNET);
// #if SYS_CFG_USE_TP == true
//     nrf_gpio_pin_set(SYS_CFG_PIN_CTRL_POWER_TP);  //close
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_TP);
// #endif
//     nrf_gpio_pin_set(SYS_CFG_PIN_CTRL_POWER_HALL);  //close
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_HALL);

// #ifndef MIRAGE2B_BOARD
//     nrf_gpio_pin_set(SYS_CFG_PIN_CTRL_POWER_HOLD);  //open
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_HOLD);
// #else
//     nrf_gpio_pin_clear(SYS_CFG_PIN_CTRL_POWER_LDO_CTRL);  //enable
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_LDO_CTRL);
//     nrf_gpio_pin_clear(SYS_CFG_PIN_CTRL_POWER_LDO_EN);  //enable
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_LDO_EN);
// #endif

//     nrf_gpio_pin_set(SYS_CFG_PIN_CTRL_POWER_CHG_EN);  //open
//     nrf_gpio_cfg_output(SYS_CFG_PIN_CTRL_POWER_CHG_EN);
//     nrf_gpio_cfg_input(SYS_CFG_PIN_CTRL_POWER_CHG_PG,NRF_GPIO_PIN_PULLUP);
//     nrf_gpio_cfg_input(SYS_CFG_PIN_CTRL_POWER_CHG_DET,NRF_GPIO_PIN_PULLUP);
     
#ifdef DEBUG_TEST_PIN_EN
    nrf_gpio_pin_clear(DEBUG_TEST_POINT_PIN);
    nrf_gpio_cfg_output(DEBUG_TEST_POINT_PIN);
#endif   
}
/**
 * @brief WDT events handler.
 */
void wdt_event_handler(void)
{
    DEBUG_PRINTF("WDT timeout event system reset\n");
    NVIC_SystemReset();
}
void WdtConfigure(void)
{
#if WDT_ENABLE
    uint32_t err_code = NRF_SUCCESS;
    //Configure WDT.
    nrf_drv_wdt_config_t config = NRF_DRV_WDT_DEAFULT_CONFIG;
    err_code = nrf_drv_wdt_init(&config, wdt_event_handler);
    APP_ERROR_CHECK(err_code);
    err_code = nrf_drv_wdt_channel_alloc(&m_channel_id);
    APP_ERROR_CHECK(err_code);
    nrf_drv_wdt_enable();
#endif
}

void FeedWdt(void *pData,uint16_t data_size)
{
#if WDT_ENABLE
    nrf_drv_wdt_channel_feed(m_channel_id);
#endif
}

void saadc_init(void)
{
//    ret_code_t err_code;
//    nrfx_saadc_uninit();
//    nrf_saadc_channel_config_t channel_config =
//    NRF_DRV_SAADC_DEFAULT_CHANNEL_CONFIG_SE(SYS_CFG_BAT_DETECT_ADC_CHANNEL);

//    err_code = nrf_drv_saadc_init(NULL, NULL);
//    APP_ERROR_CHECK(err_code);

//    err_code = nrf_drv_saadc_channel_init(SYS_CFG_BAT_DETECT_ADC_NUM, &channel_config);
//    APP_ERROR_CHECK(err_code);
}
uint32_t GetAdcSample(uint8_t channel) //mv
{
    static nrf_saadc_value_t AdValue = 0u;
    
    if(NRF_SUCCESS != nrfx_saadc_sample_convert(channel,&AdValue))
    {
        NRF_LOG_INFO("%d channel adc sample err\n",channel);
    }
    else
    {
       //do nothing
    }
    if(AdValue < 0)
    {
        AdValue = 0;
    }
    return AdValue;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Spi1_Init
* Description   :  spi init
*
* Inputs        : @param  None: 
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */
#ifdef  SHORT_AIR_GUN_BOARD 
void Spi2_Init(void)
{
    nrf_drv_spi_config_t spi_config = NRF_DRV_SPI_DEFAULT_CONFIG;
    
//    spi_config.bit_order = NRF_DRV_SPI_BIT_ORDER_MSB_FIRST;
//    spi_config.frequency = NRF_DRV_SPI_FREQ_4M;
//    spi_config.irq_priority = SPI_DEFAULT_CONFIG_IRQ_PRIORITY;
//    spi_config.mode = NRF_DRV_SPI_MODE_0;
//    spi_config.orc = 0xFF;
//    
//    spi_config.miso_pin = SYS_CFG_PIN_IMU_MISO;
//    spi_config.mosi_pin = SYS_CFG_PIN_IMU_MOSI;
//    spi_config.sck_pin = SYS_CFG_PIN_IMU_CLK;
//    spi_config.ss_pin =  NRF_DRV_SPI_PIN_NOT_USED;
//  
//    APP_ERROR_CHECK(nrf_drv_spi_init(&spi_Instance2, &spi_config, NULL,NULL));
}

void Spi2_unint(void)
{
    nrf_drv_spi_uninit(&spi_Instance2);
}
#endif


static void xim_bsp_nrf_drv_gpiote_evt_handler(nrf_drv_gpiote_pin_t pin, nrf_gpiote_polarity_t action)
{
//    if(pin == SYS_CFG_PIN_IMU_INT)
//    {
//        enable_Imu_inter(0);//disable int  wait next conn event trigger
//        ImuSampISRCallBack();
//    }
//    #if SYS_CFG_USE_TP_SWITCH == FUN_EN
//        else if(pin == SYS_CFG_PIN_TP_INT)
//        {
//            TpSampISRCallBack();
//        }
//    #endif
}

static void Imu_gpio_interrupt_init(void)
{
//    uint32_t err_code;

//    nrf_drv_gpiote_in_config_t config;

//    config.pull = SYS_CFG_PIN_IMU_PULLDOWN_CFG;
//    config.hi_accuracy = true;
//    config.sense = SYS_CFG_PIN_IMU_DETECT_SIGNAL;
//    config.is_watcher = false;
//    err_code = nrf_drv_gpiote_in_init(SYS_CFG_PIN_IMU_INT, &config, xim_bsp_nrf_drv_gpiote_evt_handler);

//    if(err_code == NRF_ERROR_INVALID_STATE)
//    {
//        DEBUG_PRINTF("!ERROR! on GPIOTE_REINIT");
//    }
//    else
//    {
//        APP_ERROR_CHECK(err_code);
//    }

}

static void BulletLoaded_gpio_interrupt_init(void)
{
//    uint32_t err_code;

//    nrf_drv_gpiote_in_config_t config;

//    config.pull = SYS_CFG_PIN_BULLET_LOADED_PULLDOWN_CFG;
//    config.hi_accuracy = true;
//    config.sense = SYS_CFG_PIN_BULLET_LOADED_DETECT_SIGNAL;
//    config.is_watcher = false;
//    err_code = nrf_drv_gpiote_in_init(SYS_CFG_PIN_BULLET_LOADED_INT, &config, xim_bsp_nrf_drv_gpiote_evt_handler);

//    if(err_code == NRF_ERROR_INVALID_STATE)
//    {
//        DEBUG_PRINTF("!ERROR! on GPIOTE_REINIT");
//    }
//    else
//    {
//        APP_ERROR_CHECK(err_code);
//    }
}

static void Tp_gpio_interrupt_init(void)
{
#ifndef  SHORT_AIR_GUN_BOARD 
    #if SYS_CFG_USE_TP_SWITCH == FUN_EN
        uint32_t err_code;

        nrf_drv_gpiote_in_config_t config;

        config.pull = SYS_CFG_PIN_TP_PULLDOWN_CFG;
        config.hi_accuracy = true;
        config.sense = SYS_CFG_PIN_TP_DETECT_SIGNAL;
        config.is_watcher = false;
        err_code = nrf_drv_gpiote_in_init(SYS_CFG_PIN_TP_INT, &config, xim_bsp_nrf_drv_gpiote_evt_handler);

        if(err_code == NRF_ERROR_INVALID_STATE)
        {
            DEBUG_PRINTF("!ERROR! on GPIOTE_REINIT");
        }
        else
        {
            APP_ERROR_CHECK(err_code);
        }
    #endif
#endif
}
#ifdef NRF52833_XXAA
static void power_usb_event_handler(nrf_drv_power_usb_evt_t event)
{
    Task_Msg_Q_str   evt_out;
    switch (event)
    {
    case NRF_DRV_POWER_USB_EVT_DETECTED:
        NRF_LOG_INFO("USB power detected");
        if (!nrf_drv_usbd_is_enabled())
        {
            nrf_drv_usbd_enable();
        }
        evt_out.mMsgLength = 0;
        evt_out.mType = MSG_TASK_Q_USB_POWER_DETECTED;
        if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Usb))
        {
            NRF_LOG_INFO("discover timeout call back put status err \n");
        }
        break;
    case NRF_DRV_POWER_USB_EVT_REMOVED:
        NRF_LOG_INFO("USB power removed");
        if (nrf_drv_usbd_is_started())
        {
            nrf_drv_usbd_stop();
        }
        if (nrf_drv_usbd_is_enabled())
        {
            nrf_drv_usbd_disable();
        }
        evt_out.mMsgLength = 0;
        evt_out.mType = MSG_TASK_Q_USB_POWER_REMOVED;
        if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Usb))
        {
            NRF_LOG_INFO("discover timeout call back put status err \n");
        }
        break;
    case NRF_DRV_POWER_USB_EVT_READY:
        NRF_LOG_INFO("USB ready");
        evt_out.mMsgLength = 0;
        evt_out.mType = MSG_TASK_Q_USB_POWER_READY;
        if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Usb))
        {
            NRF_LOG_INFO("discover timeout call back put status err \n");
        }
        break;
    default:
        ASSERT(false);
    }
}
#endif
void enable_Imu_inter(bool enable)
{
//    if(enable)
//    {
//        nrf_drv_gpiote_in_event_enable(SYS_CFG_PIN_IMU_INT, true);
//    }
//    else
//    {   
//        nrfx_gpiote_in_event_disable(SYS_CFG_PIN_IMU_INT);
//    }
}

void enable_BulletLoaded_inter(bool enable)
{
//    if(enable)
//    {
//        nrf_drv_gpiote_in_event_enable(SYS_CFG_PIN_BULLET_LOADED_INT, true);
//    }
//    else
//    {   
//        nrfx_gpiote_in_event_disable(SYS_CFG_PIN_BULLET_LOADED_INT);
//    }
}

void enable_Tp_inter(bool enable)
{
#ifndef  SHORT_AIR_GUN_BOARD 
    #if SYS_CFG_USE_TP_SWITCH == FUN_EN
        if(enable)
        {
            nrf_drv_gpiote_in_event_enable(SYS_CFG_PIN_TP_INT, true);
        }
        else
        {   
            nrfx_gpiote_in_event_disable(SYS_CFG_PIN_TP_INT);
        }
    #endif
#endif
}
void usbPowerDetectionInit(void)
{
#ifdef NRF52833_XXAA
    ret_code_t ret = 0;
    static const nrf_drv_power_usbevt_config_t config =
    {
        .handler = power_usb_event_handler
    };
    ret = nrf_drv_power_usbevt_init(&config);
    APP_ERROR_CHECK(ret);
#endif
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : 
* Description   : 
*
* Inputs        : @param   xxxx:
*                          
*                 @global val  xxxx:
*                  
*
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void  BspInit(void)
{
    ret_code_t err_code;
#ifdef DEBUG_TEST_MSG_EN
     err_code = NRF_LOG_INIT(NULL);
    APP_ERROR_CHECK(err_code);
    NRF_LOG_DEFAULT_BACKENDS_INIT();
#endif
    SystemClock_Config();
#if(APP_BOOT_ENABLE)
    err_code = ble_dfu_async_svci_init();
    APP_ERROR_CHECK(err_code);
#endif
    err_code = app_timer_init();
    APP_ERROR_CHECK(err_code);
    GPIO_InitConfig();
    err_code = nrf_pwr_mgmt_init();
    APP_ERROR_CHECK(err_code);
#ifdef DEBUG_TEST_MSG_EN
    CompileDataTimeDisp(); 
    log_resetreason();
#endif
    saadc_init();
    err_code = nrf_drv_ppi_init();
    APP_ERROR_CHECK(err_code);
    WdtConfigure();
    FeedWdt(0,0);
    nrf_drv_gpiote_init();
    Tp_gpio_interrupt_init();
    Imu_gpio_interrupt_init();
    BulletLoaded_gpio_interrupt_init();
    DEBUG_PRINTF("MCU init finish\r\n");
#ifdef DEBUG_TEST_MSG_EN
    NRF_LOG_PROCESS();
#endif
}
/***********************************************END**********************************************/
