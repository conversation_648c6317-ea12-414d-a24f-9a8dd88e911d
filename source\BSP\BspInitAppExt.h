/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : BspInitAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/07/30    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef BSP_INIT_APP_EXT_H
#define BSP_INIT_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "nrf_drv_spi.h"
/*declaration range definition*/
#ifdef BSP_INIT_DRV_C 
  #define BSP_INIT_APP_EXT 
#else 
  #define BSP_INIT_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
/* data type definiton  */
#ifdef  SHORT_AIR_GUN_BOARD 
BSP_INIT_APP_EXT    nrf_drv_spi_t spi_Instance2;
#endif
/* macro definition */
/* variable definition */
BSP_INIT_APP_EXT  uint32_t   gResetreason;
  
/* function declaration */
BSP_INIT_APP_EXT  void  BspInit(void);
#ifdef  SHORT_AIR_GUN_BOARD 
BSP_INIT_APP_EXT  void Spi2_Init(void);
BSP_INIT_APP_EXT  void Spi2_unint(void);
#endif
BSP_INIT_APP_EXT  void init_rfclk(void);
BSP_INIT_APP_EXT  void WdtConfigure(void);
BSP_INIT_APP_EXT  void FeedWdt(void *pData,uint16_t data_size);
BSP_INIT_APP_EXT  void enable_Imu_inter(bool enable);
BSP_INIT_APP_EXT  void enable_BulletLoaded_inter(bool enable);
BSP_INIT_APP_EXT  void enable_Tp_inter(bool enable);
BSP_INIT_APP_EXT  uint32_t GetAdcSample(uint8_t channel);
BSP_INIT_APP_EXT  void usbPowerDetectionInit(void);
#endif   /* end of header file */
