/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : ProductCalibAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef PRODUCT_CALIB_TEST_APP_EXT_H
#define PRODUCT_CALIB_TEST_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "ICM20602CtrlAppExt.h"
#include "imu_types.h"
#include "RxMsgProcessTaskAppExt.h"
/*declaration range definition*/
#ifdef PRODUCT_CALIB_TEST_DRV_C 
  #define PRODUCT_CALIB_TEST_APP_EXT 
#else 
  #define PRODUCT_CALIB_TEST_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */

/* data type definiton  */


/* variable definition */


/* function declaration */
PRODUCT_CALIB_TEST_APP_EXT  void radio_disable(void);
PRODUCT_CALIB_TEST_APP_EXT  void radio_tx_carrier(uint8_t txpower, uint8_t mode, uint8_t channel);
PRODUCT_CALIB_TEST_APP_EXT  void BleCalibCheckPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen);
PRODUCT_CALIB_TEST_APP_EXT  void BleCalibPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen);
PRODUCT_CALIB_TEST_APP_EXT  void CalibPipeDataUpdata(ICM20602SensorData_str *apImuSensorData,void * apMagnetSensorData);
PRODUCT_CALIB_TEST_APP_EXT  void CalibCheckPipeDataSample(ICM20602SensorData_str *apImuSensorData,void * apMagnetSensorData); 
#endif   /* end of header file */
