/*  BEGIN_FILE_HDR
**************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Common.h
************************************************************************************************
*   Project/Product : Common Driver 
*   Title           : This is Common Head File
*   Author          : guotai
************************************************************************************************
*   Description     :This is common module for different driver
*
************************************************************************************************
*   Limitations     : NONE
*
************************************************************************************************

************************************************************************************************
*   Revision History:
* 
*   Version       Date         Initials     CR#           Descriptions
*   ---------   -----------  ------------  ----------  ---------------
*   1.0         18/07/24     guotai         N/A         Original
************************************************************************************************
*   END_FILE_HDR */

#ifndef COMMON_FUN_H
#define COMMON_FUN_H

/*Include files*/
#include "XimStdint.h"

/*declaration range definition*/
#ifdef COMMON_GLB_C 
#define COMMON_FUN_APP_EXT 
#else 
#define COMMON_FUN_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* data type definition */
/* macro  definition */
#define COMMON_OVER_TIME                    (1U)
#define COMMON_NOT_OVER_TIME                (0U)

#define COMMON_COMB_MSB_MOD                 (1U)
#define COMMON_COMB_LSB_MOD                 (0U)

#define COMMON_MEM_CMP_DATA_EQUAL           (0xF1u)
#define COMMON_MEM_CMP_DATA_DIFFER          (0xF0u)

/*macro function definition*/
#define GET_MAX_VAL(a,b)  (((a) < (b)) ? (b) : (a))
#define GET_MIN_VAL(a,b)  (((a) > (b)) ? (b) : (a))

/* Function Declaration  */

/*Bit Set or cleared*/
COMMON_FUN_APP_EXT uint8_t  SET_BIT_8U(uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint8_t CLR_BIT_8U(uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint16_t SET_BIT_16U(uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint16_t CLR_BIT_16U(uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint32_t SET_BIT_32U(uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint32_t CLR_BIT_32U(uint8_t aBitx_i);

/*Displacement*/
COMMON_FUN_APP_EXT uint32_t LeftShiftClr(uint32_t aShiftVar_i,uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint32_t LeftShiftSet(uint32_t aShiftVar_i,uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint32_t RightShiftClr(uint32_t aShiftVar_i,uint8_t aBitx_i);
COMMON_FUN_APP_EXT uint32_t RightShiftSet(uint32_t aShiftVar_i,uint8_t aBitx_i);

/*Byte combination  and Byte Split*/
COMMON_FUN_APP_EXT uint8_t  ByteSplit(uint32_t aSplitVar_i,uint8_t aGetByteNum_i);
COMMON_FUN_APP_EXT uint16_t ByteCombination16U(const uint8_t * acpCombAddr_i,uint8_t aComModel_i);
COMMON_FUN_APP_EXT uint32_t ByteCombination32U(const uint8_t * acpCombAddr_i,uint8_t aComModel_i);

/*Memory operations*/
COMMON_FUN_APP_EXT uint8_t MemDataCmp(const uint8_t *const apDataFrom_i, const uint8_t *const apDataTo_i, uint16_t aLenth_i);
COMMON_FUN_APP_EXT void MemDataCopy( uint8_t *const apcDestAddr_o,const uint8_t *const apcSourceAddr_i,uint16_t aLenght_i);
COMMON_FUN_APP_EXT void MemDataSet( uint8_t *const apcDataAddr_o,uint8_t aSetValue_i,uint16_t aLenght_i);

#endif  /* end of header file */ 
