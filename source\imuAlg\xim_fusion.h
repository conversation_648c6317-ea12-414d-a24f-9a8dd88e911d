#ifndef __XIM_IMU_H__
#define __XIM_IMU_H__

#include <stdbool.h>
#include <stdint.h>
#include <math.h>
#include <string.h>
#include "stdlib.h"

#include "imu_types.h"

#define  IMU_ALG_VER   "9303"

// set calibration parameters to algorithm
void imu_fusion_reset(void);
void imu_set_calibration_param(dof9_calib_t calib_param);

// update 9DoF Raw data to fusion calculation
m9_sensor_integer_t imu_fusion_calculation(m9_sensor_integer_t* p_i16_m9);

// get euler angle(yaw pitch roll) result from fusion
ypr_t imu_get_current_ypr(void);

// get quaternion result from fusion
quaternion_t imu_get_current_quaternion(void);

// get magnetic yaw angle from fusion
float imu_get_magnetic_yaw(void);

// get aglorithm version
void get_imu_alg_version(uint8_t* p_str, uint32_t* p_len);


#endif
