 //@@ -1,107 +1,99 @@
/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Main.C
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
#ifndef TIME_SYNC_H
#define TIME_SYNC_H


/*include files*/
#include "includes.h"
#include "time_sync_ext.h"


/*macor define*/
#define TS_SOC_OBSERVER_PRIO 1

/*typedef define*/
#define RFPROTO_DEFAULT_CHANNEL_TAB                             {0,2,24,52,80,82}
#define RFPROTO_MAX_CHANNEL_TAB_SIZE                            6              // [# channels]

typedef struct
{
    uint64_t stamp;
}time_sync_payload_t;

static volatile enum
{
    RADIO_STATE_IDLE, /* Default state */
    RADIO_STATE_RX,   /* Waiting for packets */
    RADIO_STATE_TX    /* Trying to transmit packet */
} m_radio_state = RADIO_STATE_IDLE;

typedef struct
{
    double sum_x;
    double sum_x_sq;
    double sum_y;
    double sum_xy;
    int32_t counter;
}linear_fit_buffer_t;

typedef struct
{
    double x;
    double y;
}linear_fit_element_t;

typedef struct 
{   //y = a1*x + a0
    double a0;
    double a1;
}linear_fit_result_t;











/**@brief Data handler type. */
//typedef void (*ts_evt_handler_t)(uint32_t time);


//uint32_t ts_disable(void);
//uint32_t ts_tx_start(uint32_t sync_freq_hz);
//uint32_t ts_tx_stop(void);
//uint32_t ts_reset(void);

//uint32_t trigger_swi_at_remote_stamp(pf_time_sync_swi_handle callback, uint64_t stamp_remote);
//uint32_t trigger_swi_at_remote_stamp_with_ppi(pf_time_sync_swi_handle callback, uint64_t stamp_remote, uint32_t task_address);

typedef void (*pf_time_sync_swi_handle)(void);
/*function declar*/
 void update_radio_parameters(void);
 void ts_on_sys_evt(uint32_t sys_evt, void * p_context);

#endif


