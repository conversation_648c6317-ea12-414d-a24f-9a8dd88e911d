/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : LedCtrlDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/06/29    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef PWM_CTRL_DRV_C
#define PWM_CTRL_DRV_C
#endif

/* include files */
#include "PwmCtrlDrv.h"

/* static variable definition */
static low_power_pwm_t               low_power_pwm[SLOW_PWM_CTRL_NUM];
static nrf_drv_pwm_t                *gpPwmInstance[FAST_PWM_CTRL_NUM];
static nrf_pwm_values_individual_t   gPwmSeqValues[FAST_PWM_CTRL_NUM];
static nrf_pwm_sequence_t            gPwmSeq[FAST_PWM_CTRL_NUM]; 
static nrf_drv_pwm_config_t          gFastPwmConfig[FAST_PWM_CTRL_NUM];

static bool fastPwmIntEn = false;

#if(FAST_PWM_CTRL_NUM >0)
static nrf_drv_pwm_t m_pwm0 = NRF_DRV_PWM_INSTANCE(0);
#endif
#if(FAST_PWM_CTRL_NUM >1)
static nrf_drv_pwm_t m_pwm1 = NRF_DRV_PWM_INSTANCE(1);
#endif
#if(FAST_PWM_CTRL_NUM >2)
static nrf_drv_pwm_t m_pwm2 = NRF_DRV_PWM_INSTANCE(2);
#endif

#if(SLOW_PWM_CTRL_NUM >0)
APP_TIMER_DEF(lpp_timer_0);
#endif
#if(SLOW_PWM_CTRL_NUM >1)
APP_TIMER_DEF(lpp_timer_1);
#endif
#if(SLOW_PWM_CTRL_NUM >2)
APP_TIMER_DEF(lpp_timer_2);
#endif
/* static function declaration */


/* static function definition */

/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : SlowPwmTimInit
* Description   : 
* Inputs        : @param   :      
*                 @param   aPeriod:    max 32.768KHz    //base freq =   16384 Hz   APP_TIMER_CONFIG_RTC_FREQUENCY
*
* Outputs       : @param   xxxx:
*                 @global val  xxxx:
*                 @retval :
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
bool SlowPwmTimInit(uint8_t pwmIndex,uint8_t aPeriod,uint8_t aDuty,bool active_high,uint32_t bit_mask,uint8_t portId)
{
    uint32_t err_code;
    low_power_pwm_config_t low_power_pwm_config;
    
    if(pwmIndex < SLOW_PWM_CTRL_NUM)
    {
        low_power_pwm_config.active_high    = active_high;
        low_power_pwm_config.period         = aPeriod;
        low_power_pwm_config.bit_mask       = bit_mask;
#if(SLOW_PWM_CTRL_NUM >0)
        if(pwmIndex == 0u)
        {
            low_power_pwm_config.p_timer_id     = &lpp_timer_0;
        }
#if(SLOW_PWM_CTRL_NUM >1)
        else if(pwmIndex == 1u)
        {
            low_power_pwm_config.p_timer_id     = &lpp_timer_1;
        }
#endif
#if(SLOW_PWM_CTRL_NUM >2)
        else if(pwmIndex == 2u)
        {
            low_power_pwm_config.p_timer_id     = &lpp_timer_2;
        }
#endif
#endif
        if(portId ==0)
        {
            low_power_pwm_config.p_port         = NRF_GPIO;
        }
        else
        {
#if defined(NRF52840_XXAA) || defined(NRF52833_XXAA)
            low_power_pwm_config.p_port         = NRF_P1;
#else
         return false;
#endif
        }
        err_code = low_power_pwm_init((&low_power_pwm[pwmIndex]), &low_power_pwm_config, NULL);
        APP_ERROR_CHECK(err_code);
        err_code = low_power_pwm_duty_set(&low_power_pwm[pwmIndex], aDuty);
        APP_ERROR_CHECK(err_code);
    }
    else
    {
        return false;
    }
    return true;
}

/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : SlowPwmStop
* Description   : disable pwm
*
* Inputs        : @param   aLedSelect:  
*                 @param   
* Outputs       : @param   xxxx:
*                 @global val  xxxx:
*
*                 @retval : register value
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
bool SlowPwmStop(uint8_t pwmIndex)
{ 
    if(pwmIndex < SLOW_PWM_CTRL_NUM)
    {
        if(low_power_pwm_stop((&low_power_pwm[pwmIndex])) != NRF_SUCCESS)
        {
            return false;
        }
    }
    else
    {
        return false;
    }
    return true;
}
/**
 * @brief   Function for starting a low-power PWM instance.
 *
 * @param[in] p_pwm_instance            Pointer to the instance to be started.
 * @param[in] pins_bit_mask             Bit mask of pins to be started.
 *
 * @return Values returned by @ref app_timer_start.
 */
bool SlowPwmStart(uint8_t pwmIndex,uint32_t pins_bit_mask)
{
    if(pwmIndex < SLOW_PWM_CTRL_NUM)
    {
        if(low_power_pwm_start((&low_power_pwm[pwmIndex]),pins_bit_mask) != NRF_SUCCESS)
        {
            return false;
        }
    }
    else
    {
        return false;
    }
    return true;
}
/**
 * @brief   Function for setting a new high pulse width for a given instance.
 *
 * This function can be called from the timer handler.
 *
 * @param[in] p_pwm_instance            Pointer to the instance to be changed.
 * @param[in] duty_cycle                New high pulse width. 0 means that the pin is always off. 255 means that it is always on.
 *
 * @retval NRF_SUCCESS                  If the function completed successfully.
 * @retval NRF_ERROR_INVALID_PARAM      If the function returned an error because of invalid parameters.
 */
bool SlowPwmSetDuty(uint8_t pwmIndex,uint8_t duty_cycle)
{
    if(pwmIndex < SLOW_PWM_CTRL_NUM)
    {
        if(low_power_pwm_duty_set((&low_power_pwm[pwmIndex]),duty_cycle) != NRF_SUCCESS)
        {
            return false;
        }
    }
    else
    {
        return false;
    }
    return true;
}



/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name : FastPwmTimInit
* Description   : 
*
* Inputs        : @param   :      
*                 @param   aPeriod:    max 100KHz    //base freq =   1MHz
*
* Outputs       : @param   xxxx:
*                          
*                 @global val  xxxx:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void FastPwmTimInit(void)
{
#if(FAST_PWM_CTRL_NUM >0)
    gpPwmInstance[0] = &m_pwm0;
#endif
#if(FAST_PWM_CTRL_NUM >1)
    gpPwmInstance[1] = &m_pwm1;
#endif
#if(FAST_PWM_CTRL_NUM >2)
    gpPwmInstance[2] = &m_pwm2;
#endif
    for(uint8_t i =0 ; i < FAST_PWM_CTRL_NUM;i++)
    {
        gPwmSeqValues[i].channel_0 = 0x0000;
        gPwmSeqValues[i].channel_1 = 0x0000;
        gPwmSeqValues[i].channel_2 = 0x0000;
        gPwmSeqValues[i].channel_3 = 0x0000;
        gPwmSeq[i].values.p_individual = &gPwmSeqValues[i];
        gPwmSeq[i].length = NRF_PWM_VALUES_LENGTH(gPwmSeqValues[i]);
        gPwmSeq[i].repeats = 0u;
        gPwmSeq[i].end_delay = 0u;
        
        gFastPwmConfig[i].output_pins[0u] = NRF_DRV_PWM_PIN_NOT_USED;
        gFastPwmConfig[i].output_pins[1u] = NRF_DRV_PWM_PIN_NOT_USED;
        gFastPwmConfig[i].output_pins[2u] = NRF_DRV_PWM_PIN_NOT_USED;
        gFastPwmConfig[i].output_pins[3u] = NRF_DRV_PWM_PIN_NOT_USED;
        gFastPwmConfig[i].irq_priority = APP_IRQ_PRIORITY_LOWEST;
        gFastPwmConfig[i].base_clock   = NRF_PWM_CLK_1MHz;
        gFastPwmConfig[i].count_mode   = NRF_PWM_MODE_UP;
        gFastPwmConfig[i].top_value    = 1000;
        gFastPwmConfig[i].load_mode    = NRF_PWM_LOAD_INDIVIDUAL;
        gFastPwmConfig[i].step_mode    = NRF_PWM_STEP_AUTO;
    }
    fastPwmIntEn = true;
}

void FastPwmStop(uint8_t pwmIndex,bool active_high)
{ 
    if((pwmIndex < FAST_PWM_CTRL_NUM)&&(fastPwmIntEn))
    {
        nrf_drv_pwm_stop(gpPwmInstance[pwmIndex],true);
        nrf_drv_pwm_uninit(gpPwmInstance[pwmIndex]);
        
        if(gFastPwmConfig[pwmIndex].output_pins[0] != NRF_DRV_PWM_PIN_NOT_USED)
        {
            if(!active_high)
            {
                nrf_gpio_pin_clear(gFastPwmConfig[pwmIndex].output_pins[0] &(~NRFX_PWM_PIN_INVERTED) );
            }
            else
            {
                nrf_gpio_pin_set(gFastPwmConfig[pwmIndex].output_pins[0] &(~NRFX_PWM_PIN_INVERTED) );
            }
        }
        if(gFastPwmConfig[pwmIndex].output_pins[1]  != NRF_DRV_PWM_PIN_NOT_USED)
        {
            if(!active_high)
            {
                nrf_gpio_pin_clear(gFastPwmConfig[pwmIndex].output_pins[1] &(~NRFX_PWM_PIN_INVERTED));
            }
            else
            {
                nrf_gpio_pin_set(gFastPwmConfig[pwmIndex].output_pins[1] &(~NRFX_PWM_PIN_INVERTED) );
            }
        }
        if(gFastPwmConfig[pwmIndex].output_pins[2]  != NRF_DRV_PWM_PIN_NOT_USED)
        {
            if(!active_high)
            {
                nrf_gpio_pin_clear(gFastPwmConfig[pwmIndex].output_pins[2] &(~NRFX_PWM_PIN_INVERTED) );
            }
            else
            {
                nrf_gpio_pin_set(gFastPwmConfig[pwmIndex].output_pins[2] &(~NRFX_PWM_PIN_INVERTED));
            }
        }
        if(gFastPwmConfig[pwmIndex].output_pins[3] != NRF_DRV_PWM_PIN_NOT_USED)
        {
            if(!active_high)
            {
                nrf_gpio_pin_clear(gFastPwmConfig[pwmIndex].output_pins[3] &(~NRFX_PWM_PIN_INVERTED) );
            }
            else
            {
                nrf_gpio_pin_set(gFastPwmConfig[pwmIndex].output_pins[3] &(~NRFX_PWM_PIN_INVERTED) );
            }
        }
    }
    else
    {
        //do nothing
    }
}

bool FastPwmTimStart(uint8_t pwmIndex,uint16_t aPeriod,FastPwmPinCfg_str out_pins)
{
    uint32_t err_code; 
    if((pwmIndex < FAST_PWM_CTRL_NUM)&&(fastPwmIntEn))
    {
        nrf_drv_pwm_uninit(gpPwmInstance[pwmIndex]);
        gFastPwmConfig[pwmIndex].top_value    = aPeriod;
        
        gPwmSeqValues[pwmIndex].channel_0 = out_pins.duty[0];
        gPwmSeqValues[pwmIndex].channel_1 = out_pins.duty[1];
        gPwmSeqValues[pwmIndex].channel_2 = out_pins.duty[2];
        gPwmSeqValues[pwmIndex].channel_3 = out_pins.duty[3];
        
        gFastPwmConfig[pwmIndex].output_pins[0u] = out_pins.out_pins[0] | out_pins.active_high[0];
        gFastPwmConfig[pwmIndex].output_pins[1u] = out_pins.out_pins[1] | out_pins.active_high[1];
        gFastPwmConfig[pwmIndex].output_pins[2u] = out_pins.out_pins[2] | out_pins.active_high[2];
        gFastPwmConfig[pwmIndex].output_pins[3u] = out_pins.out_pins[3] | out_pins.active_high[3];
        
        err_code = nrf_drv_pwm_init(gpPwmInstance[pwmIndex], &gFastPwmConfig[pwmIndex], NULL);
        APP_ERROR_CHECK(err_code);
        (void)nrf_drv_pwm_simple_playback(gpPwmInstance[pwmIndex], &gPwmSeq[pwmIndex], 1,NRF_DRV_PWM_FLAG_LOOP);
    }
    else
    {
        return false;
    }
    return true;
}



/***********************************************END**********************************************/
