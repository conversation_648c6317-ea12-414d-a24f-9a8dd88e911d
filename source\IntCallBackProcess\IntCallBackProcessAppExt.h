/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : IntCallBackProcessAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2018/05/28    guotai        N/A            Original                
*
************************************************************************************************
* END_FILE_HDR*/

#ifndef CALL_BACK_PROCESS_APP_EXT_H
#define CALL_BACK_PROCESS_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "BlePeripCtrlAppExt.h"


/*declaration range definition*/
#ifdef CALL_BACK_PROCESS_DRV_C 
  #define CALL_BACK_PROCESS_APP_EXT 
#else 
  #define CALL_BACK_PROCESS_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
/*msg buffer size*/
#define MSG_RX_BUF_SIZE_CMD                 256u
#define MSG_RX_BUF_SIZE_IMU                 20u

/*buffer number*/
#define MSG_RX_BUF_NUM_CMD                  3u
#define MSG_RX_BUF_NUM_IMU                  6u
  
/* data type definiton  */
typedef struct
{
    uint64_t mTimeStamp;
    uint8_t  mImuRxMsgDataBuf[MSG_RX_BUF_SIZE_IMU];
}Imu_SensorSampleData_str;

/* variable definition */

/* function declaration */
CALL_BACK_PROCESS_APP_EXT  void CallBackProcessInit(void);
CALL_BACK_PROCESS_APP_EXT  void BleConnectEvtCallBack(ble_evt_t const * p_ble_evt);
CALL_BACK_PROCESS_APP_EXT  void BleAdvEvtCallBack(ble_adv_evt_t p_ble_adv_evt);
CALL_BACK_PROCESS_APP_EXT  void BleParamsUpdataEvtCallBack(const ble_gap_conn_params_t  * p_params ,uint16_t conn_handle);  
CALL_BACK_PROCESS_APP_EXT  void Timer_10ms_timeout_handler(void * p_context);
CALL_BACK_PROCESS_APP_EXT  void Timer_1s_timeout_handler(void * p_context);
CALL_BACK_PROCESS_APP_EXT  void UsbRecvCmdISRCallBack(uint8_t *apData,uint16_t aPacketRxLen);
CALL_BACK_PROCESS_APP_EXT  void BtXimServiceEvtCallBack(ble_xim_evt_t *p_evt);
CALL_BACK_PROCESS_APP_EXT  void ImuSampISRCallBack(void);
CALL_BACK_PROCESS_APP_EXT  void TpSampISRCallBack(void);
CALL_BACK_PROCESS_APP_EXT  void UartRxDataCallBack(uint8_t *apRxData,uint16_t len);
CALL_BACK_PROCESS_APP_EXT  void BleSensorDataPost(void);
#endif   /* end of header file */
