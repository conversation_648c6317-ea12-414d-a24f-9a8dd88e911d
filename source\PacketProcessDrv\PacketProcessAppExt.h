/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : PacketProcessAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/06    guotai        N/A            Original                
*   1.1         2018/03/27    guotai                       UVC application
************************************************************************************************
* END_FILE_HDR*/

#ifndef MSG_PROCESS_APP_EXT_H
#define MSG_PROCESS_APP_EXT_H

/*Include files*/
#include "XimStdint.h"

/*declaration range definition*/
#ifdef PACKET_PROCESS_DRV_C 
  #define PACKET_PROCESS_APP_EXT 
#else 
  #define PACKET_PROCESS_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define SEND_INTERFACE_USB              0x00
#define SEND_INTERFACE_BLE              0x01    /*system config  uuid*/

#define PACKET_OPERATE_SUCCESS          0x00
#define PACKET_OPERATE_FAIL             0x01
  
#define PACKET_SUM_CALC                 0x00
#define PACKET_SUM_CHECK                0x01

#define USB_RX_FRAM_SIZE                64u
#define USB_TX_FRAM_SIZE                64u
  

/* data type definiton  */


/* variable definition */
 
/* function declaration */
PACKET_PROCESS_APP_EXT uint8_t CheckOrCalcSum(uint8_t* apBuf,uint16_t alen , uint8_t* apSum, uint8_t aType);
PACKET_PROCESS_APP_EXT uint8_t RecieveMsgComb(uint8_t * pRxBuffer, uint16_t RxLen,uint8_t **apMsgOut,uint16_t* apLenOut,uint8_t aIntfType);
PACKET_PROCESS_APP_EXT uint8_t BleSendSingleFrame(uint16_t serviceBaseUUID,uint8_t *apData,uint16_t aLen);
PACKET_PROCESS_APP_EXT uint8_t BleMutilPacketTx(uint16_t serviceBaseUUID,uint8_t *apMsg,uint16_t aLen);
PACKET_PROCESS_APP_EXT uint8_t UsbMutilPacketTx(uint8_t *apMsg,uint16_t aLen);
PACKET_PROCESS_APP_EXT uint8_t UsbSendCmdAck(uint8_t *apMsg,uint16_t aLen);
#endif   /* end of header file */
