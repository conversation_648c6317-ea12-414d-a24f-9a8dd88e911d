/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : EvtProcessTaskDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/06/20    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef EVT_PROCESS_TASK_DRV_H
#define EVT_PROCESS_TASK_DRV_H

/*Include files*/
#include "EvtProcessTaskAppExt.h"

/* support module */
#include "includes.h"
#include "nrf_gpio.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
#define MSG_EVT_BURST_BUF_SIZE               1u

/*buffer number*/
#define MSG_RX_BUF_NUM_BURST_EVT             5u

#define SYS_PWR_ON_TIMEOUT                   300u  //300*10MS
#define SYS_PWR_OFF_TIMEOUT                  500u  //500*10MS
#define SYS_PWR_OFF_AUTO_TIMEOUT             30000u//180000u  //30000*10MS    30min

#define SYS_PWR_CON_IDLE_AUTO_TIMEOUT        6000u  //6000*10MS    1min

#define SYS_PWR_PCBA_WAIT_TIME               5u  //5*10MS    50ms

#define SYS_PWR_PCBA_CMD_WAIT_TIME           5 //5*1S

#define SYS_PWR_RF_CARRIER_EXIT_TIMEOUT      180  // 2 MIN

#define GATE_KEEP_ACTIVE_ACCEL               0.1f     //IMU ¾²Ö¹ÅÐ¶ÏãÐÖµ
/* data type definiton  */

// #define TRIGGER_HALF_VAL                     (TRIGGER_MAX_VAL/2u)
// #define TRIGGER_ENTER_THRESHOLD_RANGE        180u
#define GRIP_ENTER_THRESHOLD_RANGE        	 200u
#define TRIGGER_RELEASE_THRESHHOLD_VAL		 10u
#define FIRE_DEBOUNCING_MAX_TIMEOUT          5u
#define CHANGE_MAGAZINE_CLIP_TIMEOUT             5u
#define TP_DELAY_MAX_TIME                    8u
#define MAGAZINE_CLIP_HOLD_MAX_TIMEOUT           10u
#define BULLET_LOADER_MUTEX_MAX_TIMEOUT      10u

#endif /* End of header file */
