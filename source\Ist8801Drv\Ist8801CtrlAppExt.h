/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Ist8801AppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/07/31    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef IST8801_CTRL_APP_EXT_H
#define IST8801_CTRL_APP_EXT_H

/*Include files*/
#include "XimStdint.h"

/*declaration range definition*/
#ifdef  IST8801_CTRL_DRV_C
#define IST8801_CTRL_DRV_APP_EXT
#else 
#define IST8801_CTRL_DRV_APP_EXT extern
#endif

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define HALL_CTRL_TRIG               0x00
#define HALL_CTRL_GRIP               0x01

#define HALL_IST_ENABLE              1u
#define HALL_IST_DISABLE             0u
/* data type definiton  */


/* variable definition */
 
/* function declaration */
IST8801_CTRL_DRV_APP_EXT bool ist8801_Init(uint8_t Halltype,int16_t LThreshold,int16_t HThreshold);
IST8801_CTRL_DRV_APP_EXT void ist8801_Interrupt_Clear(uint8_t Halltype);
IST8801_CTRL_DRV_APP_EXT void ist8801_SuspendMode(uint8_t Halltype, uint8_t status );
IST8801_CTRL_DRV_APP_EXT bool ist8801_GetData(uint8_t Halltype,int16_t *data);

#endif    /* end of header file */
