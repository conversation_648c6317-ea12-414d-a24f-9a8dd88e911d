/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : UartRWAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/07/31    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef UART_RW_DRV_H
#define UART_RW_DRV_H


/*Include files*/
#include "UartRWAppExt.h"

/* support module */
#include "includes.h"

/*****following definitions can not be used outside this driver******/
/* macro definition */
#define PIN_NUM_UART_TX                             SYS_CFG_PIN_UART_TX
#define PIN_NUM_UART_RX                             SYS_CFG_PIN_UART_RX
#define PIN_NUM_UART_CTS                            SYS_CFG_PIN_UART_CTS
#define PIN_NUM_UART_RTS                            SYS_CFG_PIN_UART_RTS
#define UART_HWFC                                   SYS_CFG_UART_HWFC
#define UART_BAUD                                   SYS_CFG_UART_BAUD
    
#define UART_TX_BUF_SIZE 256                         /**< UART TX buffer size. */
#define UART_RX_BUF_SIZE 1                           /**< UART RX buffer size. */

#define UART_TX_ST_BUSY                             0x01
#define UART_TX_ST_IDLE                             0x00

#define UART_RX_ONE_FRAME_DIS                       (0x00u)
#define UART_RX_ONE_FRAME_EN                        (0x01u)


#define UART_RX_ONE_PACKAGE_FINISH                  (0x04u)
#define UART_RX_ONE_PACKAGE_EN                      (0x05u)


#define UART_RX_ONE_FRAME_TIMEOUT                   3u /*ms*/

#define UART_TX_IDEL_TIMEOUT                        10 /*ms*/

#define UART_FRAME_HEAD                             0XFE//0xA5
#define UART_FRAME_ACK                              0xAA
#define UART_FRAME_END                            0xFF
/*  structure definiton  */

      


#endif
