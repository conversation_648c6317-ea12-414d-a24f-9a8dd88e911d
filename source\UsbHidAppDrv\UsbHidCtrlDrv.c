/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : UsbCtrlDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef USB_HID_CTRL_DRV_C
#define USB_HID_CTRL_DRV_C
#endif

/* include files */
#include "UsbHidCtrlDrv.h"

/* static variable definition */

static USBD_RX_CallbackTypeDef     gpUsbRxFuc = NULL;
#pragma pack(4)
static uint8_t glEp0Buffer[EP0_MAXPACKETSIZE];
static uint8_t glEp0AckBuffer[EP0_MAXPACKETSIZE];
#pragma pack()
static uint8_t gLEp0AckLenght =0;
static uint8_t gLEp0RxLenght =0;

/**
 * @brief USB configured flag
 *
 * The flag that is used to mark the fact that USB is configured and ready
 * to transmit data
 */
static volatile bool m_usbd_configured = false;

/**
 * @brief USB suspended
 *
 * The flag that is used to mark the fact that USB is suspended and requires wake up
 * if new data is available.
 *
 * @note This variable is changed from the main loop.
 */
static bool m_usbd_suspended = false;

/**
 * @brief Mark the fact if remote wake up is enabled
 *
 * The internal flag that marks if host enabled the remote wake up functionality in this device.
 */
static
#if REMOTE_WU
    volatile // Disallow optimization only if Remote wakeup is enabled
#endif
bool m_usbd_rwu_enabled = false;

/**
 * @brief The flag for mouse position send pending
 *
 * Setting this flag means that USB endpoint is busy by sending
 * last mouse position.
 */
static volatile bool m_send_Usb_end_point = false;

#ifdef NRF52833_XXAA
static const uint8_t get_descriptor_device[] = {
    USBD_DEVICE_DESCRIPTOR
};

static const uint8_t get_descriptor_configuration[] = {
    USBD_CONFIG_DESCRIPTOR,
    USBD_INTERFACE0_DESCRIPTOR,
    USBD_HID0_DESCRIPTOR,
    USBD_ENDPOINT1_DESCRIPTOR
};
static const uint8_t get_descriptor_string_lang[] = {
    USBD_STRING_LANG
};
static const uint8_t get_descriptor_string_manuf[] = {
    USBD_STRING_MANUFACTURER
};
static const uint8_t get_descriptor_string_prod[] = {
    USBD_STRING_PRODUCT
};
static const uint8_t get_descriptor_report_interface_0[] = {
    USBD_MOUSE_REPORT_DESCRIPTOR
};

static const uint8_t get_config_resp_configured[]   = {1};
static const uint8_t get_config_resp_unconfigured[] = {0};

static const uint8_t get_status_device_resp_nrwu[] = {
    ((DEVICE_SELF_POWERED) ? 1 : 0), //LSB first: self-powered, no remoteWk
    0
};
static const uint8_t get_status_device_resp_rwu[]  = {
    ((DEVICE_SELF_POWERED) ? 1 : 0) | 2, //LSB first: self-powered, remoteWk
    0
};

static const uint8_t get_status_interface_resp[] = {0, 0};
static const uint8_t get_status_ep_halted_resp[] = {1, 0};
static const uint8_t get_status_ep_active_resp[] = {0, 0};


#define GET_CONFIG_DESC_SIZE    sizeof(get_descriptor_configuration)
#define GET_INTERFACE_DESC_SIZE 9
#define GET_HID_DESC_SIZE       9
#define GET_ENDPOINT_DESC_SIZE  7

#define get_descriptor_interface_0 \
    &get_descriptor_configuration[9]
#define get_descriptor_hid_0       \
    &get_descriptor_configuration[9+GET_INTERFACE_DESC_SIZE]
#define get_descriptor_endpoint_1  \
    &get_descriptor_configuration[9+GET_INTERFACE_DESC_SIZE+GET_HID_DESC_SIZE]


/* static function declaration */
/**
 * @brief Setup all the endpoints for selected configuration
 *
 * Function sets all the endpoints for specific configuration.
 *
 * @note
 * Setting the configuration index 0 means technically disabling the HID interface.
 * Such configuration should be set when device is starting or USB reset is detected.
 *
 * @param index Configuration index
 *
 * @retval NRF_ERROR_INVALID_PARAM Invalid configuration
 * @retval NRF_SUCCESS             Configuration successfully set
 */
static ret_code_t ep_configuration(uint8_t index)
{
    if ( index == 1 )
    {
        nrf_drv_usbd_ep_dtoggle_clear(NRF_DRV_USBD_EPIN1);
        nrf_drv_usbd_ep_stall_clear(NRF_DRV_USBD_EPIN1);
        nrf_drv_usbd_ep_enable(NRF_DRV_USBD_EPIN1);
        m_usbd_configured = true;
        m_send_Usb_end_point = false;
        memset(glEp0Buffer,0xff,sizeof(glEp0Buffer));
        memset(glEp0AckBuffer,0xff,sizeof(glEp0Buffer));
        gLEp0AckLenght =0;
        gLEp0RxLenght =0;
        nrf_drv_usbd_setup_clear();
    }
    else if ( index == 0 )
    {
        nrf_drv_usbd_ep_disable(NRF_DRV_USBD_EPIN1);
        m_usbd_configured = false;
        m_send_Usb_end_point = false;
        nrf_drv_usbd_setup_clear();
    }
    else
    {
        return NRF_ERROR_INVALID_PARAM;
    }
    return NRF_SUCCESS;
}

/**
 * @name Processing setup requests
 *
 * @{
 */
/**
 * @brief Respond on ep 0
 *
 * Auxiliary function for sending respond on endpoint 0
 * @param[in] p_setup Pointer to setup data from current setup request.
 *                    It would be used to calculate the size of data to send.
 * @param[in] p_data  Pointer to the data to send.
 * @param[in] size    Number of bytes to send.
 * @note Data pointed by p_data has to be available till the USBD_EVT_BUFREADY event.
 */
static void respond_setup_data(
    nrf_drv_usbd_setup_t const * const p_setup,
    void const * p_data, size_t size)
{
    /* Check the size against required response size */
    if (size > p_setup->wLength)
    {
        size = p_setup->wLength;
    }
    ret_code_t ret;
    nrf_drv_usbd_transfer_t transfer =
    {
        .p_data = {.tx = p_data},
        .size = size
    };
    ret = nrf_drv_usbd_ep_transfer(NRF_DRV_USBD_EPIN0, &transfer);
    if (ret != NRF_SUCCESS)
    {
        NRF_LOG_INFO("Transfer starting failed: %d", (uint32_t)ret);
    }
    ASSERT(ret == NRF_SUCCESS);
    UNUSED_VARIABLE(ret);
}

static void receive_setup_data(nrf_drv_usbd_setup_t const * const p_setup,uint8_t * p_data, size_t size)
{
    ret_code_t ret;
    nrf_drv_usbd_transfer_t transfer =
    {
        .p_data = {.rx = p_data},
        .size = size,
        .flags = 0
    };
    ret = nrf_drv_usbd_ep_transfer(NRF_DRV_USBD_EPOUT0, &transfer);
    if (ret != NRF_SUCCESS)
    {
        NRF_LOG_INFO("Transfer starting failed: %d", (uint32_t)ret);
    }
    ASSERT(ret == NRF_SUCCESS);
    UNUSED_VARIABLE(ret);
    nrf_drv_usbd_setup_data_clear();
}

/** React to GetStatus */
static void usbd_setup_GetStatus(nrf_drv_usbd_setup_t const * const p_setup)
{
    switch (p_setup->bmRequestType)
    {
    case 0x80: // Device
        if (((p_setup->wIndex) & 0xff) == 0)
        {
            respond_setup_data(
                p_setup,
                m_usbd_rwu_enabled ? get_status_device_resp_rwu : get_status_device_resp_nrwu,
                sizeof(get_status_device_resp_nrwu));
            return;
        }
        break;
    case 0x81: // Interface
        if (m_usbd_configured) // Respond only if configured
        {
            if (((p_setup->wIndex) & 0xff) == 0) // Only interface 0 supported
            {
                respond_setup_data(
                    p_setup,
                    get_status_interface_resp,
                    sizeof(get_status_interface_resp));
                return;
            }
        }
        break;
    case 0x82: // Endpoint
        if (((p_setup->wIndex) & 0xff) == 0) // Endpoint 0
        {
            respond_setup_data(
                p_setup,
                get_status_ep_active_resp,
                sizeof(get_status_ep_active_resp));
            return;
        }
        if (m_usbd_configured) // Other endpoints responds if configured
        {
            if (((p_setup->wIndex) & 0xff) == NRF_DRV_USBD_EPIN1)
            {
                if (nrf_drv_usbd_ep_stall_check(NRF_DRV_USBD_EPIN1))
                {
                    respond_setup_data(
                        p_setup,
                        get_status_ep_halted_resp,
                        sizeof(get_status_ep_halted_resp));
                    return;
                }
                else
                {
                    respond_setup_data(
                        p_setup,
                        get_status_ep_active_resp,
                        sizeof(get_status_ep_active_resp));
                    return;
                }
            }
        }
        break;
    default:
        break; // Just go to stall
    }
    NRF_LOG_INFO("Unknown status: 0x%2x", p_setup->bmRequestType);
    nrf_drv_usbd_setup_stall();
}

static void usbd_setup_ClearFeature(nrf_drv_usbd_setup_t const * const p_setup)
{
    if ((p_setup->bmRequestType) == 0x02) // standard request, recipient=endpoint
    {
        if ((p_setup->wValue) == 0)
        {
            if ((p_setup->wIndex) == NRF_DRV_USBD_EPIN1)
            {
                nrf_drv_usbd_ep_stall_clear(NRF_DRV_USBD_EPIN1);
                nrf_drv_usbd_setup_clear();
                return;
            }
        }
    }
    else if ((p_setup->bmRequestType) ==  0x0) // standard request, recipient=device
    {
        if (REMOTE_WU)
        {
            if ((p_setup->wValue) == 1) // Feature Wakeup
            {
                m_usbd_rwu_enabled = false;
                nrf_drv_usbd_setup_clear();
                return;
            }
        }
    }
    else if((p_setup->bmRequestType & USB_REQ_TYPE_MASK) == USB_REQ_TYPE_CLASS)  //class type
    {
        if(gLEp0AckLenght == 0)
        {
            memset(glEp0AckBuffer,0xFF,sizeof(glEp0AckBuffer));
            respond_setup_data(p_setup,glEp0AckBuffer,64);
        }
        else
        {
            respond_setup_data(p_setup,glEp0AckBuffer,64);
        }
        gLEp0AckLenght = 0;
        return;
    }
    NRF_LOG_INFO("Unknown feature to clear");
    nrf_drv_usbd_setup_stall();
}

static void usbd_setup_SetFeature(nrf_drv_usbd_setup_t const * const p_setup)
{
    if ((p_setup->bmRequestType) == 0x02) // standard request, recipient=endpoint
    {
        if ((p_setup->wValue) == 0) // Feature HALT
        {
            if ((p_setup->wIndex) == NRF_DRV_USBD_EPIN1)
            {
                nrf_drv_usbd_ep_stall(NRF_DRV_USBD_EPIN1);
                nrf_drv_usbd_setup_clear();
                return;
            }
        }
    }
    else if ((p_setup->bmRequestType) ==  0x0) // standard request, recipient=device
    {
        if (REMOTE_WU)
        {
            if ((p_setup->wValue) == 1) // Feature Wakeup
            {
                m_usbd_rwu_enabled = true;
                nrf_drv_usbd_setup_clear();
                return;
            }
        }
    }
    NRF_LOG_INFO("Unknown feature to set");
    nrf_drv_usbd_setup_stall();
}

static void usbd_setup_GetDescriptor(nrf_drv_usbd_setup_t const * const p_setup)
{
    //determine which descriptor has been asked for
    switch ((p_setup->wValue) >> 8)
    {
    case 1: // Device
        if ((p_setup->bmRequestType) == 0x80)
        {
            respond_setup_data(
                p_setup,
                get_descriptor_device,
                sizeof(get_descriptor_device));
            return;
        }
        break;
    case 2: // Configuration
        if ((p_setup->bmRequestType) == 0x80)
        {
            respond_setup_data(
                p_setup,
                get_descriptor_configuration,
                GET_CONFIG_DESC_SIZE);
            return;
        }
        break;
    case 3: // String
        if ((p_setup->bmRequestType) == 0x80)
        {
            // Select the string
            switch ((p_setup->wValue) & 0xFF)
            {
            case USBD_STRING_LANG_IX:
                respond_setup_data(
                    p_setup,
                    get_descriptor_string_lang,
                    sizeof(get_descriptor_string_lang));
                return;
            case USBD_STRING_MANUFACTURER_IX:
                respond_setup_data(
                    p_setup,
                    get_descriptor_string_manuf,
                    sizeof(get_descriptor_string_manuf));
                return;
            case USBD_STRING_PRODUCT_IX:
                respond_setup_data(p_setup,
                    get_descriptor_string_prod,
                    sizeof(get_descriptor_string_prod));
                return;
            default:
                break;
            }
        }
        break;
    case 4: // Interface
        if ((p_setup->bmRequestType) == 0x80)
        {
            // Which interface?
            if ((((p_setup->wValue) & 0xFF) == 0))
            {
                respond_setup_data(
                    p_setup,
                    get_descriptor_interface_0,
                    GET_INTERFACE_DESC_SIZE);
                return;
            }
        }
        break;
    case 5: // Endpoint
        if ((p_setup->bmRequestType) == 0x80)
        {
            // Which endpoint?
            if (((p_setup->wValue) & 0xFF) == 1)
            {
                respond_setup_data(
                    p_setup,
                    get_descriptor_endpoint_1,
                    GET_ENDPOINT_DESC_SIZE);
                return;
            }
        }
        break;
    case 0x21: // HID
        if ((p_setup->bmRequestType) == 0x81)
        {
            // Which interface
            if (((p_setup->wValue) & 0xFF) == 0)
            {
                respond_setup_data(
                    p_setup,
                    get_descriptor_hid_0,
                    GET_HID_DESC_SIZE);
                return;
            }
        }
        break;
    case 0x22: // HID report
        if ((p_setup->bmRequestType) == 0x81)
        {
            // Which interface?
            if (((p_setup->wValue) & 0xFF) == 0)
            {
                respond_setup_data(
                    p_setup,
                    get_descriptor_report_interface_0,
                    sizeof(get_descriptor_report_interface_0));
                return;
            }
        }
        break;
    default:
        break; // Not supported - go to stall
    }

    NRF_LOG_INFO("Unknown descriptor requested: 0x%2x, type: 0x%2x or value: 0x%2x",
        p_setup->wValue >> 8,
        p_setup->bmRequestType,
        p_setup->wValue & 0xFF);
    nrf_drv_usbd_setup_stall();
}

static void usbd_setup_GetConfig(nrf_drv_usbd_setup_t const * const p_setup)
{
    if (m_usbd_configured)
    {
        respond_setup_data(
            p_setup,
            get_config_resp_configured,
            sizeof(get_config_resp_configured));
    }
    else
    {
        respond_setup_data(
            p_setup,
            get_config_resp_unconfigured,
            sizeof(get_config_resp_unconfigured));
    }
}

static void usbd_setup_SetConfig(nrf_drv_usbd_setup_t const * const p_setup)
{
    if ((p_setup->bmRequestType) == 0x00)
    {
        // accept only 0 and 1
        if (((p_setup->wIndex) == 0) && ((p_setup->wLength) == 0) &&
            ((p_setup->wValue) <= UINT8_MAX))
        {
            if (NRF_SUCCESS == ep_configuration((uint8_t)(p_setup->wValue)))
            {
                nrf_drv_usbd_setup_clear();
                return;
            }
        }
    }
   else if((p_setup->bmRequestType & USB_REQ_TYPE_MASK) == USB_REQ_TYPE_CLASS)  //class type
   {
        gLEp0RxLenght = 0;
        memset(glEp0Buffer,0xFF,sizeof(glEp0Buffer));
        receive_setup_data(p_setup,glEp0Buffer,64);
        return;
    }
    NRF_LOG_INFO("Wrong configuration: Index: 0x%2x, Value: 0x%2x.",
        p_setup->wIndex,
        p_setup->wValue);
    nrf_drv_usbd_setup_stall();
}

static void usbd_setup_SetIdle(nrf_drv_usbd_setup_t const * const p_setup)
{
    if (p_setup->bmRequestType == 0x21)
    {
        //accept any value
        nrf_drv_usbd_setup_clear();
        return;
    }
    NRF_LOG_INFO("Set Idle wrong type: 0x%2x.", p_setup->bmRequestType);
    nrf_drv_usbd_setup_stall();
}

static void usbd_setup_SetInterface(
    nrf_drv_usbd_setup_t const * const p_setup)
{
    //no alternate setting is supported - STALL always
    NRF_LOG_INFO("No alternate interfaces supported.");
    nrf_drv_usbd_setup_stall();
}

static void usbd_setup_SetProtocol(
    nrf_drv_usbd_setup_t const * const p_setup)
{
    NRF_LOG_INFO("Set hid interface")
    if (p_setup->bmRequestType == 0x21)
    {
        //accept any value
        nrf_drv_usbd_setup_clear();
        return;
    }
    NRF_LOG_INFO("Set Protocol wrong type: 0x%2x.", p_setup->bmRequestType);
    nrf_drv_usbd_setup_stall();
}

/** @} */ /* End of processing setup requests functions */

static void usbd_event_handler(nrf_drv_usbd_evt_t const * const p_event)
{
    switch (p_event->type)
    {
    case NRF_DRV_USBD_EVT_SUSPEND:
        NRF_LOG_INFO("SUSPEND state detected");
        //m_usbd_suspend_state_req = true;
        break;
    case NRF_DRV_USBD_EVT_RESUME:
        NRF_LOG_INFO("RESUMING from suspend");
        //m_usbd_suspend_state_req = false;
        break;
    case NRF_DRV_USBD_EVT_WUREQ:
        NRF_LOG_INFO("RemoteWU initiated");
        //m_usbd_suspend_state_req = false;
        break;
    case NRF_DRV_USBD_EVT_RESET:
        {
            NRF_LOG_INFO("usb reset");
            m_usbd_configured = false;
            m_send_Usb_end_point = false;
            memset(glEp0Buffer,0xff,sizeof(glEp0Buffer));
            memset(glEp0AckBuffer,0xff,sizeof(glEp0Buffer));
            gLEp0AckLenght =0;
            gLEp0RxLenght =0;
            ret_code_t ret = ep_configuration(0);
            ASSERT(ret == NRF_SUCCESS);
            UNUSED_VARIABLE(ret);
            //m_usbd_suspend_state_req = false;
            break;
        }
    case NRF_DRV_USBD_EVT_SOF:
        {
            break;
        }
    case NRF_DRV_USBD_EVT_EPTRANSFER:
        if (NRF_DRV_USBD_EPIN1 == p_event->data.eptransfer.ep)
        {
            m_send_Usb_end_point = false;
        }
        else
        if (NRF_DRV_USBD_EPIN0 == p_event->data.eptransfer.ep)
        {
            if (NRF_USBD_EP_OK == p_event->data.eptransfer.status)
            {
                if (!nrf_drv_usbd_errata_154())
                {
                    /* Transfer ok - allow status stage */
                    nrf_drv_usbd_setup_clear();
                }
            }
            else if (NRF_USBD_EP_ABORTED == p_event->data.eptransfer.status)
            {
                /* Just ignore */
                NRF_LOG_INFO("Transfer aborted event on EPIN0");
            }
            else
            {
                NRF_LOG_INFO("Transfer failed on EPIN0: %d", p_event->data.eptransfer.status);
                nrf_drv_usbd_setup_stall();
            }
        }
        else
        if (NRF_DRV_USBD_EPOUT0 == p_event->data.eptransfer.ep)
        {
            /* NOTE: No EPOUT0 data transfers are used.
             * The code is here as a pattern how to support such a transfer. */
            if (NRF_USBD_EP_OK == p_event->data.eptransfer.status)
            {
                 if((p_event->data.eptransfer.ep & (~NRF_USBD_EP_DIR_Msk)) == NRF_DRV_USBD_EPOUT0)
                 {
                     gLEp0RxLenght = nrf_drv_usbd_epout_size_get(NRF_DRV_USBD_EPOUT0);
                     
                     if(gpUsbRxFuc != NULL)
                     {
                         gpUsbRxFuc(glEp0Buffer,gLEp0RxLenght);
                     }
                    // NRF_LOG_INFO("setup rx cmd len=%d,%x,%x,%x",gLEp0RxLenght,glEp0Buffer[0],glEp0Buffer[1],glEp0Buffer[2]);
                 }
                /* NOTE: Data values or size may be tested here to decide if clear or stall.
                 * If errata 154 is present the data transfer is acknowledged by the hardware. */
                if (!nrf_drv_usbd_errata_154())
                {
                    /* Transfer ok - allow status stage */
                    nrf_drv_usbd_setup_clear();
                }
            }
            else if (NRF_USBD_EP_ABORTED == p_event->data.eptransfer.status)
            {
                /* Just ignore */
                NRF_LOG_INFO("Transfer aborted event on EPOUT0");
            }
            else
            {
                NRF_LOG_INFO("Transfer failed on EPOUT0: %d", p_event->data.eptransfer.status);
                nrf_drv_usbd_setup_stall();
            }
        }
        else
        {
            /* Nothing to do */
        }
        break;
    case NRF_DRV_USBD_EVT_SETUP:
        {
            nrf_drv_usbd_setup_t setup;
            nrf_drv_usbd_setup_get(&setup);
            //NRF_LOG_INFO("setup type=%x,req=%x,wValue=%x,index=%x,lenght=%x",setup.bmRequestType,setup.bRequest,setup.wValue,setup.wIndex,setup.wLength);
            switch (setup.bRequest)
            {
            case 0x00: // GetStatus
                usbd_setup_GetStatus(&setup);
                break;
            case 0x01: // CleartFeature
                usbd_setup_ClearFeature(&setup);
                break;
            case 0x03: // SetFeature
                usbd_setup_SetFeature(&setup);
                break;
            case 0x05: // SetAddress
                //nothing to do, handled by hardware; but don't STALL
                break;
            case 0x06: // GetDescriptor
                usbd_setup_GetDescriptor(&setup);
                break;
            case 0x08: // GetConfig
                usbd_setup_GetConfig(&setup);
                break;
            case 0x09: // SetConfig
                usbd_setup_SetConfig(&setup);
                break;
            //HID class
            case 0x0A: // SetIdle
                usbd_setup_SetIdle(&setup);
                break;
            case 0x0B: // SetProtocol or SetInterface
                if (setup.bmRequestType == 0x01) // standard request, recipient=interface
                {
                    usbd_setup_SetInterface(&setup);
                }
                else if (setup.bmRequestType == 0x21) // class request, recipient=interface
                {
                    usbd_setup_SetProtocol(&setup);
                }
                else
                {
                    NRF_LOG_INFO("Command 0xB. Unknown request: 0x%2x", setup.bmRequestType);
                    nrf_drv_usbd_setup_stall();
                }
                break;
            default:
                NRF_LOG_INFO("Unknown request: 0x%2x", setup.bRequest);
                nrf_drv_usbd_setup_stall();
                return;
            }
            break;
        }
    default:
        break;
    }
}
#endif
/* static function definition */

/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  CamWriteReg
* Description   :  USBD_CUSTOM_HID_SendReport_FS  Send the report to the Host
*
* Inputs        : @param  report: specifies the register address which will be transmitted 
*                 @param  len: send data pointer 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  USB_RW_ST_OK if all operations are OK else USB_RW_ST_BUSY
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t USBD_CUSTOM_HID_SendDataMsg(const uint8_t *report,uint16_t len)
{
    uint8_t ret = USB_RW_ST_OK;
#ifdef NRF52833_XXAA
    static  nrf_drv_usbd_transfer_t transfer =
    {
        .p_data = {.tx = NULL},
        .size = 0
    };
    if(m_usbd_configured && (!m_send_Usb_end_point))
    {
        if(len > EP0_MAXPACKETSIZE)
        {
            len = EP0_MAXPACKETSIZE;
        }
         /* Send data */
        transfer.p_data.tx = report;
        transfer.size = len;
        transfer.flags = 0;
        m_send_Usb_end_point = true;
        UNUSED_RETURN_VALUE(nrf_drv_usbd_ep_transfer(
            NRF_DRV_USBD_EPIN1,
            &transfer));
    }
    else
    {
        ret = USB_RW_ST_BUSY;
    }
#endif
    return ret; 
}

/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  GetUsbEnumSt()
* Description   :  USBD_CUSTOM_HID_SendReport_FS  Send the report to the Host
*
* Inputs        : @param  report: specifies the register address which will be transmitted 
*                 @param  len: send data pointer 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  USB_RW_ST_OK if all operations are OK else USB_RW_ST_BUSY
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t GetUsbEnumSt(void)
{
    uint8_t ret = USB_RW_ST_OK;
    if(!m_usbd_configured)
    {
        ret = USB_RW_ST_FAIL;
    }
    return ret; 
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  USBD_CUSTOM_HID_SendAckMsg
* Description   :  fill GET_REPORT buffer 
*
* Inputs        : @param  Ack: fill data pointer
*                 @param  len: fill data length
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  USB_RW_ST_OK if all operations are OK else USB_RW_ST_BUSY
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t USBD_CUSTOM_HID_SendAckMsg(const uint8_t *Ack,uint16_t len)
{
   if(len > EP0_MAXPACKETSIZE)
   {
      len =EP0_MAXPACKETSIZE;
   }

   memcpy(glEp0AckBuffer,Ack,len);
   gLEp0AckLenght = len;
    return USB_RW_ST_OK; 
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  USB_DEVICE_Init
* Description   :  Init Device Library,Add Supported Class and Start the library
*
* Inputs        : @param  apUsbCallback: usb receive data callback  fuction pointer
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void USB_DEVICE_Init(USBD_RX_CallbackTypeDef apUsbCallback)
{

    ret_code_t ret;
    /* Init Device Library,Add Supported Class and Start the library*/
    gpUsbRxFuc = apUsbCallback;
    m_usbd_configured = false;
    m_send_Usb_end_point = false;
    memset(glEp0Buffer,0xff,sizeof(glEp0Buffer));
    memset(glEp0AckBuffer,0xff,sizeof(glEp0Buffer));
    gLEp0AckLenght =0;
    gLEp0RxLenght =0;
#ifdef NRF52833_XXAA
    NRF_LOG_INFO("USDB example started.");
    uint32_t temp1 = nrf_drv_usbd_errata_104();
    uint32_t temp2 = nrf_drv_usbd_errata_154();
    if (NRF_DRV_USBD_ERRATA_ENABLE)
    {
        NRF_LOG_INFO("USB errata 104 %s", (uint32_t)(temp1 ? "enabled" : "disabled"));
        NRF_LOG_INFO("USB errata 154 %s", (uint32_t)(temp2 ? "enabled" : "disabled"));
    }
    /* USB work starts right here */
    ret = nrf_drv_usbd_init(usbd_event_handler);
    APP_ERROR_CHECK(ret);

    /* Configure selected size of the packed on EP0 */
    nrf_drv_usbd_ep_max_packet_size_set(NRF_DRV_USBD_EPOUT0, EP0_MAXPACKETSIZE);
    nrf_drv_usbd_ep_max_packet_size_set(NRF_DRV_USBD_EPIN0, EP0_MAXPACKETSIZE);
#endif
}
void StartUsb(void)
{
#ifdef NRF52833_XXAA
    if (!nrf_drv_usbd_is_started())
    {
        nrf_drv_usbd_start(true);
    }
#endif
}
void StopUsb(void)
{
#ifdef NRF52833_XXAA
    if (nrf_drv_usbd_is_started())
    {
        nrf_drv_usbd_stop();
    }
    if (nrf_drv_usbd_is_enabled())
    {
        nrf_drv_usbd_disable();
    }
#endif
}
/***********************************************END**********************************************/
