/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : IntCallBackProcessDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0          2018/05/29     guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef CALL_BACK_PROCESS_DRV_C
#define CALL_BACK_PROCESS_DRV_C
#endif

/* include files */
#include "IntCallBackProcessDrv.h"

/* static variable definition */
static Task_Msg_Q_str gCmdRxPostMsg_t[MSG_RX_BUF_NUM_CMD];
static uint8_t gCmdRxMsgDataBuf[MSG_RX_BUF_NUM_CMD][MSG_RX_BUF_SIZE_CMD];

static Task_Msg_Q_str gUsbRxPostMsg_t[MSG_RX_BUF_NUM_CMD];
#pragma pack(4)
static uint8_t gUsbRxMsgDataBuf[MSG_RX_BUF_NUM_CMD][64];
#pragma pack()

static Task_Msg_Q_str gImuRxPostMsg_t[MSG_RX_BUF_NUM_IMU];
static Imu_SensorSampleData_str gImuRxMsgDataBuf[MSG_RX_BUF_NUM_IMU];

/* public function definition */
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  BtXimServiceEvtCallBack
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void BleConnectEvtCallBack(ble_evt_t const * p_ble_evt)
{
    Task_Msg_Q_str   evt_out;
    
    switch (p_ble_evt->header.evt_id)
    {
        case BLE_GAP_EVT_CONNECTED:
            
            gSystemSt_t.mBleHostMac[0]= p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr[0];
            gSystemSt_t.mBleHostMac[1]= p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr[1];
            gSystemSt_t.mBleHostMac[2]= p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr[2];
            gSystemSt_t.mBleHostMac[3]= p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr[3];
            gSystemSt_t.mBleHostMac[4]= p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr[4];
            gSystemSt_t.mBleHostMac[5]= p_ble_evt->evt.gap_evt.params.connected.peer_addr.addr[5]; 
            evt_out.mMsgLength = 0;
            evt_out.mType = MSG_TASK_Q_BLE_CONN;
            evt_out.mServiceUUID = p_ble_evt->evt.gap_evt.conn_handle;
            if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Ble))
            {
                NRF_LOG_INFO("discover timeout call back put status err \n");
            }
            break;
        case BLE_GAP_EVT_DISCONNECTED:
            evt_out.mMsgLength = 0;
            gSystemSt_t.mImuSendCnt = 0;
            evt_out.mType = MSG_TASK_Q_BLE_DISCONN;
            evt_out.mServiceUUID = p_ble_evt->evt.gap_evt.conn_handle;
            if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Ble))
            {
                NRF_LOG_INFO("discover timeout call back put status err \n");
            }
            break;
        case BLE_GAP_EVT_CONN_PARAM_UPDATE:
            evt_out.mMsgLength = p_ble_evt->evt.gap_evt.conn_handle;
            evt_out.mServiceUUID = p_ble_evt->evt.gap_evt.params.conn_param_update.conn_params.max_conn_interval;
            evt_out.mType = MSG_TASK_Q_BLE_CONN_PARAM;
            NRF_LOG_INFO("conn_params Updata event max interval is=%x\n",evt_out.mServiceUUID);
            if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Ble))
            {
                NRF_LOG_INFO("discover timeout call back put status err \n");
            } 
        default:
            // No implementation needed.
            break;
    }
}
void BleAdvEvtCallBack(ble_adv_evt_t p_ble_adv_evt)
{
    Task_Msg_Q_str   evt_out;
    
    switch (p_ble_adv_evt)
    {
        case BLE_ADV_EVT_FAST:
            NRF_LOG_INFO("Fast advertising.");
            break;
        case BLE_ADV_EVT_IDLE:
            evt_out.mMsgLength = 0;
            evt_out.mType = MSG_TASK_Q_BLE_ADV_STOP;
            if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Ble))
            {
                NRF_LOG_INFO("discover timeout call back put status err \n");
            }
            break;
        default:
            // No implementation needed.
            break;
    }
}

void BleParamsUpdataEvtCallBack(const ble_gap_conn_params_t  * p_params ,uint16_t conn_handle) 
{
    Task_Msg_Q_str   evt_out;
    
    evt_out.mMsgLength = conn_handle;
    evt_out.mServiceUUID = p_params->max_conn_interval;
    evt_out.mType = MSG_TASK_Q_BLE_CONN_PARAM;
    NRF_LOG_INFO("conn_params Updata event max interval is=%x\n",evt_out.mServiceUUID);
    /*if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Ble))
    {
        NRF_LOG_INFO("discover timeout call back put status err \n");
    } */
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  EvtTimerCallBack
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void Timer_10ms_timeout_handler(void * p_context)
{
    Task_Msg_Q_str   evt_out;
    evt_out.mMsgLength = 0;
    evt_out.mType = MSG_TASK_Q_TIMER_10MS;
#ifdef MIRAGE2B_BOARD
   UartRxFrameTimeCnt_ISR();
#endif
   LedCtrlFlashCnt();
   if(gSystemSt_t.BlePipeNotifyEn_t.Timer10msEn == SYS_CTRL_EN )
   { 
       if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Timer))
       {
            NRF_LOG_INFO("10ms timer discover timeout call back put status err \n");
       }
   }
}
void Timer_1s_timeout_handler(void * p_context)
{
    Task_Msg_Q_str   evt_out;
    evt_out.mMsgLength = 0;
    evt_out.mType = MSG_TASK_Q_TIMER_1S;
    gSystemSt_t.mImuSendHz = gSystemSt_t.mImuSendCnt;
    gSystemSt_t.mImuSendCnt = 0;
   if(NRF_SUCCESS != app_sched_event_put(&evt_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Timer))
   {
        NRF_LOG_INFO("1s timer discover timeout call back put status err \n");
   }
   
}
void UartRxDataCallBack(uint8_t *apRxData,uint16_t len)
{
    uint8_t i =0;
    Task_Msg_Q_str   msg_out;
    for(i=0;i< MSG_RX_BUF_NUM_CMD;i++)
    {
        if(gCmdRxPostMsg_t[i].mType == MSG_TASK_Q_RUF)
        {
            break;
        }
        else
        {
            //do nothing
        }
    }
    if(i < MSG_RX_BUF_NUM_CMD)
    {
        gCmdRxPostMsg_t[i].mMsgLength = len;
        gCmdRxPostMsg_t[i].mType = MSG_TASK_Q_UART;
        memcpy(gCmdRxPostMsg_t[i].mpBuf,apRxData,len);
        msg_out.mMsgLength = len;
        msg_out.mType = MSG_TASK_Q_UART;
        msg_out.mpBuf = &gCmdRxPostMsg_t[i];
        if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_RxMsgProcess_Uart))
        {
            NRF_LOG_INFO("uart discover timeout call back put status err \n");
        }
    }
    else
    {
        i = MSG_RX_BUF_NUM_CMD -1u;
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  UsbRecvCmdCallBack
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void UsbRecvCmdISRCallBack(uint8_t *apData,uint16_t aPacketRxLen)
{
    static uint16_t LastFramLen = 0;
    uint16_t RxMsgLen = 0;
    uint8_t i =0;
    uint8_t *apUsbData = NULL;
    uint16_t MsgLenTemp = 0;
     Task_Msg_Q_str   msg_out;
    
    for(i=0;i< MSG_RX_BUF_NUM_CMD;i++)
    {
        if(gUsbRxPostMsg_t[i].mType == MSG_TASK_Q_RUF)
        {
            break;
        }
        else
        {
            //do nothing
        }
    }
    memcpy((uint8_t *)gUsbRxPostMsg_t[i].mpBuf,&apData[1],aPacketRxLen);
    if((apData[0] & (~0x20)) == 0x00) /*first number*/
    {
        MsgLenTemp = apData[4];
        MsgLenTemp = (MsgLenTemp << 8u) + apData[3];
        if((MsgLenTemp + 6u) > (USB_RX_FRAM_SIZE - 1u))   //  exceed 1 frame length
        {
            LastFramLen = (MsgLenTemp + 6u) % (USB_RX_FRAM_SIZE - 1u);
            if(LastFramLen == 0u)
            {
                LastFramLen =  USB_RX_FRAM_SIZE - 1u;
            }
            else
            {
                //do nothing
            }
        }
        else
        {
            aPacketRxLen = (MsgLenTemp + 6u) +1u;
            LastFramLen = 0;
        }
    }
    else if((apData[0] & 0x20) == 0x20) /*last number*/
    {
        aPacketRxLen = LastFramLen +1u;
        LastFramLen = 0;
    }
    else
    {
        //do nothing
    }
    if(i < MSG_RX_BUF_NUM_CMD)
    {
        gUsbRxPostMsg_t[i].mMsgLength = aPacketRxLen -1u;
        gUsbRxPostMsg_t[i].mType = MSG_TASK_Q_USB;

        msg_out.mMsgLength = aPacketRxLen -1u;
        msg_out.mType = MSG_TASK_Q_USB;
        msg_out.mpBuf = &gUsbRxPostMsg_t[i];
        if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_RxMsgProcess_Usb))
        {
            NRF_LOG_INFO("usb discover timeout call back put status err \n");
        }
    }
    else
    {
        i = MSG_RX_BUF_NUM_CMD -1u;
    }
}

/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  BtXimServiceEvtCallBack
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void BtXimServiceEvtCallBack(ble_xim_evt_t *p_evt)
{
    uint8_t i =0;
    uint8_t *apBleData = NULL;
    uint16_t RxMsgLen = 0;
    Task_Msg_Q_str   msg_out;
    switch(p_evt->type )
    {
        case BLE_XIM_EVT_RX_DATA:
        {
            if(p_evt->p_xim->uuid_serves == BLE_BASE_UUID_SYS_CFG)    //config pipe
            {
                if(PACKET_OPERATE_SUCCESS == RecieveMsgComb((uint8_t *)(p_evt->params.rx_data.p_data),p_evt->params.rx_data.length,&apBleData,&RxMsgLen,SEND_INTERFACE_BLE))
                {
                    for(i=0;i< MSG_RX_BUF_NUM_CMD;i++)
                    {
                        if(gCmdRxPostMsg_t[i].mType == MSG_TASK_Q_RUF)
                        {
                            break;
                        }
                        else
                        {
                            //do nothing
                        }
                    }
                    if(i < MSG_RX_BUF_NUM_CMD)
                    {
                        gCmdRxPostMsg_t[i].mMsgLength = RxMsgLen;
                        gCmdRxPostMsg_t[i].mType = MSG_TASK_Q_CMD;
                        gCmdRxPostMsg_t[i].mServiceUUID = p_evt->p_xim->uuid_serves;
                        
                        memcpy(gCmdRxPostMsg_t[i].mpBuf,apBleData,RxMsgLen);
                        msg_out.mMsgLength = RxMsgLen;
                        msg_out.mType = MSG_TASK_Q_CMD;
                        msg_out.mpBuf = &gCmdRxPostMsg_t[i];
                        if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_RxMsgProcess_Ble))
                        {
                            NRF_LOG_INFO("discover timeout call back put status err \n");
                        }
                    }
                    else
                    {
                        i = MSG_RX_BUF_NUM_CMD -1u;
                    }
                }
                else
                {
                    //do nothing
                }
            }
            else
            {
                for(i=0;i< MSG_RX_BUF_NUM_CMD;i++)
                {
                    if(gCmdRxPostMsg_t[i].mType == MSG_TASK_Q_RUF)
                    {
                        break;
                    }
                    else
                    {
                        //do nothing
                    }
                }
                if(i < MSG_RX_BUF_NUM_CMD)
                {
                    gCmdRxPostMsg_t[i].mMsgLength = p_evt->params.rx_data.length; /*UUID  LENGTH*/
                    gCmdRxPostMsg_t[i].mType = MSG_TASK_Q_CMD;
                    gCmdRxPostMsg_t[i].mServiceUUID = p_evt->p_xim->uuid_serves;

                    memcpy(gCmdRxPostMsg_t[i].mpBuf,p_evt->params.rx_data.p_data,p_evt->params.rx_data.length);
                    
                    msg_out.mMsgLength = p_evt->params.rx_data.length;
                    msg_out.mType = MSG_TASK_Q_CMD;
                    msg_out.mpBuf = &gCmdRxPostMsg_t[i];
                    if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_RxMsgProcess_Ble))
                    {
                        NRF_LOG_INFO("cfg service discover timeout call back put status err \n");
                    }
                }
                else
                {
                    i = MSG_RX_BUF_NUM_CMD -1u;
                    printf("cmd Q Buf Full-con\n");
                }
            }
        }
        break;
        case BLE_XIM_EVT_TX_RDY:
        {
            //NRF_LOG_INFO("ble tx Ready service uuid =0x%x\n",p_evt->p_xim->uuid_serves);
        }
        break;
        case BLE_XIM_EVT_COMM_STARTED:
        {
            msg_out.mMsgLength = 0;
            msg_out.mType = MSG_TASK_Q_BLE_SERVICE_NOTIFY_EN;
            msg_out.mServiceUUID = p_evt->p_xim->uuid_serves;
            if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Ble))
            {
                NRF_LOG_INFO(" notify en discover timeout call back put status err \n");
            } 
            //NRF_LOG_INFO("Notification enabled service uuid =0x%x\n",p_evt->p_xim->uuid_serves);
        }
        break;
        case BLE_XIM_EVT_COMM_STOPPED:
        {
            if(p_evt->p_xim->uuid_serves == 0x180F) //batty service
            {
                gSystemSt_t.BlePipeNotifyEn_t.BattyPipe = SYS_CTRL_DIS;
            }
            else if(p_evt->p_xim->uuid_serves == BLE_BASE_UUID_IMU_NOR)
            {
                gSystemSt_t.BlePipeNotifyEn_t.DataPipe = SYS_CTRL_DIS;
            }
            else if(p_evt->p_xim->uuid_serves == BLE_BASE_UUID_SYS_CFG)
            {
                gSystemSt_t.BlePipeNotifyEn_t.CfgPipe = SYS_CTRL_DIS;
            }
            else if(p_evt->p_xim->uuid_serves == BLE_BASE_UUID_IMU_CALIB)
            {
                gSystemSt_t.BlePipeNotifyEn_t.CalibPipe = SYS_CTRL_DIS;
            }
            else if(p_evt->p_xim->uuid_serves == BLE_BASE_UUID_CALIB_CHECK)
            {
                gSystemSt_t.BlePipeNotifyEn_t.CalibCheckPipe = SYS_CTRL_DIS;
            }
            msg_out.mMsgLength = 0;
            msg_out.mType = MSG_TASK_Q_BLE_SERVICE_NOTIFY_DIS;
            msg_out.mServiceUUID = p_evt->p_xim->uuid_serves;
            if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_EvtProcess_Ble))
            {
                NRF_LOG_INFO("discover timeout call back put status err \n");
            }
            NRF_LOG_INFO("Notification Disabled service uuid =0x%x\n",p_evt->p_xim->uuid_serves);
        }
        break;
        default:
            //NRF_LOG_INFO("ble evt err\n");
            break;
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  ImuSampCallBack
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */
void BleSensorDataPost(void)
{
	uint8_t i =0;
    Task_Msg_Q_str   msg_out;
    for(i=0;i< MSG_RX_BUF_NUM_IMU;i++)
    {
        if(gImuRxPostMsg_t[i].mType == MSG_TASK_Q_RUF)
        {
            break;
        }
    }
    if(i < MSG_RX_BUF_NUM_IMU)
    {
        gImuRxPostMsg_t[i].mType = MSG_TASK_Q_IMU;
        gImuRxPostMsg_t[i].mServiceUUID = 0x0000;
        gImuRxPostMsg_t[i].mMsgLength =sizeof(uint64_t);
        msg_out.mMsgLength = sizeof(uint64_t);
        msg_out.mType = MSG_TASK_Q_IMU;
        msg_out.mpBuf = &gImuRxPostMsg_t[i];
        if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_RxMsgProcess_Ble))
        {
            NRF_LOG_INFO("Imu discover timeout call back put status err \n");
        }
    }
    else
    {
        NRF_LOG_INFO("IMU Q Buf Full-con\n");
        i = MSG_RX_BUF_NUM_IMU -1u;
    } 
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  ImuSampCallBack
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void ImuSampISRCallBack(void)       //running 150us
{
    // static uint8_t  delayCnt = 0;
    uint8_t i =0;
//    uint64_t time = get_stamp64_sync_finshed();
    Task_Msg_Q_str   msg_out;
    ImuSensorData_str   Acc_gyroData_t;

//    if(gImuDrv_t.get_bytes != NULL)
//    {
//        if(gImuDrv_t.get_bytes(&Acc_gyroData_t) == IMU_OPERATE_SUCCESS)     //
//        {
//            for(i=0;i< MSG_RX_BUF_NUM_IMU;i++)
//            {
//                if(gImuRxPostMsg_t[i].mType == MSG_TASK_Q_RUF)
//                {
//                    break;
//                }
//                else
//                {

//                    //do nothing
//                }
//            }
//            if(i < MSG_RX_BUF_NUM_IMU)
//            {
//                gImuRxPostMsg_t[i].mType = MSG_TASK_Q_IMU;
//                gImuRxPostMsg_t[i].mServiceUUID = 0x0000;
////                ((Imu_SensorSampleData_str*)(gImuRxPostMsg_t[i].mpBuf))->mTimeStamp  = time;
//                memcpy(((Imu_SensorSampleData_str*)(gImuRxPostMsg_t[i].mpBuf))->mImuRxMsgDataBuf,&Acc_gyroData_t,sizeof(Acc_gyroData_t));
//                gImuRxPostMsg_t[i].mMsgLength = sizeof(Acc_gyroData_t)+ sizeof(uint64_t);
//                msg_out.mMsgLength = sizeof(Acc_gyroData_t) + sizeof(uint64_t);
//                msg_out.mType = MSG_TASK_Q_IMU;
//                msg_out.mpBuf = &gImuRxPostMsg_t[i];
//                if(NRF_SUCCESS != app_sched_event_put(&msg_out,sizeof(Task_Msg_Q_str),Task_RxMsgProcess_Ble))
//                {
//                    NRF_LOG_INFO("Imu discover timeout call back put status err \n");
//                }
//            }
//            else
//            {
//                NRF_LOG_INFO("IMU Q Buf Full-con\n");
//                i = MSG_RX_BUF_NUM_IMU -1u;
//            } 
//        }
//    }
//    else
//    {
//        //do nothing
//    }
    //TEST_PIN_LOW();
}
void TpSampISRCallBack(void)       //running 472 <800us
{
    touchpad_motion_t   TpData_t;
    
    if(xim_touchpad_drv_read_point_info(&TpData_t))     //runing 
    {
        TpData_t.y = TpData_t.y >=1023?0:1023-TpData_t.y; 
        gSystemSt_t.mTpSampleData_t = TpData_t;
        if((TpData_t.atcive_event == TP_UP_EVT)
           ||(TpData_t.atcive_event == TP_IDLE_EVT)
          )
        {
            gSystemSt_t.mTouchRelease = true;
        }
        else 
        {
            gSystemSt_t.mTouchRelease = false;
        }
    }
    else
    {
        //do nothing
    }
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  RxMsgProcessTaskInit
* Description   :   
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void CallBackProcessInit(void)
{
    uint8_t i = 0;

    for(i = 0;i < MSG_RX_BUF_NUM_CMD; i++)
    {
        gCmdRxPostMsg_t[i].mType = MSG_TASK_Q_RUF;
        gCmdRxPostMsg_t[i].mMsgLength = 0u;
        gCmdRxPostMsg_t[i].mServiceUUID = 0u;
        gCmdRxPostMsg_t[i].mpBuf = gCmdRxMsgDataBuf[i];
    }
    for(i = 0;i < MSG_RX_BUF_NUM_CMD; i++)
    {
        gUsbRxPostMsg_t[i].mType = MSG_TASK_Q_RUF;
        gUsbRxPostMsg_t[i].mMsgLength = 0u;
        gUsbRxPostMsg_t[i].mServiceUUID = 0u;
        gUsbRxPostMsg_t[i].mpBuf = gUsbRxMsgDataBuf[i];
    }
    
    for(i = 0;i < MSG_RX_BUF_NUM_IMU; i++)
    {
        gImuRxPostMsg_t[i].mType = MSG_TASK_Q_RUF;
        gImuRxPostMsg_t[i].mMsgLength = 0u;
        gImuRxPostMsg_t[i].mServiceUUID = 0u;
        gImuRxPostMsg_t[i].mpBuf = &gImuRxMsgDataBuf[i];
    }
}
/***********************************************END**********************************************/



