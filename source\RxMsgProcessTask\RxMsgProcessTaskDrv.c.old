 /*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : RxMsgProcessTaskDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef RX_MSG_PROCESS_TASK_DRV_C
#define RX_MSG_PROCESS_TASK_DRV_C
#endif

/* include files */
#include "RxMsgProcessTaskDrv.h"

/* static variable definition */
static uint8_t  gSysMode = SYS_RUN_MODEL_NORMAL;
static uint32_t gAuthRngData = 0u;

static void convert_from_float_to_protocol_uint24_t_ypr(float in, protocol_uint24_t* p_out)
{
#define ypr_amplify 10000
    int32_t value = in * ypr_amplify;
    p_out->mostByte = (value >> 16) & 0xFF;
    p_out->midByte = (value >> 8) & 0xFF;
    p_out->lastByte = (value >> 0) & 0xFF;
}
static void convert_from_float_to_protocol_uint24_t_q4(float in, protocol_uint24_t* p_out)
{
#define q4_amplify 0x7FFFFF
    int32_t value = in * q4_amplify;
    p_out->mostByte = (value >> 16) & 0xFF;
    p_out->midByte = (value >> 8) & 0xFF;
    p_out->lastByte = (value >> 0) & 0xFF;
}
static void convert_from_uint16_t_to_protocol(uint16_t input, protocol_uint16_t* p_protocol_u16)
{
    p_protocol_u16->mostByte = (input >> 8) & 0xFF;
    p_protocol_u16->lastByte = (input >> 0) & 0xFF;
}
static void swap_byte(uint8_t* p_b1, uint8_t* p_b2)
{
    register uint8_t buffer = *p_b1;
    *p_b1 = *p_b2;
    *p_b2 = buffer;
}
static void revert_byte_19(uint8_t* p_byte_20)
{
    for(int i = 0;i<9;i++)
    {        
        swap_byte(p_byte_20+i, p_byte_20+18-i);
    }
}
/** @brief Function for getting vector of random numbers.
 *
 * @param[out] p_buff       Pointer to unit8_t buffer for storing the bytes.
 * @param[in]  length       Number of bytes to take from pool and place in p_buff.
 *
 * @retval     Number of bytes actually placed in p_buff.
 */
static uint8_t random_vector_generate(uint8_t * p_buff, uint8_t size)
{
    uint32_t err_code;
    uint8_t  available;

    nrf_drv_rng_bytes_available(&available);
    uint8_t length = MIN(size, available);

    err_code = nrf_drv_rng_rand(p_buff, length);
    APP_ERROR_CHECK(err_code);

    return length;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  MsgCmdProcess
* Description   :  process cmd from usb
*
* Inputs        : @param  apCmdMsg: recieve msg buffer pointer
*                 @param  None: 
*                 @param  None: 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval : send  status
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
static void UsbPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen)
{
    AckFormat_str aTxAck_t;
    uint32_t err_code;
    uint16_t MsgLen = apCmdMsg[3];
    MsgLen= (MsgLen << 8u) + apCmdMsg[2];
    uint8_t *pRxData = &apCmdMsg[4];
    uint32_t temp32 = 0;
//    uint16_t temp16 = 0;
    static uint8_t rng_init_finish = false;
    memset(&aTxAck_t, 0, sizeof(aTxAck_t));
    aTxAck_t.mRfu = 0x00;
    aTxAck_t.mFormatType = MSG_FORMAT_TYPE_ACK;                      
    aTxAck_t.mMsgCode = 0xFF;
    aTxAck_t.mCmdType = apCmdMsg[0];
    aTxAck_t.mCmdCode = apCmdMsg[1];
   /*MSG TYPE CHECK*/
    if(PACKET_OPERATE_SUCCESS == CheckOrCalcSum(apCmdMsg,(aMsgLen - 2u),&apCmdMsg[aMsgLen - 2u],PACKET_SUM_CHECK))
    {
        if(apCmdMsg[0] == MSG_FORMAT_TYPE_CAM_CMD)
        {
            switch(apCmdMsg[1])
            {
                case MSG_CODE_RND_GET:
                {
                    /*get system tilk*/
                    if (false == rng_init_finish)
                    {
                        rng_init_finish = true;
                        err_code = nrf_drv_rng_init(NULL);
                        APP_ERROR_CHECK(err_code);
                    }
                    aTxAck_t.mMsgLen  = 4 + 3u;
                    aTxAck_t.mData[0] = MSG_ACK_ST_SUCC;
                    gAuthRngData =  get_stamp64_sync_finshed();
                    random_vector_generate(&aTxAck_t.mData[1], 4u);

//                    gAuthRngData =   aTxAck_t.mData[1]      | aTxAck_t.mData[2]>>8u
//                                    |aTxAck_t.mData[3]>>16u | aTxAck_t.mData[4]>>24u;
										gAuthRngData = aTxAck_t.mData[4];
										gAuthRngData = (gAuthRngData << 8u) + aTxAck_t.mData[3];
										gAuthRngData = (gAuthRngData << 8u) + aTxAck_t.mData[2];
										gAuthRngData = (gAuthRngData << 8u) + aTxAck_t.mData[1];
                }
                break;
                case MSG_CODE_AUTH_CTRL:
                {
                    aTxAck_t.mMsgLen  = 3u;
                    aTxAck_t.mData[0] = MSG_ACK_ST_SUCC;
                    temp32 = pRxData[3];
                    temp32 = (temp32 << 8u ) + pRxData[2];
                    temp32 = (temp32 << 8u ) + pRxData[1];
                    temp32 = (temp32 << 8u ) + pRxData[0];
                    
                    gAuthRngData = (gAuthRngData + 0x20111124) * 7u; //calculate password
                    if(temp32 != gAuthRngData)
                    {
                        aTxAck_t.mData[0] = MSG_ACK_ST_FAIL;
                        DEBUG_PRINTF("Authentication fail\r\n");
                    }
                    else
                    {
                        /*go to manager mode*/
                        DEBUG_PRINTF("Authentication ok\r\n");
                    }
                    gSysMode = SYS_RUN_MODEL_MANAGE;
                }
                break;
                case MSG_CODE_READ_SYS_ST:
                {
                    DEBUG_PRINTF("read system info\r\n");
                    aTxAck_t.mMsgLen  = 3u;
                    aTxAck_t.mData[0] = MSG_ACK_ST_SUCC;
                    
                    // //HMD fualt status
                    // aTxAck_t.mData[1u] = gSystemSt_t.mSetMarkID;
                    // //Device status
                    // aTxAck_t.mData[2u] = gSystemSt_t.mDeviceSt_uni.byte;
                    // aTxAck_t.mData[3u] = 0;
                    // aTxAck_t.mData[4u] = gSystemSt_t.mFaultSt_uni.byte;
                    
                    // /*hardware mcu firmware and fpgaversion*/
                    // memcpy(&(aTxAck_t.mData[5u]), gSystemCfg_t.mProducInfo.ProdHardVer,4u);
                    // memcpy(&(aTxAck_t.mData[9u]), SoftInfo_t.ProdSoftVer,4u);
                    // aTxAck_t.mData[13u] = '0';
                    // aTxAck_t.mData[14u] = '0';
                    // aTxAck_t.mData[15u] = bit4_to_char((gSystemCfg_t.mTpVer >> 4) & 0x0F);
                    // aTxAck_t.mData[16u] = bit4_to_char((gSystemCfg_t.mTpVer >> 0) & 0x0F);

                    // /*HMD IMU version*/
                    // memcpy(&(aTxAck_t.mData[17u]),gSystemCfg_t.mImuAlgVer,4);
                    
                    // aTxAck_t.mMsgLen  = 3u + 21;

                    //Device status
                    aTxAck_t.mData[3u] = gSysMode 
                                        | gSystemSt_t.mDeviceSt_uni.Bits.mBleConnect << 3
                                        |gSystemSt_t.mDeviceSt_uni.Bits.mSysModel << 4;
                    /*hardware mcu firmware and fpgaversion*/
                    memcpy(&(aTxAck_t.mData[10u]), gSystemCfg_t.mProducInfo.ProdHardVer,4u);
                    memcpy(&(aTxAck_t.mData[14u]), SoftInfo_t.ProdSoftVer,4u);
                    
                    /*HMD IMU version*/
                    memcpy(&(aTxAck_t.mData[26u]), gSystemCfg_t.mImuAlgVer,4u);
                    
                    aTxAck_t.mMsgLen  = 3u + 29u;
                }
                break;
				case MSG_CODE_SYS_RESET:	
				{
					if(pRxData[0] == 0x00)
					{
						nrf_wdt_int_disable(0xFFFFFFFF);
						NVIC_SystemReset();
					}
					else if(pRxData[0] == 0x01)
					{
						// TODO add goto bootloader
						DEBUG_PRINTF("goto bootloader");
						temp32 = sd_power_gpregret_set(0, 0xB1);
						APP_ERROR_CHECK(temp32);
						NVIC_SystemReset();		
					}
				}
				break;
                case MSG_CODE_SYS_CFG_WRITE:
                {
                    aTxAck_t.mMsgLen  = 3u;
                    aTxAck_t.mData[0] = MSG_ACK_ST_SUCC;
                    Imu_10msTimer_Enable(0);
                    if(gSysMode == SYS_RUN_MODEL_MANAGE)
                    {
						switch(pRxData[0])
						{
							case 0x00:	// production SN
								pRxData[17] = '\0';  //name max lenght 16byte
								memcpy(gSystemCfg_t.mDataCfgSet.mProdSN.mdata,&pRxData[1],MsgLen -1u);
								gSystemCfg_t.mDataCfgSet.mProdSN.mLen = MsgLen -1u;
								gSystemCfg_t.mDataCfgSet.mProdSN.mdata[gSystemCfg_t.mDataCfgSet.mProdSN.mLen] = '\0';
								FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
								DEBUG_PRINTF("write product sn %d\r\n",gSystemCfg_t.mDataCfgSet.mProdSN.mLen);
								break;
							case 0x01:	// device name
								pRxData[16] = '\0';  //name max lenght 16byte
								memcpy(gSystemCfg_t.mDataCfgSet.mDeviceName.mdata,&pRxData[1],strlen((char*)pRxData));
								gSystemCfg_t.mDataCfgSet.mDeviceName.mLen = strlen((char*)pRxData);
								gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen] = '\0';
								if(pRxData[17] != 0)
								{
									gSystemCfg_t.mDataCfgSet.isSuffixEnabled = SYS_CTRL_EN;
								}
								else
								{
									gSystemCfg_t.mDataCfgSet.isSuffixEnabled = SYS_CTRL_DIS;
								}
								FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
								break;
							case 0x02:	//Model Name
								pRxData[17] = '\0';
								memcpy(gSystemCfg_t.mDataCfgSet.mModelName.mdata,&pRxData[1],strlen((char*)pRxData));
								gSystemCfg_t.mDataCfgSet.mModelName.mLen = strlen((char*)pRxData);
								gSystemCfg_t.mDataCfgSet.mModelName.mdata[gSystemCfg_t.mDataCfgSet.mModelName.mLen] = '\0';
								FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
								break;
							case 0x04:
								for(uint8_t i = 0; i < 6u; i++)
								{
									gSystemCfg_t.mDataCfgSet.mLocalMac_t.mMac[i] = pRxData[i+2];
								}
								gSystemCfg_t.mDataCfgSet.mLocalMac_t.mEnable = 0x01;
								FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
								Ble_Dfu_Bootloader_enter_prepare();
								(void)sd_power_gpregret_clr(0, 0xffffffff);
								(void)sd_power_gpregret_set(0, PWR_CTRL_ENTER_SLEEP_FLG);
								NVIC_SystemReset();
								break;
							case 0x05:
								gSystemSt_t.mSetMarkID = pRxData[1] & 0x7f;
								gSystemCfg_t.mDataCfgSet.mMarkID = gSystemSt_t.mSetMarkID;
								FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
								advertising_updata(0xF000 | gSystemSt_t.mSetMarkID,gSystemCfg_t.mDataCfgSet.mDeviceTYpe);
								NRF_LOG_INFO("set Mark ID=%x",gSystemSt_t.mSetMarkID );
								break;
							default:
								aTxAck_t.mData[0] = MSG_ACK_ST_NO_AUTH;
								break;
						}
                    }
                    else
                    {
                        aTxAck_t.mData[0] = MSG_ACK_ST_NO_AUTH;
                    }
                    Imu_10msTimer_Enable(1);
                }
                break;
                case MSG_CODE_SYS_CFG_READ:
                {
                    aTxAck_t.mMsgLen  = 3u;
                    aTxAck_t.mData[0u] = MSG_ACK_ST_SUCC;

                    if(pRxData[0] == 0x00) // production SN
                    {
                        memcpy(&(aTxAck_t.mData[1]),gSystemCfg_t.mDataCfgSet.mProdSN.mdata,gSystemCfg_t.mDataCfgSet.mProdSN.mLen);
                        aTxAck_t.mMsgLen  += gSystemCfg_t.mDataCfgSet.mProdSN.mLen;
                        DEBUG_PRINTF("read product sn\r\n");
                    }
                    else if(pRxData[0] == 0x01) //device name
                    {
                        memcpy(&(aTxAck_t.mData[1]),gSystemCfg_t.mDataCfgSet.mDeviceName.mdata,gSystemCfg_t.mDataCfgSet.mDeviceName.mLen);
                        aTxAck_t.mMsgLen  += gSystemCfg_t.mDataCfgSet.mDeviceName.mLen;
                        DEBUG_PRINTF("read device name\r\n");
                    }
                    else if(pRxData[0] == 0x02) //model name
                    {
                        memcpy(&(aTxAck_t.mData[1]),gSystemCfg_t.mDataCfgSet.mModelName.mdata,gSystemCfg_t.mDataCfgSet.mModelName.mLen);
                        aTxAck_t.mMsgLen  += gSystemCfg_t.mDataCfgSet.mModelName.mLen;
                        DEBUG_PRINTF("read model name\r\n");
                    }
					else if(pRxData[0] == 0x04)
					{
						memcpy(&aTxAck_t.mData[1],gSystemCfg_t.mProducInfo.ProdBtMac,6u);
						aTxAck_t.mMsgLen  += 6u;
					}
                    else if(pRxData[0] == 0x05)
                    {
						aTxAck_t.mData[1u] = gSystemSt_t.mSetMarkID;
						aTxAck_t.mMsgLen  += 1u;
                    }
                    else
                    {
                        aTxAck_t.mData[0] = MSG_ACK_ST_FAIL;
                    }
                }
                break;
                case MSG_CODE_RF_CARRIER_CTRL:
                {
                    aTxAck_t.mMsgLen  = 3u;
                    if(gSysMode == SYS_RUN_MODEL_MANAGE && gSystemSt_t.mDeviceSt_uni.Bits.mSysModel == SYS_MODE_PCBA_TEST)
                    {
                        aTxAck_t.mData[0u] = MSG_ACK_ST_SUCC;
                        if(pRxData[0] == 0x00)
                        {
                            radio_disable();
                        }
                        else if (pRxData[1] <= 83)
                        {
                            (void)sd_softdevice_disable();
							radio_disable();
                            init_rfclk();
                            radio_tx_carrier(RADIO_TXPOWER_TXPOWER_0dBm, RADIO_MODE_MODE_Nrf_1Mbit, pRxData[1]);
                            DEBUG_PRINTF("set rf radio %u carrier send", pRxData[1]);
                        }
                    }
                    else
                    {
                        aTxAck_t.mData[0u] = MSG_ACK_ST_FAIL;
                    }
                }
                break;
                case MSG_CODE_TEST_MODE_CTRL:
                {
                    aTxAck_t.mMsgLen  = 3u;
                    aTxAck_t.mData[0u] = MSG_ACK_ST_SUCC;
                    if (gSysMode == SYS_RUN_MODEL_MANAGE)
                    {
						gSystemSt_t.mFaultSt_uni.Bits.mTrigHallSt = SYS_FAULT_ST_ERR;
                        gSystemSt_t.mDeviceSt_uni.Bits.mSysModel = SYS_MODE_PCBA_TEST;
                        LedClose(LED_CTRL_SELECT_0);
                        LedClose(LED_CTRL_SELECT_1);
                        enable_Imu_inter(0); //disable
                        enable_Tp_inter(0); //disable
                        enable_BulletLoaded_inter(0);   //disable
                        app_timer_stop_all();
//                        CRITICAL_REGION_ENTER();
//                        sd_softdevice_disable();
//                        CRITICAL_REGION_EXIT();
//                        init_rfclk();
                        NRF_LOG_INFO("go to PCBA teset model\n")
                    }
                    else
                    {
                        aTxAck_t.mData[0u] = MSG_ACK_ST_CMD_ERR;
                    }
                }
                break;
                case MSG_CODE_PWR_STATUS_CTRL:
                {
                    aTxAck_t.mMsgLen  = 3u;
                    if (gSysMode == SYS_RUN_MODEL_MANAGE)
                    {
                        if(pRxData[0] == 0x01)
                        {
                            aTxAck_t.mData[0u] = MSG_ACK_ST_SUCC;
							(void)sd_softdevice_disable();
							radio_disable();
                            init_rfclk();
                            radio_tx_carrier(RADIO_TXPOWER_TXPOWER_0dBm, RADIO_MODE_MODE_Nrf_2Mbit, 02);
                            LedLighten(LED_CTRL_SELECT_0);
                            LedLighten(LED_CTRL_SELECT_1); 
//                            HallPwrCtrl(1);
//                            ImuPwrCtrl(1);
                            NRF_LOG_INFO("go to PCBA active model\n")
                        }
                        else if(pRxData[0] == 0x02)
                        {
                            aTxAck_t.mData[0u] = MSG_ACK_ST_SUCC;
//                            radio_disable();
                            LedClose(LED_CTRL_SELECT_0);
                            LedClose(LED_CTRL_SELECT_1); 
//                            HallPwrCtrl(0);
//                            ImuPwrCtrl(0);
                            NRF_LOG_INFO("go to PCBA idle model\n")
                        }
                        else
                        {
                            aTxAck_t.mData[0u] = MSG_ACK_ST_CMD_ERR;
                        }
                    }
                    else
                    {
                        aTxAck_t.mData[0u] = MSG_ACK_ST_CMD_ERR;
                    }
                }
                break;
                default:
                {
                    aTxAck_t.mMsgLen  = 3u;
                    aTxAck_t.mData[0] = MSG_ACK_ST_CMD_ERR;
                }
                break;
            }
        }
        else
        {
            aTxAck_t.mMsgLen  = 3u;
            aTxAck_t.mData[0] = MSG_ACK_ST_CMD_ERR;
        }
    }
    else
    {
        DEBUG_PRINTF("Msg check sum fail\r\n");
        aTxAck_t.mMsgLen  = 3u;
        aTxAck_t.mData[0] = MSG_ACK_ST_CHECK_ERR;
    }
    CheckOrCalcSum((uint8_t*)(&aTxAck_t),aTxAck_t.mMsgLen + 4u,&(aTxAck_t.mData[aTxAck_t.mMsgLen-2]),PACKET_SUM_CALC);
//   (void)UsbSendCmdAck((uint8_t*)(&aTxAck_t),aTxAck_t.mMsgLen + 6u); 
		(void)UsbSendCmdAck((uint8_t*)(&aTxAck_t),aTxAck_t.mMsgLen + 10u); 
}
static void GunBulletNumLoss(uint8_t aTrigSt)
{
    // static uint8_t iTrigStLast = uart_cmd_trig_off;
    // #if SYS_CFG_92G_DEVICE == FUN_EN
    // if (iTrigStLast == uart_cmd_trig_off && aTrigSt == uart_cmd_trig_on)
    // #else
    // if ((iTrigStLast == uart_cmd_trig_off && aTrigSt == uart_cmd_trig_on)
    // ||(gSystemSt_t.mButtonSt_t.mDSW_t.bits.Speed_Govern1_bit == SYS_CTRL_EN))
    // #endif
    {
        gSystemSt_t.Gun_t.mBulletNumRemain =  gSystemSt_t.Gun_t.mBulletNumRemain>0?gSystemSt_t.Gun_t.mBulletNumRemain-1u:0u;
        if (gSystemSt_t.Gun_t.mBulletNumRemain == 0)
        {
            UartSendCmd(uart_cmd_not_allowed_shot, NULL, 0u);
        }
    }
    // iTrigStLast = aTrigSt;
}
static void GunHeartbeatAck()
{
    UartSendCmd(uart_cmd_heartbeat_reply, NULL, 0u);
}

static void batPerProcess(uint8_t aBatPer)
{
	static uint8_t batPerLast[2] = {100, 100};
	static uint8_t batPerCnt[2];
	static uint8_t switchCnt[2];
	if((gSystemSt_t.mButtonSt_t.mDSW_t.bits.Hand_right_bit == SYS_CTRL_EN) 
		&& (switchCnt[0]++>=SYS_GUN_BAT_SWITCH_RELOAD_CNT_MAX))
	{
		switchCnt[1] = 0x00;
		switchCnt[0] = SYS_GUN_BAT_SWITCH_RELOAD_CNT_MAX;
		if(aBatPer > batPerLast[0])
		{
			if(batPerCnt[0]++ >= SYS_GUN_BAT_PER_RELOAD_CNT_MAX)
			{
				batPerCnt[0] = 0x00;
				batPerLast[0] = aBatPer;
			}
		}
		else
		{
			batPerCnt[0] = 0x00;
			batPerLast[0] = aBatPer;
		}
		gSystemSt_t.mBatPercent = batPerLast[0];
	}
	else if(switchCnt[1]++>=SYS_GUN_BAT_SWITCH_RELOAD_CNT_MAX)
	{
		switchCnt[1] = SYS_GUN_BAT_SWITCH_RELOAD_CNT_MAX;
		switchCnt[0] = 0x00;
		if(aBatPer > batPerLast[1])
		{
			if(batPerCnt[1]++ >= SYS_GUN_BAT_PER_RELOAD_CNT_MAX)
			{
				batPerCnt[1] = 0x00;
				batPerLast[1] = aBatPer;
			}
		}
		else
		{
			batPerCnt[1] = 0x00;
			batPerLast[1] = aBatPer;
		}
		gSystemSt_t.mBatPercent = batPerLast[1];
	}
	
}
static void UartPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen)
{
	
    NRF_LOG_INFO("0x%x\t0x%x\t0x%x\t0x%x\t0x%x\t",*(apCmdMsg+1),*(apCmdMsg+2),*(apCmdMsg+3), *(apCmdMsg+4), *(apCmdMsg+5));
    gSystemSt_t.Gun_t.mConnCountDown = SYS_GUN_CONN_COUNT_DOWN_MAX;
	  UartParamSet(apCmdMsg[1], apCmdMsg[0], apCmdMsg[2], apCmdMsg[3]);
    switch (*(apCmdMsg+4))
    {
        case uart_cmd_heart_beat:
        {
            gSystemSt_t.mFaultSt_uni.Bits.mGunConnSt = SYS_FAULT_ST_OK;
					  GunHeartbeatAck();
            break;
        }
        case uart_cmd_trig_on:
        {
			TEST_PIN_HIGH();
            gSystemSt_t.mButtonSt_t.mTrig = TRIGGER_MAX_VAL;
            GunBulletNumLoss(uart_cmd_trig_on);
            break;
        }
        case uart_cmd_trig_off:
        {
			TEST_PIN_LOW();
            gSystemSt_t.mButtonSt_t.mTrig = TRIGGER_MIN_VAL;
            // GunBulletNumLoss(uart_cmd_trig_off);
            break;
        }
        case uart_cmd_bullet_load:
        {
			TEST_PIN_HIGH();
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.TouchPad_bit = SYS_CTRL_EN;        //single
            gSystemSt_t.Gun_t.mTrigSendCountDonw = SYS_GUN_TRIG_COUNT_DOWN_MAX;
            if ((gSystemSt_t.Gun_t.mChangeMagazineClipState == gun_wait_bullet_load)
            && (gSystemSt_t.mButtonSt_t.mDSW_t.bits.Hand_right_bit == SYS_CTRL_EN))
            {
                gSystemSt_t.Gun_t.mChangeMagazineClipState = gun_replace_magazine_clip_success;
                gSystemSt_t.Gun_t.mBulletNumRemain = gSystemSt_t.Gun_t.mBulletNumRemainBackup;
                UartSendCmd(uart_cmd_num_of_bullets_set, &gSystemSt_t.Gun_t.mBulletNumRemain, 1u);
				DEBUG_PRINTF("LOAD BULLET %d OK!", gSystemSt_t.Gun_t.mBulletNumRemain); //uart_cmd_allowed_shot
				UartSendCmd(uart_cmd_allowed_shot, NULL, 0u);
            }
            else 
            {
								if (gSystemSt_t.Gun_t.mBulletNumRemain > 0x00)
								{
									gSystemSt_t.Gun_t.mBulletNumRemain--;
								}
								if(gSystemSt_t.Gun_t.mBulletNumRemain == 0x00)
								{
									UartSendCmd(uart_cmd_not_allowed_shot, NULL, 0u);
								}
            }
            break;
        }
        case uart_cmd_magazine_clip_load:
        {
			TEST_PIN_HIGH();
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Hand_right_bit = SYS_CTRL_EN;
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Hand_left_bit = SYS_CTRL_DIS;
            break;
        }
        case uart_cmd_magazine_clip_release:
        {
			TEST_PIN_HIGH();
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Hand_right_bit = SYS_CTRL_DIS; 
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Hand_left_bit = SYS_CTRL_EN;
			gSystemSt_t.Gun_t.mBulletNumRemain = gSystemSt_t.Gun_t.mBulletNumRemain==0?0:1;
            break;
        }
        case uart_cmd_status_safety:
        {
			TEST_PIN_HIGH();
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Speed_Govern0_bit = SYS_CTRL_DIS;
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Speed_Govern1_bit = SYS_CTRL_DIS;
            break;
        }
        case uart_cmd_status_single_shot:
        {
			TEST_PIN_HIGH();
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Speed_Govern0_bit = SYS_CTRL_EN; 
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Speed_Govern1_bit = SYS_CTRL_DIS;
            break;
        }
        case uart_cmd_status_continuous_shot:
        {
			TEST_PIN_HIGH();
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Speed_Govern0_bit = SYS_CTRL_DIS;
            gSystemSt_t.mButtonSt_t.mDSW_t.bits.Speed_Govern1_bit = SYS_CTRL_EN;
            break;
        }
        case uart_cmd_bat_per_reply:
        {
			TEST_PIN_LOW();
			if(gSystemSt_t.mButtonSt_t.mDSW_t.bits.Hand_right_bit == SYS_CTRL_EN)
			{
				batPerProcess((*(apCmdMsg+5))>100u?100u:(*(apCmdMsg+5)));
			}
			else
			{
				batPerProcess((*(apCmdMsg+6))>100u?100u:(*(apCmdMsg+6)));
			}
//			if(*(apCmdMsg+5) == 0x64)
//			{
//				gSystemSt_t.mBatPercent = 101;
//			}
//			else
//			{
//				gSystemSt_t.mBatPercent = *(apCmdMsg+6);
//			}
//			NRF_LOG_INFO("0x%x\t0x%x\t0x%x\t0x%x\t0x%x\t0x%x\t",*(apCmdMsg+2),*(apCmdMsg+3),*(apCmdMsg+4), *(apCmdMsg+5), *(apCmdMsg+6), *(apCmdMsg+7));
//			NRF_LOG_INFO("Bat:%d", gSystemSt_t.mBatPercent);
            break;
        }
    default:
        break;
    }
}

static void BleNorPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen)
{
    nrf_dfu_adv_name_t   m_adv_name;
    SingleFrame_Protocol_Rx_t SingleFrameCmd_t;
    
    if((apCmdMsg[0] & SYS_PROTOCOL_NEW_FLAG) == 0x00) //old protocol
    {
        memcpy(SingleFrameCmd_t.buffer,apCmdMsg,sizeof(SingleFrameCmd_t.buffer));
        NRF_LOG_INFO("old protocol:0x%02X, command:0x%02X", SingleFrameCmd_t.content.command, SingleFrameCmd_t.content.subtype);
        switch(SingleFrameCmd_t.content.command)
        {
            case cmd_type_set_notify_type:
            {
                if(gSystemSt_t.BlePipeNotifyEn_t.DataPipe)
                {
                    switch(SingleFrameCmd_t.content.subtype)
                    {
                        case Notify_SubCmd_none :
                            NRF_LOG_INFO("None type");
                            gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                            break;
                        case Notify_SubCmd_ypr :
                            NRF_LOG_INFO("enable with ypr");
                            gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                            gSystemSt_t.mOldImuPipeType = data_type_ypr;
                            break;
                        case Notify_SubCmd_acc_and_gyro:
                            NRF_LOG_INFO("enable with acc and ypr");
                            gSystemSt_t.mOldImuPipeType = data_type_acc_and_gyro;
                            break;
                        case Notify_SubCmd_q4 :
                            NRF_LOG_INFO("enable with q4");
                            gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                            gSystemSt_t.mOldImuPipeType = data_type_q4;
                            break;
                        case Notify_SubCmd_trig_grip_raw:
                            NRF_LOG_INFO("enable with trig grip raw");
                            gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                            gSystemSt_t.mOldImuPipeType = data_type_trig_grip_raw;
                            break;
                        case Notify_SubCmd_ypr_accel_gyro :
                            NRF_LOG_INFO("enable with type_ypr_accel_gyro");
                            gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                            gSystemSt_t.mOldImuPipeType = data_type_ypr_accel_gyro;
                            break;
                        case Notify_SubCmd_TLY:
                            NRF_LOG_INFO("enable with TLV type");
                            gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x01;
                            break;
                        default:
                            break;
                    }
                }
                else
                {
                    //do nothing
                }
            }
            break;
            case cmd_type_bsp:
            {
                switch(SingleFrameCmd_t.content.subtype)
                {
                    case Bsp_SubCmd_motor_off:
                    {
                        MotorPwmPwrCtrl(0,0,0,0);
                        NRF_LOG_INFO("close motor");
                        break;
                    }
                    case Bsp_SubCmd_motor_on: 
                    {
                        MotorPwmPwrCtrl(0,0,0,0);
                        uint8_t duty = SingleFrameCmd_t.content.data[0];
                        uint16_t pulse_wide_us = SingleFrameCmd_t.content.data[1];
                                 pulse_wide_us = (pulse_wide_us << 8u) + SingleFrameCmd_t.content.data[2];
                        uint32_t timeout = SingleFrameCmd_t.content.data[3];
                                 timeout = (timeout << 8u) + SingleFrameCmd_t.content.data[4];
                                timeout = (timeout << 8u) + SingleFrameCmd_t.content.data[5];
                                timeout = (timeout << 8u) + SingleFrameCmd_t.content.data[6];
                        if(duty == 0)
                        {
                            duty = gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mDuty;
                        }
                        else if(duty > 100)
                        {
                            duty = 100;
                        }
                        if((pulse_wide_us < 50) || (pulse_wide_us > 50000))
                        {
                            pulse_wide_us = gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mPeriod;
                        }
                        if((timeout != 0) && ((timeout < 50) || (timeout > 180000)))
                        {
                            timeout = gSystemCfg_t.mDataCfgSet.mMotorPwmDefCfg_t.mTimeout;
                        }
                        NRF_LOG_INFO("duty =%d, pulse_wide_us:%d : timeout_ms:%d",duty, pulse_wide_us,timeout);
                        MotorPwmPwrCtrl(1,duty,pulse_wide_us,timeout);
                        break;
                    }
                    default:
                       break;
                }
                break;
            }
            case cmd_type_ble:
            {
                switch(SingleFrameCmd_t.content.subtype)
                {
                    case Ble_SubCmd_all_service:
                        if(!gSystemSt_t.isAllServiceEnabled)
                        {
                            //Ble_xim_OldCalib_init(BtXimServiceEvtCallBack);
                            //advertising_updata(0xF100 | gSystemSt_t.mSetMarkID,gSystemCfg_t.mDataCfgSet.mDeviceTYpe);
                            //sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
                        }
                        break;
                    case Ble_SubCmd_reboot:
                        Ble_Dfu_Bootloader_enter_prepare();
                        (void)sd_power_gpregret_clr(0, 0xffffffff);
                        (void)sd_power_gpregret_set(0, PWR_CTRL_ENTER_SLEEP_FLG);
                        NVIC_SystemReset();
                        break;
                    case Ble_SubCmd_shutdown:
                        NRF_LOG_INFO("goto shutdown");
                        break;
                    case Ble_SubCmd_sleep:
                        NRF_LOG_INFO("goto sleep");
                        break;
                    case Ble_SubCmd_wakeup:
                        NRF_LOG_INFO("goto wakeup");
                        break;
                    case Ble_SubCmd_enter_dfu:
                        Imu_10msTimer_Enable(0);
                        memset(&m_adv_name,0xff,sizeof(m_adv_name));
                        memcpy(m_adv_name.name, gSystemCfg_t.mDataCfgSet.mDfuName.mdata, gSystemCfg_t.mDataCfgSet.mDfuName.mLen-1u);
                        m_adv_name.len = gSystemCfg_t.mDataCfgSet.mDfuName.mLen-1u;
                        Ble_Dfu_Bootloader_enter_prepare();
                        set_BootAdv_name(&m_adv_name);
                        nrf_delay_ms(10);
                        ble_dfu_bootloader_start_finalize();
                        break;
                    default:
                       break;
                }
                break;
            }
            case cmd_type_manufature:
            {
                Imu_10msTimer_Enable(0);
                switch(SingleFrameCmd_t.content.subtype)
                {
                    case Manufature_SubCmd_setsn:     //PCBA SN
                        SingleFrameCmd_t.content.data[17] = '\0';
                        memcpy(gSystemCfg_t.mDataCfgSet.mProdSN.mdata,SingleFrameCmd_t.content.data,strlen((char*)SingleFrameCmd_t.content.data));
                        gSystemCfg_t.mDataCfgSet.mProdSN.mLen = strlen((char*)SingleFrameCmd_t.content.data);
                        gSystemCfg_t.mDataCfgSet.mProdSN.mdata[gSystemCfg_t.mDataCfgSet.mProdSN.mLen] = '\0';
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        break;
                    case Manufature_SubCmd_setmac:  //Mac Msb
                        for(uint8_t i = 0; i < 6u; i++)
                        {
                            gSystemCfg_t.mDataCfgSet.mLocalMac_t.mMac[i] &= SingleFrameCmd_t.content.data[6+i];
                            gSystemCfg_t.mDataCfgSet.mLocalMac_t.mMac[i] |= SingleFrameCmd_t.content.data[12+i];
                        }
                        gSystemCfg_t.mDataCfgSet.mLocalMac_t.mEnable = 0x01;
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        if(SingleFrameCmd_t.content.data[0] == 0x01)
                        {
                            Ble_Dfu_Bootloader_enter_prepare();
                            (void)sd_power_gpregret_clr(0, 0xffffffff);
                            (void)sd_power_gpregret_set(0, PWR_CTRL_ENTER_SLEEP_FLG);
                            NVIC_SystemReset();
                        }
                        break;
                    case Manufature_SubCmd_setdevicename:
                        SingleFrameCmd_t.content.data[16] = '\0';  //name max lenght 16byte
                        memcpy(gSystemCfg_t.mDataCfgSet.mDeviceName.mdata,SingleFrameCmd_t.content.data,strlen((char*)SingleFrameCmd_t.content.data));
                        gSystemCfg_t.mDataCfgSet.mDeviceName.mLen = strlen((char*)SingleFrameCmd_t.content.data);
                        gSystemCfg_t.mDataCfgSet.mDeviceName.mdata[gSystemCfg_t.mDataCfgSet.mDeviceName.mLen] = '\0';
                        if(SingleFrameCmd_t.content.data[17] != 0)
                        {
                            gSystemCfg_t.mDataCfgSet.isSuffixEnabled = SYS_CTRL_EN;
                        }
                        else
                        {
                            gSystemCfg_t.mDataCfgSet.isSuffixEnabled = SYS_CTRL_DIS;
                        }
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        break;
                    case Manufature_SubCmd_setmodelname:   //product SN
                        SingleFrameCmd_t.content.data[17] = '\0';
                        memcpy(gSystemCfg_t.mDataCfgSet.mModelName.mdata,SingleFrameCmd_t.content.data,strlen((char*)SingleFrameCmd_t.content.data));
                        gSystemCfg_t.mDataCfgSet.mModelName.mLen = strlen((char*)SingleFrameCmd_t.content.data);
                        gSystemCfg_t.mDataCfgSet.mModelName.mdata[gSystemCfg_t.mDataCfgSet.mModelName.mLen] = '\0';
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        break;
                    case Manufature_SubCmd_setTrigSwJudg: //set trigger swtich adc judge
                        
                        ((uint8_t *)&(gSystemCfg_t.mDataCfgSet.mTrigSwCalibLow))[1] = SingleFrameCmd_t.content.data[0];
                        ((uint8_t *)&(gSystemCfg_t.mDataCfgSet.mTrigSwCalibLow))[0] = SingleFrameCmd_t.content.data[1];
						
                        ((uint8_t *)&(gSystemCfg_t.mDataCfgSet.mTrigSwCalibHigh))[1] = SingleFrameCmd_t.content.data[2];
                        ((uint8_t *)&(gSystemCfg_t.mDataCfgSet.mTrigSwCalibHigh))[0] = SingleFrameCmd_t.content.data[3];

						((uint8_t *)&(gSystemCfg_t.mDataCfgSet.mTrigSwCalibRelease))[1] = SingleFrameCmd_t.content.data[4];
                        ((uint8_t *)&(gSystemCfg_t.mDataCfgSet.mTrigSwCalibRelease))[0] = SingleFrameCmd_t.content.data[5];
						if((gSystemCfg_t.mDataCfgSet.mTrigSwCalibRelease > gSystemCfg_t.mDataCfgSet.mTrigSwCalibLow)
						 &&(gSystemCfg_t.mDataCfgSet.mTrigSwCalibHigh > gSystemCfg_t.mDataCfgSet.mTrigSwCalibLow))
						{
							gSystemCfg_t.mDataCfgSet.mTrigSwCalibRelease = (uint16_t)((gSystemCfg_t.mDataCfgSet.mTrigSwCalibRelease-gSystemCfg_t.mDataCfgSet.mTrigSwCalibLow)*255.0f/	\
																					  (gSystemCfg_t.mDataCfgSet.mTrigSwCalibHigh - gSystemCfg_t.mDataCfgSet.mTrigSwCalibLow))+10u;
						}
						
					
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        
                        NRF_LOG_INFO("rx trig judge:%x,%x,%x,%x",SingleFrameCmd_t.content.data[0],SingleFrameCmd_t.content.data[1],SingleFrameCmd_t.content.data[2],SingleFrameCmd_t.content.data[3]);
                        break;
                    case Manufature_SubCmd_change_adv_type:
                    {
                        gSystemCfg_t.mDataCfgSet.mDeviceTYpe[0] = SingleFrameCmd_t.content.data[0];
                        gSystemCfg_t.mDataCfgSet.mDeviceTYpe[1] = SingleFrameCmd_t.content.data[1];
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        NRF_LOG_INFO("change Type:%x,%x",gSystemCfg_t.mDataCfgSet.mDeviceTYpe[0],gSystemCfg_t.mDataCfgSet.mDeviceTYpe[1]);
                        break;
                    }
                    case Manufature_SubCmd_setRFRadio2440MHz:
                        
                        gSystemSt_t.mRfCarrierEn = SYS_CTRL_EN;
                        (void)sd_softdevice_disable();
                        init_rfclk();
                        radio_tx_carrier(RADIO_TXPOWER_TXPOWER_0dBm, RADIO_MODE_MODE_Nrf_1Mbit, 40);
                        NRF_LOG_INFO("set rf radio 2440 carrier send");
                        break;
                    case Manufature_SubCmd_resetIMUMagCalib:
                        gSystemCfg_t.mImuCalibSet = gcSysDefaultImuCalibSet_t;
                        FlashDataWrite(DS_DATA_TYPE_D1,0,(uint8_t *)(&gSystemCfg_t.mImuCalibSet),sizeof(gSystemCfg_t.mImuCalibSet),DS_DATA_OPERATE_BACKUP_EN);
                        NRF_LOG_INFO("reset imu calibset ");
                        break;
                    case Manufature_SubCmd_enter_aging:
                    {
                        uint32_t timeout = SingleFrameCmd_t.content.data[1];
                                 timeout = (timeout << 8u) + SingleFrameCmd_t.content.data[2];
                                 timeout = (timeout << 8u) + SingleFrameCmd_t.content.data[3];
                                 timeout = (timeout << 8u) + SingleFrameCmd_t.content.data[4];
                        NRF_LOG_INFO("set aging test: enable Timeout =%x,timeout_s=%d",SingleFrameCmd_t.content.data[0],timeout);
                        break;
                    }
                    default:break;
                }
                Imu_10msTimer_Enable(1);
                break;
            }
            case cmd_type_blobs:
            {
                if((SingleFrameCmd_t.content.subtype & 0x80) == 0x80) 
                {
                    if(SingleFrameCmd_t.content.data[0] == 0x00) //change ID  don't save
                    {
                        gSystemSt_t.mSetMarkID = SingleFrameCmd_t.content.subtype & 0x7f;
                        
                    }
                    else
                    {
                        Imu_10msTimer_Enable(0);
                        gSystemSt_t.mSetMarkID = SingleFrameCmd_t.content.subtype & 0x7f;
                        gSystemCfg_t.mDataCfgSet.mMarkID = SingleFrameCmd_t.content.subtype & 0x7f;
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        Imu_10msTimer_Enable(1);
                    }
                    advertising_updata(0xF000 | gSystemSt_t.mSetMarkID,gSystemCfg_t.mDataCfgSet.mDeviceTYpe);
                    NRF_LOG_INFO("set Mark ID=%x",gSystemSt_t.mSetMarkID );
                }
                else 
                {
                    //do nothing
                }
                break;
            }
            case cmd_type_gun:
            {
                switch (SingleFrameCmd_t.content.subtype)
                {
                    case Gun_SubCmd_Temp_Bullet_Num_Set:
                    {
                        if (0 != gSystemSt_t.Gun_t.mBulletNumRemain)
                        {
                            gSystemSt_t.Gun_t.mChangeMagazineClipState = gun_replace_magazine_clip_success;
                            gSystemSt_t.Gun_t.mBulletNumRemain = SingleFrameCmd_t.content.data[0]+1;
                            gSystemSt_t.Gun_t.mBulletNumRemainBackup = 0;
                            UartSendCmd(uart_cmd_num_of_bullets_set, &gSystemSt_t.Gun_t.mBulletNumRemain, 1u);
							UartSendCmd(uart_cmd_allowed_shot, NULL, 0u);
                        }
                        else
                        {
                            gSystemSt_t.Gun_t.mBulletNumRemainBackup = SingleFrameCmd_t.content.data[0];
                            gSystemSt_t.Gun_t.mChangeMagazineClipState = gun_wait_bullet_load;
														NRF_LOG_INFO("!!!wait Temp Bullet load");
                        }
                        NRF_LOG_INFO("set Temp Bullet Number=%u", gSystemSt_t.Gun_t.mBulletNumRemain);
                        break;
                    }
                    case Gun_SubCmd_Perpetual_Bullet_Num_Set:
                        Imu_10msTimer_Enable(0);
                        gSystemCfg_t.mDataCfgSet.mBulletNumPrefab = SingleFrameCmd_t.content.data[0];
                        gSystemSt_t.Gun_t.mBulletNumPrefab = gSystemCfg_t.mDataCfgSet.mBulletNumPrefab;
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        NRF_LOG_INFO("set Perpetual Bullet Number=%u", gSystemCfg_t.mDataCfgSet.mBulletNumPrefab);
                        Imu_10msTimer_Enable(1);
                        break;
                    case Gun_SubCmd_Life_Time_Set:
                        Imu_10msTimer_Enable(0);
                        gSystemCfg_t.mDataCfgSet.mGunLifeTime = SingleFrameCmd_t.content.data[0]<<8|SingleFrameCmd_t.content.data[1];
                        FlashDataWrite(DS_DATA_TYPE_D0,0,(uint8_t *)(&gSystemCfg_t.mDataCfgSet),sizeof(gSystemCfg_t.mDataCfgSet),DS_DATA_OPERATE_BACKUP_EN);
                        NRF_LOG_INFO("set Life time count=%d", gSystemCfg_t.mDataCfgSet.mGunLifeTime);
                        Imu_10msTimer_Enable(1);
                        break;
                    default:
                        break;
                }
                break;
            }
            default:
            break;
        }
    }
    else
    {
        NRF_LOG_INFO("new protocol:0x%02X, command:0x%02X",apCmdMsg[0],apCmdMsg[1] );
    }
}
static void BleCfgPipeCmdProcess(uint8_t *apCmdMsg,uint16_t aMsgLen)
{
    BleCmdMsgFormat_str  rxCmd_t;
   
    rxCmd_t.cmd.byte = apCmdMsg[0];
    rxCmd_t.mMsgLen =  apCmdMsg[1];
    memcpy(rxCmd_t.mData,&apCmdMsg[2],rxCmd_t.mMsgLen);
     NRF_LOG_INFO("cfg pipe cmd len=%d,cmd =%x,data=%x\n",aMsgLen,rxCmd_t.cmd.byte,rxCmd_t.mData[0]);
    switch(rxCmd_t.cmd.bits.mMsgCode )
    {
        case 0x10:
        {
            switch(rxCmd_t.mData[0])
            {
                case Notify_SubCmd_none :
                    NRF_LOG_INFO("None type");
                    gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                    break;
                case Notify_SubCmd_ypr :
                    NRF_LOG_INFO("enable with ypr");
                   gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                    gSystemSt_t.mOldImuPipeType = data_type_ypr;
                    break;
                case Notify_SubCmd_q4 :
                    NRF_LOG_INFO("enable with q4");
                    gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                    gSystemSt_t.mOldImuPipeType = data_type_q4;
                    break;
                case Notify_SubCmd_ypr_accel_gyro :
                    NRF_LOG_INFO("enable with type_ypr_accel_gyro");
                    gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
                    gSystemSt_t.mOldImuPipeType = data_type_ypr_accel_gyro;
                    break;
								case Notify_SubCmd_trig_grip_raw:
										NRF_LOG_INFO("enable with trig grip raw");
										gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00;
										gSystemSt_t.mOldImuPipeType = data_type_trig_grip_raw;
										break;
                case Notify_SubCmd_TLY:
                    NRF_LOG_INFO("enable with TLV type");
                   gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x01;
                     break;
                default:
                    break;
            }
            break;
        }
        default:
            break;
    }
}

static void ImuDataProcess(uint8_t *apCmdMsg,uint16_t aMsgLen)    //500  < 2ms
{
    // gSystemSt_t.ImutSampleData_t.timestamp =  ((Imu_SensorSampleData_str*)(apCmdMsg))->mTimeStamp;
    // memcpy(&(gSystemSt_t.ImutSampleData_t.SensorData_t),((Imu_SensorSampleData_str*)(apCmdMsg))->mImuRxMsgDataBuf,sizeof(ICM20602SensorData_str));
    gSystemSt_t.ImutSampleData_t.ImuRawData_t.timestamp = gSystemSt_t.ImutSampleData_t.timestamp;
    
    // memcpy(&(gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel),&(gSystemSt_t.ImutSampleData_t.SensorData_t.mAcc3axisData_t),sizeof(dof9_element_t));  //accel  6byte
    // memcpy(&(gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro),&(gSystemSt_t.ImutSampleData_t.SensorData_t.mGyro3axisData_t),sizeof(dof9_element_t)); // gyro  6byte
    // memset(&(gSystemSt_t.ImutSampleData_t.ImuRawData_t.magnet),0,sizeof(dof9_element_t));
    
    // gSystemSt_t.ImutSampleData_t.ImuRawData_t = imu_fusion_calculation(&(gSystemSt_t.ImutSampleData_t.ImuRawData_t));
    // gSystemSt_t.ImutSampleData_t.ImuQ4_t = imu_get_current_quaternion();
    // gSystemSt_t.ImutSampleData_t.ImuYpr_t = imu_get_current_ypr();
}
/* public function definition */
void ImuNorPipeDataSend(void)     //<1ms
{
    uint8_t  index =0; //iSubPackageIndex = 2u;
    uint32_t time = gSystemSt_t.ImutSampleData_t.timestamp /100u;
    SingleFrame_Protocol_Tx_t  ImuSendBuf;
    BleTlvMsgFormat_str        ImuTlvSendBuf;
    memset(&ImuSendBuf,0x00,sizeof(ImuSendBuf));
    if(gSystemCfg_t.mDataCfgSet.mProtocolType ) //new protocol send
    {
        ImuTlvSendBuf.mTypeChnl = MSG_FORMAT_TYPE_DATA_EVT;
        ImuTlvSendBuf.mMsgCode = MSG_CODE_EVT;
        
        //sys st TLV
        if(gSystemSt_t.mSynTimEnable != SYS_BLE_TIME_SYN_SEND)
        {
            time = 0;
        }
        
        ImuTlvSendBuf.mData[0] =MSG_CODE_TLV_DATA_SYS_ST;
        ImuTlvSendBuf.mData[1] = 15;
        memcpy(&ImuTlvSendBuf.mData[2],&time,4u);
        ImuTlvSendBuf.mData[6] = gSystemSt_t.mSetMarkID;
        ImuTlvSendBuf.mData[7] = gerCurrentMtuSize();
        ImuTlvSendBuf.mData[8] = gSystemSt_t.mDeviceSt_uni.byte;
        ImuTlvSendBuf.mData[9] = gSystemSt_t.mFaultSt_uni.byte;
        ImuTlvSendBuf.mData[10] = gSystemSt_t.mButtonSt_t.mTrig;
        ImuTlvSendBuf.mData[11] = gSystemSt_t.mButtonSt_t.mDSW_t.byte;

//        ImuTlvSendBuf.mData[12] =  ((uint8_t *)(&gSystemSt_t.mJoyGamePad_t.Bytesxy[0]))[1u];
//        ImuTlvSendBuf.mData[13] =  ((uint8_t *)(&gSystemSt_t.mJoyGamePad_t.Bytesxy[0]))[0u];
//        ImuTlvSendBuf.mData[14] =  ((uint8_t *)(&gSystemSt_t.mJoyGamePad_t.Bytesxy[1]))[1u];
//        ImuTlvSendBuf.mData[15] =  ((uint8_t *)(&gSystemSt_t.mJoyGamePad_t.Bytesxy[1]))[0u];

//        ImuTlvSendBuf.mData[16] = gSystemSt_t.mButtonSt_t.mGrip;

        index =17;

        //Euler Tlv
        if(gSystemSt_t.PipeTlvTypeEn_t.bits.mEuler_en == SYS_CTRL_EN)
        {
            ImuTlvSendBuf.mData[index] =MSG_CODE_TLV_DATA_EULEE;
            ImuTlvSendBuf.mData[index +1u] = 12;
            memcpy(&ImuTlvSendBuf.mData[index +2u],&gSystemSt_t.ImutSampleData_t.ImuYpr_t.yaw,4u);
            memcpy(&ImuTlvSendBuf.mData[index+6u],&gSystemSt_t.ImutSampleData_t.ImuYpr_t.pitch,4u);
            memcpy(&ImuTlvSendBuf.mData[index +10u],&gSystemSt_t.ImutSampleData_t.ImuYpr_t.roll,4u);
            index += 14;
        }
        else
        {
            //do nothing
        }
        //Q4 Tlv
        if(gSystemSt_t.PipeTlvTypeEn_t.bits.mQ4_en == SYS_CTRL_EN)
        {
            ImuTlvSendBuf.mData[index] =MSG_CODE_TLV_DATA_4Q;
            ImuTlvSendBuf.mData[index +1u] = 16;
            memcpy(&ImuTlvSendBuf.mData[index +2u],&gSystemSt_t.ImutSampleData_t.ImuQ4_t.q0,4u);
            memcpy(&ImuTlvSendBuf.mData[index+6u],&gSystemSt_t.ImutSampleData_t.ImuQ4_t.q1,4u);
            memcpy(&ImuTlvSendBuf.mData[index +10u],&gSystemSt_t.ImutSampleData_t.ImuQ4_t.q2,4u);
            memcpy(&ImuTlvSendBuf.mData[index +14u],&gSystemSt_t.ImutSampleData_t.ImuQ4_t.q3,4u);
            index += 18;
        }
        else
        {
            //do nothing
        }
        //Raw calib data Tlv
        if(gSystemSt_t.PipeTlvTypeEn_t.bits.mImuRaw_en == SYS_CTRL_EN)
        {
			ImuTlvSendBuf.mData[index] =MSG_CODE_TLV_DATA_RAW_DATA_MAGENET;
            ImuTlvSendBuf.mData[index+1u] = 12;
            ImuTlvSendBuf.mData[index+2] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.x;
            ImuTlvSendBuf.mData[index+3] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.x >> 8u;
            ImuTlvSendBuf.mData[index+4] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.y;
            ImuTlvSendBuf.mData[index+5] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.y >> 8u;
            ImuTlvSendBuf.mData[index+6] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.z;
            ImuTlvSendBuf.mData[index+7] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.z >> 8u;
            
            ImuTlvSendBuf.mData[index+8] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.x;
            ImuTlvSendBuf.mData[index+9] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.x >> 8u;
            ImuTlvSendBuf.mData[index+10] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.y;
            ImuTlvSendBuf.mData[index+11] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.y >> 8u;
            ImuTlvSendBuf.mData[index+12] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.z;
            ImuTlvSendBuf.mData[index+13] = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.z >> 8u;
            
            index += 14;
        }
        else
        {
            // do nothing
        }
        //Raw sensor data Tlv
        if(gSystemSt_t.PipeTlvTypeEn_t.bits.mImuSensorData == SYS_CTRL_EN)
        {
            ImuTlvSendBuf.mData[index] =MSG_CODE_TLV_DATA_IMU_SENSOR;
            ImuTlvSendBuf.mData[index+1u] = 18;
            ImuTlvSendBuf.mData[index+2] = gSystemSt_t.ImutSampleData_t.SensorData_t.mAcc3axisData_t.mAccX;
            ImuTlvSendBuf.mData[index+3] = gSystemSt_t.ImutSampleData_t.SensorData_t.mAcc3axisData_t.mAccX >> 8u;
            ImuTlvSendBuf.mData[index+4] = gSystemSt_t.ImutSampleData_t.SensorData_t.mAcc3axisData_t.mAccY;
            ImuTlvSendBuf.mData[index+5] = gSystemSt_t.ImutSampleData_t.SensorData_t.mAcc3axisData_t.mAccY >> 8u;
            ImuTlvSendBuf.mData[index+6] = gSystemSt_t.ImutSampleData_t.SensorData_t.mAcc3axisData_t.mAccZ;
            ImuTlvSendBuf.mData[index+7] = gSystemSt_t.ImutSampleData_t.SensorData_t.mAcc3axisData_t.mAccZ >> 8u;
            
            ImuTlvSendBuf.mData[index+8] = gSystemSt_t.ImutSampleData_t.SensorData_t.mGyro3axisData_t.mGyroX;
            ImuTlvSendBuf.mData[index+9] = gSystemSt_t.ImutSampleData_t.SensorData_t.mGyro3axisData_t.mGyroX >> 8u;
            ImuTlvSendBuf.mData[index+10] = gSystemSt_t.ImutSampleData_t.SensorData_t.mGyro3axisData_t.mGyroY;
            ImuTlvSendBuf.mData[index+11] = gSystemSt_t.ImutSampleData_t.SensorData_t.mGyro3axisData_t.mGyroY >> 8u;
            ImuTlvSendBuf.mData[index+12] = gSystemSt_t.ImutSampleData_t.SensorData_t.mGyro3axisData_t.mGyroZ;
            ImuTlvSendBuf.mData[index+13] = gSystemSt_t.ImutSampleData_t.SensorData_t.mGyro3axisData_t.mGyroZ >> 8u;
            
            ImuTlvSendBuf.mData[index+14] = 0;
            ImuTlvSendBuf.mData[index+15] = 0;
            ImuTlvSendBuf.mData[index+16] = 0;
            ImuTlvSendBuf.mData[index+17] = 0;
            ImuTlvSendBuf.mData[index+18] = 0;
            ImuTlvSendBuf.mData[index+19] = 0;
            
            index += 20;
        }
        ImuTlvSendBuf.mMsgLen = index;
        CheckOrCalcSum((uint8_t *)(&ImuTlvSendBuf),ImuTlvSendBuf.mMsgLen +2u,&ImuTlvSendBuf.mData[ImuTlvSendBuf.mMsgLen],PACKET_SUM_CALC);

        if(
            (gSystemSt_t.mDeviceSt_uni.Bits.mBleConnect == SYS_BLE_CONN_ST_CONN)
            &&(gSystemSt_t.BlePipeNotifyEn_t.DataPipe  == SYS_CTRL_EN)
            && (gSystemSt_t.mRfCarrierEn == SYS_CTRL_DIS)
            &&(BleMutilPacketTx(BLE_BASE_UUID_IMU_NOR,(uint8_t *)(&ImuTlvSendBuf),ImuTlvSendBuf.mMsgLen +3u)==PACKET_OPERATE_SUCCESS)
        )
        {
            gSystemSt_t.mImuSendCnt++;
        }
    }
    else
    {
        if(gSystemSt_t.mOldImuPipeType == data_type_ypr)
        {
            ImuSendBuf.Euler_0x01_str.type = data_type_ypr;
            ImuSendBuf.Euler_0x01_str.id =  gSystemSt_t.mSetMarkID;
            
            convert_from_float_to_protocol_uint24_t_ypr(gSystemSt_t.ImutSampleData_t.ImuYpr_t.yaw,&(ImuSendBuf.Euler_0x01_str.yaw));
            convert_from_float_to_protocol_uint24_t_ypr(gSystemSt_t.ImutSampleData_t.ImuYpr_t.pitch,&(ImuSendBuf.Euler_0x01_str.pitch));
            convert_from_float_to_protocol_uint24_t_ypr(gSystemSt_t.ImutSampleData_t.ImuYpr_t.roll,&(ImuSendBuf.Euler_0x01_str.roll));
            ImuSendBuf.Euler_0x01_str.cnt.lastByte = gSystemSt_t.mImuSendHz;
            ImuSendBuf.Euler_0x01_str.trigger = gSystemSt_t.mButtonSt_t.mTrig;
            ImuSendBuf.Euler_0x01_str.IoKeys_uni.byte = gSystemSt_t.mButtonSt_t.mDSW_t.byte;
            ImuSendBuf.Euler_0x01_str.gamepad_t.bits.Event = gSystemSt_t.mTpSampleData_t.atcive_event;
            ImuSendBuf.Euler_0x01_str.gamepad_t.bits.Axis_x = gSystemSt_t.mTpSampleData_t.x;
            ImuSendBuf.Euler_0x01_str.gamepad_t.bits.Axis_y = gSystemSt_t.mTpSampleData_t.y;
            convert_from_uint16_t_to_protocol(ImuSendBuf.Euler_0x01_str.gamepad_t.HalfWordxy[0],&ImuSendBuf.Euler_0x01_str.gamepad_t.Bytesxy[0]);
            convert_from_uint16_t_to_protocol(ImuSendBuf.Euler_0x01_str.gamepad_t.HalfWordxy[1],&ImuSendBuf.Euler_0x01_str.gamepad_t.Bytesxy[1]);
        }
        else if(gSystemSt_t.mOldImuPipeType == data_type_q4)
        {
            ImuSendBuf.Q4_0x02_str.type = data_type_q4;
            ImuSendBuf.Q4_0x02_str.id =  gSystemSt_t.mSetMarkID;
            convert_from_float_to_protocol_uint24_t_q4(gSystemSt_t.ImutSampleData_t.ImuQ4_t.q0,&(ImuSendBuf.Q4_0x02_str.q0));
            convert_from_float_to_protocol_uint24_t_q4(gSystemSt_t.ImutSampleData_t.ImuQ4_t.q1,&(ImuSendBuf.Q4_0x02_str.q1));
            convert_from_float_to_protocol_uint24_t_q4(gSystemSt_t.ImutSampleData_t.ImuQ4_t.q2,&(ImuSendBuf.Q4_0x02_str.q2));
            convert_from_float_to_protocol_uint24_t_q4(gSystemSt_t.ImutSampleData_t.ImuQ4_t.q3,&(ImuSendBuf.Q4_0x02_str.q3));
            
            ImuSendBuf.Q4_0x02_str.trigger = gSystemSt_t.mButtonSt_t.mTrig;
            ImuSendBuf.Q4_0x02_str.IoKeys_uni.byte = gSystemSt_t.mButtonSt_t.mDSW_t.byte;
            ImuSendBuf.Q4_0x02_str.gamepad_t.bits.Event = gSystemSt_t.mTpSampleData_t.atcive_event;
            ImuSendBuf.Q4_0x02_str.gamepad_t.bits.Axis_x =gSystemSt_t.mTpSampleData_t.x;
            ImuSendBuf.Q4_0x02_str.gamepad_t.bits.Axis_y = gSystemSt_t.mTpSampleData_t.y;  
            convert_from_uint16_t_to_protocol(ImuSendBuf.Q4_0x02_str.gamepad_t.HalfWordxy[0],&ImuSendBuf.Q4_0x02_str.gamepad_t.xy[0]);
            convert_from_uint16_t_to_protocol(ImuSendBuf.Q4_0x02_str.gamepad_t.HalfWordxy[1],&ImuSendBuf.Q4_0x02_str.gamepad_t.xy[1]);
        }
        else if(gSystemSt_t.mOldImuPipeType == data_type_acc_and_gyro)
        {
            ImuSendBuf.Euler_0x03_str.type = data_type_acc_and_gyro;

            ImuSendBuf.Euler_0x03_str.Acc_x = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.x;
            ImuSendBuf.Euler_0x03_str.Acc_y = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.y;
            ImuSendBuf.Euler_0x03_str.Acc_z = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.z; 

            ImuSendBuf.Euler_0x03_str.Gyro_x = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.x;
            ImuSendBuf.Euler_0x03_str.Gyro_y = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.y;
            ImuSendBuf.Euler_0x03_str.Gyro_z = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.z;   

            ImuSendBuf.Euler_0x03_str.mGrip = 0;
            ImuSendBuf.Euler_0x03_str.mTrig = gSystemSt_t.mButtonSt_t.mTrig;

            ImuSendBuf.Euler_0x03_str.IoKeys_uni.byte = gSystemSt_t.mButtonSt_t.mDSW_t.byte;

            ImuSendBuf.Euler_0x03_str.gamepad_t.bits.Event = 0;//gSystemSt_t.mTpSampleData_t.atcive_event;
            ImuSendBuf.Euler_0x03_str.gamepad_t.bits.RockerX_flg = 0;
            ImuSendBuf.Euler_0x03_str.gamepad_t.bits.Axis_x = 0;
            ImuSendBuf.Euler_0x03_str.gamepad_t.bits.Axis_y = 0;
            convert_from_uint16_t_to_protocol(ImuSendBuf.Euler_0x03_str.gamepad_t.HalfWordxy[0],&ImuSendBuf.Euler_0x03_str.gamepad_t.Bytesxy[0]);
            convert_from_uint16_t_to_protocol(ImuSendBuf.Euler_0x03_str.gamepad_t.HalfWordxy[1],&ImuSendBuf.Euler_0x03_str.gamepad_t.Bytesxy[1]);
        }
        else if(gSystemSt_t.mOldImuPipeType == data_type_trig_grip_raw)
        {
            ImuSendBuf.Euler_0x04_str.type = data_type_trig_grip_raw;

            convert_from_uint16_t_to_protocol(gSystemSt_t.mButtonSt_t.mRawTrig, &ImuSendBuf.Euler_0x04_str.trigRawdata);
        }
        else if(gSystemSt_t.mOldImuPipeType == data_type_time_sync_test)
        {
            ImuSendBuf.Raw_0x20_TsyncTest.type = data_type_ypr_accel_gyro;
            ImuSendBuf.Raw_0x20_TsyncTest.time = gSystemSt_t.ImutSampleData_t.timestamp /100;
            ImuSendBuf.Raw_0x20_TsyncTest.id =  gSystemSt_t.mSetMarkID;
            ImuSendBuf.Raw_0x20_TsyncTest.ax = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.x;
            ImuSendBuf.Raw_0x20_TsyncTest.ay = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.y;
            ImuSendBuf.Raw_0x20_TsyncTest.az = gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.z;
            
            ImuSendBuf.Raw_0x20_TsyncTest.gx = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.x;
            ImuSendBuf.Raw_0x20_TsyncTest.gy = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.y;
            ImuSendBuf.Raw_0x20_TsyncTest.gz = gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.z;
        
            ImuSendBuf.Raw_0x20_TsyncTest.keys = gSystemSt_t.mButtonSt_t.mDSW_t.byte;
            
            revert_byte_19(&ImuSendBuf.buffer[1]);
        }
        else
        {
            ImuSendBuf.Raw_0x20_str.type = data_type_ypr_accel_gyro;
            ImuSendBuf.Raw_0x20_str.id =  gSystemSt_t.mSetMarkID;
            ImuSendBuf.Raw_0x20_str.yaw  = gSystemSt_t.ImutSampleData_t.ImuYpr_t.yaw * 10.0f;
            ImuSendBuf.Raw_0x20_str.pitch  = gSystemSt_t.ImutSampleData_t.ImuYpr_t.pitch * 10.0f;
            ImuSendBuf.Raw_0x20_str.roll  = gSystemSt_t.ImutSampleData_t.ImuYpr_t.roll * 10.0f;
            ImuSendBuf.Raw_0x20_str.Acc_x = (gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.x /2) >> 4u;
            ImuSendBuf.Raw_0x20_str.Acc_y = (gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.y /2) >> 4u;
            ImuSendBuf.Raw_0x20_str.Acc_z = (gSystemSt_t.ImutSampleData_t.ImuRawData_t.accel.z /2) >> 4u;
            
            ImuSendBuf.Raw_0x20_str.Gyro_x = (gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.x /2) >> 4u;
            ImuSendBuf.Raw_0x20_str.Gyro_y = (gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.y /2) >> 4u;
            ImuSendBuf.Raw_0x20_str.Gyro_z = (gSystemSt_t.ImutSampleData_t.ImuRawData_t.gyro.z /2) >> 4u;
            
            
            ImuSendBuf.Raw_0x20_str.trigger = gSystemSt_t.mButtonSt_t.mTrig;
            ImuSendBuf.Raw_0x20_str.IoKeys_uni.byte = gSystemSt_t.mButtonSt_t.mDSW_t.byte;
            ImuSendBuf.Raw_0x20_str.mLifeTimeRemain = gSystemCfg_t.mDataCfgSet.mGunLifeTime;
            ImuSendBuf.Raw_0x20_str.mBulletNumRemain = gSystemSt_t.Gun_t.mBulletNumRemain;
            
            revert_byte_19(&ImuSendBuf.buffer[1]);
        }
        if(
            (gSystemSt_t.mDeviceSt_uni.Bits.mBleConnect == SYS_BLE_CONN_ST_CONN)
            &&(gSystemSt_t.BlePipeNotifyEn_t.DataPipe  == SYS_CTRL_EN)
            && (gSystemSt_t.mRfCarrierEn == SYS_CTRL_DIS)
            &&(BleSendSingleFrame(BLE_BASE_UUID_IMU_NOR,ImuSendBuf.buffer,20)==PACKET_OPERATE_SUCCESS)
        )
        {
            gSystemSt_t.mImuSendCnt++;
        }
        else
        {
            //do nothing
        }
    }
}
 /* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  RecieveMsg
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void RxMsgProcessTaskInit(void)
{
    ble_gap_addr_t addr;
    uint32_t       err_code;
    ProducInfo_str   *producInfo = (ProducInfo_str *)(0x0006F000);
    CallBackProcessInit();
    FlashManageInit();
    memset(&gSystemSt_t,0x00,sizeof(gSystemSt_t));
    memset(&gSystemCfg_t,0x00,sizeof(gSystemCfg_t));
    if((producInfo->mSaveFlg[0] == 0xAA) && (producInfo->mSaveFlg[1] == 0x55))
    {
        memcpy(&(gSystemCfg_t.mProducInfo),producInfo,sizeof(ProducInfo_str));
    }
    else
    {
        gSystemCfg_t.mProducInfo = ProducInfoDefault;
        err_code = sd_ble_gap_addr_get(&addr);
        if(err_code != NRF_SUCCESS)
        {
            NRF_LOG_INFO("get gap addr fail ");
        }
        else
        {
            memcpy(gSystemCfg_t.mProducInfo.ProdBtMac,addr.addr,BLE_GAP_ADDR_LEN);
        }
        
    }
    if(FlashDataRead(DS_DATA_TYPE_D0,0,(uint8_t *)(&(gSystemCfg_t.mDataCfgSet)),sizeof(SysCfgdata_str)) !=DS_DATA_OPERATE_SUCCESS)
    {
        gSystemCfg_t.mDataCfgSet = gcSysDefaultCfg_t;
    }
    if(FlashDataRead(DS_DATA_TYPE_D1,0,(uint8_t *)(&(gSystemCfg_t.mImuCalibSet)),sizeof(dof9_calib_t)) !=DS_DATA_OPERATE_SUCCESS)
    {
        gSystemCfg_t.mImuCalibSet = gcSysDefaultImuCalibSet_t;
    }
    // gSystemCfg_t.mDataCfgSet.mLocalMac_t.mEnable = 0;
    if(gSystemCfg_t.mDataCfgSet.mLocalMac_t.mEnable)
    {
        memcpy(gSystemCfg_t.mProducInfo.ProdBtMac,gSystemCfg_t.mDataCfgSet.mLocalMac_t.mMac,6u);
    }
    if(gSystemCfg_t.mDataCfgSet.mProdSN.mLen != 0)
    {
        memset(gSystemCfg_t.mProducInfo.ProdPCBASN,0x00,sizeof(gSystemCfg_t.mProducInfo.ProdPCBASN));
        memcpy(gSystemCfg_t.mProducInfo.ProdPCBASN,gSystemCfg_t.mDataCfgSet.mProdSN.mdata,gSystemCfg_t.mDataCfgSet.mProdSN.mLen);
    }
    else
    {
        gSystemCfg_t.mDataCfgSet.mProdSN.mLen = PROD_SN_LEN;
        memcpy(gSystemCfg_t.mDataCfgSet.mProdSN.mdata,gSystemCfg_t.mProducInfo.ProdPCBASN,gSystemCfg_t.mDataCfgSet.mProdSN.mLen);
    }
    gSystemSt_t.Gun_t.mBulletNumPrefab = gSystemCfg_t.mDataCfgSet.mBulletNumPrefab;
    gSystemSt_t.Gun_t.mBulletNumRemain = gSystemCfg_t.mDataCfgSet.mBulletNumRemain;

    gSystemCfg_t.mTpVer = 0xffff;
    gSystemCfg_t.mImuAlgVer[0] = '9';
    gSystemCfg_t.mImuAlgVer[1] = '3';
    gSystemCfg_t.mImuAlgVer[2] = '0';
    gSystemCfg_t.mImuAlgVer[3] = '5';
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_R.mPeriod  =100;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_R.mDuty = 99;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_R.mTimeout = 1;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_G.mPeriod  =100;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_G.mDuty = 99;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_G.mTimeout = 1;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_B.mPeriod  =100;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_B.mDuty = 99;
    // gSystemCfg_t.mDataCfgSet.mBlobPwmCfg_B.mTimeout = 1;
    gSystemSt_t.mFaultSt_uni.byte = 0xff;  //no err
    gSystemSt_t.mFaultSt_uni.Bits.mTrigHallSt = SYS_FAULT_ST_ERR;
	gSystemSt_t.mFaultSt_uni.Bits.mGripHallSt = SYS_FAULT_ST_ERR;
    gSystemSt_t.mDeviceSt_uni.Bits.mAuthCtrl = SYS_CTRL_DIS;
    gSystemSt_t.mDeviceSt_uni.Bits.mBleConnect = SYS_BLE_CONN_ST_DISCONN;
    gSystemSt_t.mDeviceSt_uni.Bits.mUsbConnect = SYS_USB_CONN_ST_DISCONN;
    gSystemSt_t.mDeviceSt_uni.Bits.mSysModel = SYS_MODE_INIT;
    
    gSystemSt_t.mSetMarkID = gSystemCfg_t.mDataCfgSet.mMarkID;
    gSystemSt_t.mOldImuPipeType = gSystemCfg_t.mDataCfgSet.mDefaultSingleDataType;
    gSystemSt_t.PipeTlvTypeEn_t.byte =  gSystemCfg_t.mDataCfgSet.PipeTlvTypeEn_t.byte;
    gSystemSt_t.BleMtuSize = BLE_GATT_ATT_MTU_DEFAULT - 3u;
    gSystemCfg_t.mDataCfgSet.mProtocolType  = 0x00; //old type send
}
 /* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Task_RxMsgProcess
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void Task_RxMsgProcess_Ble(void *pData,uint16_t data_size)
{
    Task_Msg_Q_str            *pRxMsgQ_t = (Task_Msg_Q_str *)(((Task_Msg_Q_str *)pData)->mpBuf);

    FeedWdt(0,0);
    switch(pRxMsgQ_t->mType)
    {
        case MSG_TASK_Q_CMD:
        {
            if(pRxMsgQ_t->mServiceUUID == BLE_BASE_UUID_IMU_NOR)
            {
                BleNorPipeCmdProcess(pRxMsgQ_t->mpBuf,pRxMsgQ_t->mMsgLength);
            }
            else if(pRxMsgQ_t->mServiceUUID == BLE_BASE_UUID_SYS_CFG)
            {
                BleCfgPipeCmdProcess(pRxMsgQ_t->mpBuf,pRxMsgQ_t->mMsgLength);
            }
            else if(pRxMsgQ_t->mServiceUUID == BLE_BASE_UUID_IMU_CALIB)
            {
                BleCalibPipeCmdProcess(pRxMsgQ_t->mpBuf,pRxMsgQ_t->mMsgLength);
            }
            else if(pRxMsgQ_t->mServiceUUID == BLE_BASE_UUID_CALIB_CHECK)
            {
                if(gSystemSt_t.mDeviceSt_uni.Bits.mSysModel == SYS_MODE_CALIB)
                {
                    BleCalibCheckPipeCmdProcess(pRxMsgQ_t->mpBuf,pRxMsgQ_t->mMsgLength);
                }
            }
            
            break;
        }
        case MSG_TASK_Q_IMU:
        {
            if(  (gSystemSt_t.mDeviceSt_uni.Bits.mSysModel == SYS_MODE_NOR)
                || (gSystemSt_t.mDeviceSt_uni.Bits.mSysModel == SYS_MODE_CALIB)
               )
            {
                ImuDataProcess(pRxMsgQ_t->mpBuf,pRxMsgQ_t->mMsgLength); 
                ImuNorPipeDataSend();
            }
            if( (gSystemSt_t.mDeviceSt_uni.Bits.mSysModel == SYS_MODE_CALIB)
                && (gSystemSt_t.mRfCarrierEn == SYS_CTRL_DIS)
              )
            {
                CalibPipeDataUpdata(&gSystemSt_t.ImutSampleData_t.SensorData_t,NULL);
                CalibCheckPipeDataSample(&gSystemSt_t.ImutSampleData_t.SensorData_t,NULL);
            }
            if(gSystemSt_t.mTouchRelease)
            {
                memset(&gSystemSt_t.mTpSampleData_t,0x00,sizeof(gSystemSt_t.mTpSampleData_t));
            }
            break;
        }
        default:
        {
            DEBUG_PRINTF("Rx CMD Queue err\r\n");
            break;
        }
    }
    pRxMsgQ_t->mType = MSG_TASK_Q_RUF;
}

 /* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Task_RxMsgProcess
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void Task_RxMsgProcess_Usb(void *pData,uint16_t data_size)
{
    Task_Msg_Q_str            *pRxMsgQ_t = (Task_Msg_Q_str *)(((Task_Msg_Q_str *)pData)->mpBuf);

    FeedWdt(0,0);
    switch(pRxMsgQ_t->mType)
    {
        case MSG_TASK_Q_USB:
        {
            UsbPipeCmdProcess(pRxMsgQ_t->mpBuf,pRxMsgQ_t->mMsgLength);
            break;
        }
        default:
        {
            DEBUG_PRINTF("Rx CMD Queue err\r\n");
            break;
        }
    }
    pRxMsgQ_t->mType = MSG_TASK_Q_RUF;
}

 /* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  Task_RxMsgProcess
* Description   :   
*
* Inputs        : @param  : 
*                 @param  :  
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void Task_RxMsgProcess_Uart(void *pData,uint16_t data_size)
{
    Task_Msg_Q_str            *pRxMsgQ_t = (Task_Msg_Q_str *)(((Task_Msg_Q_str *)pData)->mpBuf);

    FeedWdt(0,0);
    switch(pRxMsgQ_t->mType)
    {
        case MSG_TASK_Q_UART:
        {
            UartPipeCmdProcess(pRxMsgQ_t->mpBuf,pRxMsgQ_t->mMsgLength);
            break;
        }
        default:
        {
            DEBUG_PRINTF("Rx CMD Queue err\r\n");
            break;
        }
    }
    pRxMsgQ_t->mType = MSG_TASK_Q_RUF;
}

/***********************************************END**********************************************/
