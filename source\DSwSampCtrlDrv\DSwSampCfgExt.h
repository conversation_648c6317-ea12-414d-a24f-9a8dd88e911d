/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : DSwSampCfgExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef D_SW_SAMP_CFG_EXT_H
#define D_SW_SAMP_CFG_EXT_H                              

/*Include files*/
#ifndef FUN_DIS
#define FUN_DIS   0u
#endif

#ifndef FUN_EN
#define FUN_EN    1u
#endif

/*******************************************************************************
* ALL definition and declaration CAN be used and revised outside this driver   *
********************************************************************************/
/* macro definition  */  
/* Number of switches config */
#if defined( MIRAGE2B_BOARD)
    #define D_SW_SAMP_OBJ_NUM                 1u 
#else
    #define D_SW_SAMP_OBJ_NUM                 1u 
#endif

/* Struct bitfield mode config */
#define D_SW_SAMP_BIT_FIELD_MODE          FUN_DIS

/* Switch logic state initialize to "SW_NOEFFECT_ST" config */
#define D_SW_SAMP_INIT_NOEFFECT_CFG       FUN_DIS

/* if configured, when changed from NOEFFECT to ON , it will output OFF*/
#define D_SW_SAMP_NOEF_TO_ON_AS_OFF       FUN_DIS

/* Sample mode enable cofig */
#define D_SW_SAMP_OFF_CFG                 FUN_DIS
#define D_SW_SAMP_ON_CFG                  FUN_DIS
#define D_SW_SAMP_DIRECT_CFG              FUN_EN
#define D_SW_SAMP_INV_CFG                 FUN_DIS
#define D_SW_SAMP_SET_LOW_GOING_CFG       FUN_DIS
#define D_SW_SAMP_SET_HIGH_GOING_CFG      FUN_DIS
#define D_SW_SAMP_TOGGLE_LOW_GOING_CFG    FUN_DIS
#define D_SW_SAMP_TOGGLE_HIGH_GOING_CFG   FUN_DIS

#define D_SW_SAMP_S12_SUPPORT             FUN_DIS

#define D_SW_SAMP_DEFAULT_SAMP_TIMES      2u


/* Sample mode for every switch config */
#if (D_SW_SAMP_OBJ_NUM >= 1)
#define D_SW_SAMP_SAMP_MODE_0             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 2)
#define D_SW_SAMP_SAMP_MODE_1             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 3)
#define D_SW_SAMP_SAMP_MODE_2             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 4)
#define D_SW_SAMP_SAMP_MODE_3             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 5)
#define D_SW_SAMP_SAMP_MODE_4             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 6)
#define D_SW_SAMP_SAMP_MODE_5             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 7)
#define D_SW_SAMP_SAMP_MODE_6             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 8)
#define D_SW_SAMP_SAMP_MODE_7             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 9)
#define D_SW_SAMP_SAMP_MODE_8             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 10)
#define D_SW_SAMP_SAMP_MODE_9             D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 11)
#define D_SW_SAMP_SAMP_MODE_10            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 12)
#define D_SW_SAMP_SAMP_MODE_11            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 13)
#define D_SW_SAMP_SAMP_MODE_12            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 14)
#define D_SW_SAMP_SAMP_MODE_13            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 15)
#define D_SW_SAMP_SAMP_MODE_14            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 16)
#define D_SW_SAMP_SAMP_MODE_15            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 17)
#define D_SW_SAMP_SAMP_MODE_16            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 18)
#define D_SW_SAMP_SAMP_MODE_17            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 19)
#define D_SW_SAMP_SAMP_MODE_18            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 20)
#define D_SW_SAMP_SAMP_MODE_19            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 21)
#define D_SW_SAMP_SAMP_MODE_20            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 22)
#define D_SW_SAMP_SAMP_MODE_21            D_SW_SAMP_MODE_INV | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 23)
#define D_SW_SAMP_SAMP_MODE_22            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 24)
#define D_SW_SAMP_SAMP_MODE_23            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 25)
#define D_SW_SAMP_SAMP_MODE_24            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 26)
#define D_SW_SAMP_SAMP_MODE_25            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 27)
#define D_SW_SAMP_SAMP_MODE_26            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 28)
#define D_SW_SAMP_SAMP_MODE_27            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 29)
#define D_SW_SAMP_SAMP_MODE_28            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 30)
#define D_SW_SAMP_SAMP_MODE_29            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 31)
#define D_SW_SAMP_SAMP_MODE_30            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 32)
#define D_SW_SAMP_SAMP_MODE_31            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 33)
#define D_SW_SAMP_SAMP_MODE_32            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 34)
#define D_SW_SAMP_SAMP_MODE_33            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 35)
#define D_SW_SAMP_SAMP_MODE_34            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 36)
#define D_SW_SAMP_SAMP_MODE_35            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 37)
#define D_SW_SAMP_SAMP_MODE_36            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 38)
#define D_SW_SAMP_SAMP_MODE_37            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 39)
#define D_SW_SAMP_SAMP_MODE_38            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 40)
#define D_SW_SAMP_SAMP_MODE_39            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 41)
#define D_SW_SAMP_SAMP_MODE_40            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 42)
#define D_SW_SAMP_SAMP_MODE_41            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 43)
#define D_SW_SAMP_SAMP_MODE_42            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 44)
#define D_SW_SAMP_SAMP_MODE_43            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 45)
#define D_SW_SAMP_SAMP_MODE_44            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 46)
#define D_SW_SAMP_SAMP_MODE_45            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 47)
#define D_SW_SAMP_SAMP_MODE_46            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 48)
#define D_SW_SAMP_SAMP_MODE_47            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 49)
#define D_SW_SAMP_SAMP_MODE_48            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

#if (D_SW_SAMP_OBJ_NUM >= 50)
#define D_SW_SAMP_SAMP_MODE_49            D_SW_SAMP_MODE_DIRECT | DSS_NOEF_TO_ON_AS_OFF_NO
#endif

/* check the collision between D_SW_SAMP_INIT_NOEFFECT and D_SW_SAMP_NOEF_TO_ON_AS_OFF*/
#if (D_SW_SAMP_INIT_NOEFFECT_CFG == FUN_DIS)
#undef D_SW_SAMP_NOEF_TO_ON_AS_OFF
#define D_SW_SAMP_NOEF_TO_ON_AS_OFF       FUN_DIS
#endif

/* "D_SW_SAMP_OBJ_NUM" checking */
#if (D_SW_SAMP_OBJ_NUM > 50)
  #error: Only less than 50 objects can be supported!
#endif
#if (D_SW_SAMP_OBJ_NUM == 0)
  #error: D_SW_SAMP_OBJ_NUM is less than 1!
#endif


/* Checking whether macro "FUN_EN" and "FUN_DIS" are defined */
#ifndef FUN_EN
  #error "FUN_EN is not defined!"
#endif

/* function configuration definition */

#endif	/* end of header file */
