/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : ICM426XXCtrlDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2017/10/24    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef ICM426XX_CTRL_DRV_C
#define ICM426XX_CTRL_DRV_C
#endif

/* include files */
#include "ICM426XXCtrlDrv.h"

/* static variable definition */
/* Just a handy variable to handle the icm426xx object */
static struct inv_icm426xx icm_driver;
static ImuSensorData_str   icmSensorData_t;
static uint8_t who_am_i = 0;
/* static function declaration */

/* static function definition */
imu_drv_str gImuDrv_t = {NULL,NULL,NULL};
/* public function definition */
/** @brief Hook for low-level high res system sleep() function to be implemented by upper layer
 *  ~100us resolution is sufficient
 *  @param[in] us number of us the calling thread should sleep
 */
void inv_icm426xx_sleep_us(uint32_t us)
{
    ICM426XX_DELAY_US(us);
}


/** @brief Hook for low-level high res system get_time() function to be implemented by upper layer
 *  Timer should be on 64bit with a 1 us resolution
 *  @param[out] The current time in us
 */
uint64_t inv_icm426xx_get_time_us(void)
{
    return 0;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  ICM426XXWriteReg
* Description   :  
*
* Inputs        : @param  None:
*                          
*                 @global val  None:
*                  
* Outputs       : @param   None:
*                          
*                 @global val  None:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
int ICM426XXWriteReg(struct inv_icm426xx_serif * serif, uint8_t aAddr, uint8_t * apbuf, uint32_t aLen)
{
    int ret = ICM426XX_OPERATE_SUCCESS;
#if IMU_SPI_EN
    uint8_t Tx_buf[ICM426XX_TXRX_LEN_MAX];
    uint8_t Rx_buf[ICM426XX_TXRX_LEN_MAX];
    
    Tx_buf[0] = ((uint8_t)aAddr) &0x7F; //write flg
    memcpy(&Tx_buf[1],apbuf,aLen);
    ICM426XX_PIN_SPI_CS_LOW();
    if(nrf_drv_spi_transfer(&IMU_CTRL_SPI_NUM_CFG, Tx_buf, aLen+1u, Rx_buf, aLen+1u) != NRFX_SUCCESS)
    {
        ret = ICM426XX_OPERATE_ERROR;
    }
    ICM426XX_PIN_SPI_CS_HIGH();
#else
    if(I2C_WriteOneReg(ICM426XX_I2C_HANDLE,ICM426XX_I2C_ADDRESS,aAddr,apbuf, aLen) == I2C_RW_ST_ERROR)
    {
        ret = ICM426XX_OPERATE_ERROR;
    }
    else
    {
        //do nothing
    }
#endif
    if(aAddr == 0x6B)
    {
        ICM426XX_DELAY_MS(5);
    }
    return ret;
} 
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  ICM426XXReadReg
* Description   :  
*
* Inputs        : @param  None:
*                          
*                 @global val  None:
*                  
* Outputs       : @param   None:
*                          
*                 @global val  None:
*
*                 @retval :
*                               
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
int ICM426XXReadReg(struct inv_icm426xx_serif * serif, uint8_t aAddr, uint8_t * apbuf, uint32_t aLen)
{
    int ret = ICM426XX_OPERATE_SUCCESS;
#if IMU_SPI_EN
    uint8_t Tx_buf[ICM426XX_TXRX_LEN_MAX];
    uint8_t Rx_buf[ICM426XX_TXRX_LEN_MAX];
    
    Tx_buf[0] = ((uint8_t)aAddr) | 0x80; //read flg
    memcpy(&Tx_buf[1],apbuf,aLen);
    ICM426XX_PIN_SPI_CS_LOW();
    if(nrf_drv_spi_transfer(&IMU_CTRL_SPI_NUM_CFG, Tx_buf, 1u, Rx_buf, 1u) != NRFX_SUCCESS)
    {
        ret = ICM426XX_OPERATE_ERROR;
    }
    else
    {
        memset(Tx_buf,0xff,sizeof(Tx_buf));
        if(nrf_drv_spi_transfer(&IMU_CTRL_SPI_NUM_CFG, Tx_buf, aLen, Rx_buf, aLen) != NRFX_SUCCESS)
        {
            ret = ICM426XX_OPERATE_ERROR;
        }
        else
        {
            memcpy(apbuf,Rx_buf,aLen);
        }
    }
    ICM426XX_PIN_SPI_CS_HIGH();
#else
    /*select read rigister address*/
    if(I2C_ReadOneReg(ICM426XX_I2C_HANDLE,ICM426XX_I2C_ADDRESS,(uint8_t)aAddr,apbuf , aLen) == I2C_RW_ST_SUCCESS)
    {
        //do nothing
    }
    else
    {
        ret = ICM426XX_OPERATE_ERROR;
    }
#endif
    return ret;
} 

int inv_io_hal_configure(struct inv_icm426xx_serif * serif)
{
    switch (serif->serif_type) 
    {
        default:
        return -1;
    }
}
void HandleInvDeviceDataRegisters(inv_icm426xx_sensor_event_t * event)
{
    icmSensorData_t.mTempData = event->temperature;
    icmSensorData_t.mAcc3axisData_t.mAccX  = (event->accel[1]);
    icmSensorData_t.mAcc3axisData_t.mAccY  = -event->accel[0];
    icmSensorData_t.mAcc3axisData_t.mAccZ  = event->accel[2];
    
    icmSensorData_t.mGyro3axisData_t.mGyroX = (event->gyro[1]);
    icmSensorData_t.mGyro3axisData_t.mGyroY = -event->gyro[0];
    icmSensorData_t.mGyro3axisData_t.mGyroZ = event->gyro[2];
}

int SetupInvDevice(void)
{
    int rc = 0;
    struct inv_icm426xx_serif icm426xx_serif;
     who_am_i = 0;
    /* Initialize device */
    NRF_LOG_INFO("Initialize Icm426xx");
    
    /* Initialize serial inteface between MCU and Icm426xx */
    icm426xx_serif.context   = 0;        /* no need */
    icm426xx_serif.read_reg  = ICM426XXReadReg;
    icm426xx_serif.write_reg = ICM426XXWriteReg;
    icm426xx_serif.max_read  = 1024;  /* maximum number of bytes allowed per serial read */
    icm426xx_serif.max_write = 1024;  /* maximum number of bytes allowed per serial write */
#if(IMU_SPI_EN)
    icm426xx_serif.serif_type = ICM426XX_UI_SPI4;
#else
    icm426xx_serif.serif_type = ICM426XX_UI_I2C;
#endif
    icm426xx_serif.configure = inv_io_hal_configure;
    
    
    rc = inv_icm426xx_init(&icm_driver, &icm426xx_serif, HandleInvDeviceDataRegisters);
    /* Disable fifo usage, data will be read from sensors registers*/
    rc |= inv_icm426xx_configure_fifo(&icm_driver, INV_ICM426XX_FIFO_DISABLED);
    if(rc != INV_ERROR_SUCCESS) 
    {
        NRF_LOG_INFO("!!! ERROR : failed to initialize Icm426xx.");
        return rc;
    }    
    
    /* Check WHOAMI */
    NRF_LOG_INFO("Check Icm426xx whoami value");
    
    rc = inv_icm426xx_get_who_am_i(&icm_driver, &who_am_i);
    if(rc != INV_ERROR_SUCCESS) 
    {
        NRF_LOG_INFO("!!! ERROR : failed to read Icm426xx whoami value.");
        return rc;
    }
    NRF_LOG_INFO( "WHOAMI value. Got 0x%02x (expected: 0x%02x)", who_am_i, ICM_WHOAMI);
    return rc;
}

int ConfigureInvDevice(uint8_t is_low_noise_mode,
                       ICM426XX_ACCEL_CONFIG0_FS_SEL_t acc_fsr_g,
                       ICM426XX_GYRO_CONFIG0_FS_SEL_t gyr_fsr_dps,
                       ICM426XX_ACCEL_CONFIG0_ODR_t acc_freq,
                       ICM426XX_GYRO_CONFIG0_ODR_t gyr_freq,
                       uint8_t is_rtc_mode)
{
    int rc = 0;
    
    rc |= inv_icm426xx_enable_clkin_rtc(&icm_driver, is_rtc_mode);

    rc |= inv_icm426xx_set_accel_fsr(&icm_driver, acc_fsr_g);
    rc |= inv_icm426xx_set_gyro_fsr(&icm_driver, gyr_fsr_dps);
    
    rc |= inv_icm426xx_set_accel_frequency(&icm_driver, acc_freq);
    rc |= inv_icm426xx_set_gyro_frequency(&icm_driver, gyr_freq);
    
    if (is_low_noise_mode)
    {
        rc |= inv_icm426xx_enable_accel_low_noise_mode(&icm_driver);
    }
    else
    {
        rc |= inv_icm426xx_enable_accel_low_power_mode(&icm_driver);
    }
    
    rc |= inv_icm426xx_enable_gyro_low_noise_mode(&icm_driver);

    /* Wait Max of ICM426XX_GYR_STARTUP_TIME_US and ICM426XX_ACC_STARTUP_TIME_US*/
    (ICM426XX_GYR_STARTUP_TIME_US > ICM426XX_ACC_STARTUP_TIME_US) ? inv_icm426xx_sleep_us(ICM426XX_GYR_STARTUP_TIME_US) : inv_icm426xx_sleep_us(ICM426XX_ACC_STARTUP_TIME_US);
        
    return rc;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  GetData_acc_gyro
* Description   :  
*
* Inputs        : @param  : 
*                 @param  : 
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t GetData_acc_gyro_426xx(ImuSensorData_str *apOutData_t)
{
    uint8_t ret = IMU_OPERATE_SUCCESS;
    
    if(0==inv_icm426xx_get_data_from_registers(&icm_driver))
    {
        *apOutData_t =  icmSensorData_t;
    }
    else
    {
        ret = IMU_OPERATE_ERROR;
    }
    
    return ret;
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  GetData_acc_gyro
* Description   :  
*
* Inputs        : @param  : 
*                 @param  : 
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
void Imc426XXClearData_acc_gyro(void)
{
    inv_icm426xx_get_data_from_registers(&icm_driver);
    memset(&icmSensorData_t,0x00,sizeof(icmSensorData_t)); 
}
/* BEGIN_FUNCTION_HDR
************************************************************************************************
* Function Name :  IMC6Axis_LowNoiseModeConfig
* Description   :  
*
* Inputs        : @param  : 
*                 @param  : 
*                 @param  : 
*                 @global val  None:
* Outputs       : @param   None:
*                 @global val  None:
*                 @retval :  None
* Limitations   : 
*************************************************************************************************
END_FUNCTION_HDR */ 
uint8_t Imc426XX_Imu_Init(ImuDataRate_em  smplrt_div)
{
    uint8_t ret = IMU_OPERATE_SUCCESS;
    
    if(SetupInvDevice() == 0)
    {
        if(ConfigureInvDevice(IS_LOW_NOISE_MODE,ICM426XX_ACCEL_CONFIG0_FS_SEL_16g,ICM426XX_GYRO_CONFIG0_FS_SEL_2000dps,ICM426XX_ACCEL_CONFIG0_ODR_1_KHZ,ICM426XX_GYRO_CONFIG0_ODR_1_KHZ,HZUSE_CLK_IN)  != 0)
        {
            ret = IMU_OPERATE_ERROR;
        }
       
    }
    else
    {
        ret = IMU_OPERATE_ERROR;
    }
    return ret;
}

uint8_t Imu_init(void)
{
    uint8_t ret = IMU_OPERATE_SUCCESS;
    uint8_t whoIm = 0xFF;
    
    if(ICM426XXReadReg(NULL, MPUREG_WHO_AM_I, &whoIm, 1)== ICM426XX_OPERATE_SUCCESS)
    {
        if(whoIm == ICM20602_WHOAMI)
        {
            gImuDrv_t.init_action = IMC6Axis_LowNoiseModeConfig;
            gImuDrv_t.clearBuf = ICM20602_ClearData_acc_gyro;
            gImuDrv_t.get_bytes = GetData_acc_gyro;
        }
        else
        {
            gImuDrv_t.init_action = Imc426XX_Imu_Init;
            gImuDrv_t.clearBuf = Imc426XXClearData_acc_gyro;
            gImuDrv_t.get_bytes = GetData_acc_gyro_426xx;
        }
        if(gImuDrv_t.init_action(CONFIG0_ODR_1_KHZ) !=IMU_OPERATE_SUCCESS)
        {
            ret = IMU_OPERATE_ERROR;
        }
    }
    else
    {
        ret = IMU_OPERATE_ERROR;
    }
    return ret;
}
/***********************************************END**********************************************/
