// @@ -1,1048 +1,1045 @@
/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Main.C
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/
#ifndef TIME_SYNC_C
#define TIME_SYNC_C
#endif

/*include files*/
#include "time_sync.h"

/*macro*/

#if   defined ( __CC_ARM )
#define TX_CHAIN_DELAY_PRESCALER_0 699
#elif defined ( __ICCARM__ )
#define TX_CHAIN_DELAY_PRESCALER_0 703
#elif defined ( __GNUC__ )
#define TX_CHAIN_DELAY_PRESCALER_0 704
#endif

#define SYNC_TIMER_PRESCALER 0

#if SYNC_TIMER_PRESCALER == 0
#define TX_CHAIN_DELAY TX_CHAIN_DELAY_PRESCALER_0
#else
#error Invalid prescaler value
#endif

  
#define TS_LEN_US                                (1000UL)
#define TX_LEN_EXTENSION_US                      (1000UL)
#define TS_SAFETY_MARGIN_US                      (500UL)    /**< The timeslot activity should be finished with this much to spare. */
#define TS_EXTEND_MARGIN_US                      (700UL)   /**< The timeslot activity should request an extension this long before end of timeslot. */
#define MAIN_DEBUG                               0x12345678UL
#define TIMER_MAX_VAL                            (0xFFFF)
#define TIMER_US_MAX_TARGET                     (1<<24) //使用自动重载会导致CC[1] 里面的SWI失效
#define TIMER_US_MAX_VALUE                         ((TIMER_US_MAX_TARGET)-1)

/*varyable define*/
static const int GenParamCounter = 20;
static bool isUpdateProcessing = false;
static volatile bool m_timeslot_session_open;
volatile uint32_t    m_blocked_cancelled_count;
static uint32_t      m_total_timeslot_length = 0;
static uint32_t      m_timeslot_distance = 0;
static ts_params_t   m_params;
static volatile bool m_send_sync_pkt = false;
static pf_time_sync_swi_handle callback_when_swi = 0;

volatile uint32_t m_test_count = 0;
volatile uint32_t m_rcv_count = 0;

static time_sync_payload_t m_sync_pkt;
static void sync_timer_offset_compensate(void);
static void timeslot_begin_handler(void);
static void timeslot_end_handler(void);
static linear_fit_buffer_t timestamp_sync_linear_fit_buffer_0;
static const uint8_t       Conn_Default_Channel_tab[RFPROTO_MAX_CHANNEL_TAB_SIZE] = RFPROTO_DEFAULT_CHANNEL_TAB;

void ts_on_sys_evt(uint32_t sys_evt, void * p_context);

NRF_SDH_SOC_OBSERVER(timesync_soc_obs,     \
                     TS_SOC_OBSERVER_PRIO, \
                     ts_on_sys_evt, 0);
/**< This will be used when requesting the first timeslot or any time a timeslot is blocked or cancelled. */
static nrf_radio_request_t m_timeslot_req_earliest = {
        NRF_RADIO_REQ_TYPE_EARLIEST,
        .params.earliest = {
            NRF_RADIO_HFCLK_CFG_XTAL_GUARANTEED,
            NRF_RADIO_PRIORITY_NORMAL,
            TS_LEN_US,
            NRF_RADIO_EARLIEST_TIMEOUT_MAX_US
        }};

/**< This will be used at the end of each timeslot to request the next timeslot. */
static nrf_radio_request_t m_timeslot_req_normal = {
        NRF_RADIO_REQ_TYPE_NORMAL,
        .params.normal = {
            NRF_RADIO_HFCLK_CFG_XTAL_GUARANTEED,
            NRF_RADIO_PRIORITY_NORMAL,
            0,
            TS_LEN_US
        }};

/**< This will be used at the end of each timeslot to request the next timeslot. */
static nrf_radio_signal_callback_return_param_t m_rsc_return_sched_next_normal = {
        NRF_RADIO_SIGNAL_CALLBACK_ACTION_REQUEST_AND_END,
        .params.request = {
                (nrf_radio_request_t*) &m_timeslot_req_normal
        }};

/**< This will be used at the end of each timeslot to request the next timeslot. */
static nrf_radio_signal_callback_return_param_t m_rsc_return_sched_next_earliest = {
        NRF_RADIO_SIGNAL_CALLBACK_ACTION_REQUEST_AND_END,
        .params.request = {
                (nrf_radio_request_t*) &m_timeslot_req_earliest
        }};

/**< This will be used at the end of each timeslot to request an extension of the timeslot. */
static nrf_radio_signal_callback_return_param_t m_rsc_extend = {
        NRF_RADIO_SIGNAL_CALLBACK_ACTION_EXTEND,
        .params.extend = {TX_LEN_EXTENSION_US}
        };

/**< This will be used at the end of each timeslot to request the next timeslot. */
static nrf_radio_signal_callback_return_param_t m_rsc_return_no_action = {
        NRF_RADIO_SIGNAL_CALLBACK_ACTION_NONE,
        .params.request = {NULL}
        };



//external api to get stamp region us
static inline uint32_t trigger_hf0_cc0_capture(ts_params_t* p_param)
{
    p_param->high_freq_timer[0]->TASKS_CAPTURE[0] = 1;
    return p_param->high_freq_timer[0]->CC[0];
}

//get rf tx or rx capture stamp region us
static inline uint32_t read_hf0_cc2_capture(ts_params_t* p_param)
{
    return p_param->high_freq_timer[0]->CC[2];
}

//external api to get stamp region ms and s
static inline uint32_t trigger_hf1_cc0_capture(ts_params_t* p_param)
{
    p_param->high_freq_timer[1]->TASKS_CAPTURE[0] = 1;
    return p_param->high_freq_timer[1]->CC[0];
}

//get rf tx or rx capture stamp region ms and s
static inline uint32_t read_hf1_cc2_capture(ts_params_t* p_param)
{
    return p_param->high_freq_timer[1]->CC[2];
}

//manage ppi index 0 1 2
static inline void config_ppi_on_rf_rx(ts_params_t* p_param)
{
    uint32_t channel_us = p_param->ppi_chns[0];
    uint32_t channel_ms = p_param->ppi_chns[1];
    
    NRF_PPI->CH[channel_us].EEP = (uint32_t) &NRF_RADIO->EVENTS_ADDRESS;
    NRF_PPI->CH[channel_us].TEP = (uint32_t) &(p_param->high_freq_timer[0]->TASKS_CAPTURE[2]);
    NRF_PPI->CHENSET         = (1 << channel_us);
    
    NRF_PPI->CH[channel_ms].EEP = (uint32_t) &NRF_RADIO->EVENTS_ADDRESS;
    NRF_PPI->CH[channel_ms].TEP = (uint32_t) &(p_param->high_freq_timer[1]->TASKS_CAPTURE[2]);
    NRF_PPI->CHENSET         = (1 << channel_ms);
}

//manage ppi index 0 1 2
static inline void config_ppi_on_rf_tx(ts_params_t* p_param)
{
    uint32_t channel_us = p_param->ppi_chns[0];
    uint32_t channel_ms = p_param->ppi_chns[1];
    uint32_t channel_tx = p_param->ppi_chns[2];
    
    NRF_PPI->CH[channel_us].EEP = (uint32_t) &(p_param->high_freq_timer[2]->EVENTS_COMPARE[0]);
    NRF_PPI->CH[channel_us].TEP = (uint32_t) &(p_param->high_freq_timer[0]->TASKS_CAPTURE[2]);
    NRF_PPI->CHENSET         = (1 << channel_us);
    
    NRF_PPI->CH[channel_ms].EEP = (uint32_t) &(p_param->high_freq_timer[2]->EVENTS_COMPARE[0]);
    NRF_PPI->CH[channel_ms].TEP = (uint32_t) &(p_param->high_freq_timer[1]->TASKS_CAPTURE[2]);
    NRF_PPI->CHENSET         = (1 << channel_ms);
    
    NRF_PPI->CH[channel_tx].EEP = (uint32_t) &(p_param->high_freq_timer[2]->EVENTS_COMPARE[1]);
    NRF_PPI->CH[channel_tx].TEP = (uint32_t) &NRF_RADIO->TASKS_START;
    NRF_PPI->CHENSET         = (1 << channel_tx);
}

static inline void config_ppi_swi(ts_params_t* p_param)
{
    uint32_t channel_us_to_ppi = p_param->ppi_chns[4];
    NRF_PPI->CH[channel_us_to_ppi].EEP = (uint32_t) &(p_param->high_freq_timer[0]->EVENTS_COMPARE[1]);
    NRF_PPI->CH[channel_us_to_ppi].TEP = (uint32_t) &(p_param->egu->TASKS_TRIGGER[0]);
}

static inline void config_ppi_user_side(ts_params_t* p_param, uint32_t task_addr)
{
    uint32_t channel_us_to_usr_task_ppi = p_param->ppi_chns[5];
    NRF_PPI->CH[channel_us_to_usr_task_ppi].EEP = (uint32_t) &(p_param->high_freq_timer[0]->EVENTS_COMPARE[1]);
    NRF_PPI->CH[channel_us_to_usr_task_ppi].TEP = task_addr;
}

static inline void config_swi_enable(ts_params_t* p_param)
{
    uint32_t channel_us_to_ppi = p_param->ppi_chns[4];
    uint32_t channel_us_to_usr_task_ppi = p_param->ppi_chns[5];
    
    NVIC_ClearPendingIRQ(p_param->egu_irq_type);
    NVIC_SetPriority(p_param->egu_irq_type, 7);
    NVIC_EnableIRQ(p_param->egu_irq_type);
    
    p_param->egu->INTENSET = EGU_INTENSET_TRIGGERED0_Msk;
    NRF_PPI->CHENSET         = (1 << channel_us_to_ppi);
    if(NRF_PPI->CH[channel_us_to_usr_task_ppi].TEP != 0)
    {
        NRF_PPI->CHENSET         = (1 << channel_us_to_usr_task_ppi);
    }
}

static inline void config_swi_set_next_swi(ts_params_t* p_param, uint32_t next_us_cc)
{
    p_param->high_freq_timer[0]->CC[1] = next_us_cc;
}

static inline void config_swi_disable(ts_params_t* p_param)
{
    uint32_t channel_us_to_ppi = p_param->ppi_chns[4];
    uint32_t channel_us_to_usr_task_ppi = p_param->ppi_chns[5];
    
    NVIC_ClearPendingIRQ(p_param->egu_irq_type);
    NVIC_DisableIRQ(p_param->egu_irq_type);
    
    p_param->egu->INTENSET = EGU_INTENSET_TRIGGERED0_Msk;
    NRF_PPI->CHENCLR         = (1 << channel_us_to_ppi);
    NRF_PPI->CHENCLR         = (1 << channel_us_to_usr_task_ppi);
}


static inline void config_rf_rx_start(ts_params_t* p_param)
{
    NRF_RADIO->SHORTS     = RADIO_SHORTS_READY_START_Msk | RADIO_SHORTS_END_START_Msk;
    NRF_RADIO->TASKS_RXEN = 1;
}

//manage timer index 2
static inline void config_timer_tx_prepare(ts_params_t* p_param)
{
    p_param->high_freq_timer[2]->PRESCALER   = 4; // 1 us resolution
    p_param->high_freq_timer[2]->MODE        = TIMER_MODE_MODE_Timer << TIMER_MODE_MODE_Pos;
    p_param->high_freq_timer[2]->SHORTS      = TIMER_SHORTS_COMPARE1_STOP_Msk | TIMER_SHORTS_COMPARE1_CLEAR_Msk;
    p_param->high_freq_timer[2]->TASKS_STOP  = 1;
    p_param->high_freq_timer[2]->TASKS_CLEAR = 1;
    p_param->high_freq_timer[2]->CC[0]       = 45; // Matches 40 us radio rampup time
    p_param->high_freq_timer[2]->CC[1]       = 60; // Margin for timer readout
    
    p_param->high_freq_timer[2]->EVENTS_COMPARE[0] = 0;
    p_param->high_freq_timer[2]->EVENTS_COMPARE[1] = 0;
}

static inline void config_rf_tx_start(ts_params_t* p_param)
{
    NRF_RADIO->SHORTS                        = RADIO_SHORTS_END_DISABLE_Msk;
    NRF_RADIO->TASKS_TXEN                    = 1;
    p_param->high_freq_timer[2]->TASKS_START = 1;
}


static inline void rf_tx_update_payload(ts_params_t* p_param)
{
    uint64_t stamp_us = read_hf1_cc2_capture(&m_params);
    stamp_us *= TIMER_US_MAX_TARGET;
    stamp_us += read_hf0_cc2_capture(&m_params);
    m_sync_pkt.stamp = stamp_us;
}

static inline uint64_t get_rf_remote_stamp(uint64_t* p_remote_raw)
{
    return (*p_remote_raw) + TX_CHAIN_DELAY;
}

static void config_timer_hf0(ts_params_t* p_param)
{
    p_param->high_freq_timer[0]->TASKS_STOP  = 1;
    p_param->high_freq_timer[0]->TASKS_CLEAR = 1;
    p_param->high_freq_timer[0]->MODE        = TIMER_MODE_MODE_Timer;
    p_param->high_freq_timer[0]->BITMODE     = TIMER_BITMODE_BITMODE_24Bit;
    p_param->high_freq_timer[0]->PRESCALER   = SYNC_TIMER_PRESCALER;
    p_param->high_freq_timer[0]->CC[3]       = TIMER_US_MAX_VALUE;      //auto reload
    p_param->high_freq_timer[0]->TASKS_START = 1;
}

//ppi 3 usage 
static void config_timer_hf1(ts_params_t* p_param)
{
    p_param->high_freq_timer[1]->TASKS_STOP  = 1;
    p_param->high_freq_timer[1]->TASKS_CLEAR = 1;
    p_param->high_freq_timer[1]->MODE        = TIMER_MODE_MODE_Counter;
    p_param->high_freq_timer[1]->BITMODE     = TIMER_BITMODE_BITMODE_32Bit;
    p_param->high_freq_timer[1]->PRESCALER   = 0;
    p_param->high_freq_timer[1]->TASKS_START = 1;
    
    uint32_t channel_counter = p_param->ppi_chns[3];
    
    NRF_PPI->CH[channel_counter].EEP = (uint32_t) &(p_param->high_freq_timer[0]->EVENTS_COMPARE[3]);
    NRF_PPI->CH[channel_counter].TEP = (uint32_t) &(p_param->high_freq_timer[1]->TASKS_COUNT);
    NRF_PPI->CHENSET         = (1 << channel_counter);
}

static uint64_t fix_stamp_offset_ppm(ts_params_t* p_param, uint64_t raw)
{
    int64_t ppm_offset = (int64_t)(((double)raw)*p_param->stamp_ppm/1e6);
    int64_t stamp_with_offset = raw + p_param->stamp_offset;
    int64_t stamp_with_ppm = stamp_with_offset + ppm_offset;
    
    if(stamp_with_ppm < 0)
    {
        NRF_LOG_INFO("stamp_with_ppm < 0 !!!\r\n");
    }
    
    return (uint64_t)stamp_with_ppm;
}

static uint64_t de_fix_stamp_offset_ppm(ts_params_t* p_param, uint64_t remote)
{
    int64_t remote_with_offset = remote - p_param->stamp_offset;
    int64_t ppm_offset = (int64_t)(((double)remote_with_offset)*p_param->stamp_ppm/1e6);
    int64_t remote_with_ppm = remote_with_offset - ppm_offset;
    if(remote_with_ppm < 0)
    {
        NRF_LOG_INFO("remote_with_ppm < 0 !!!\r\n");
    }
    return (uint64_t)remote_with_ppm; 
}


static inline uint64_t read_stamp_fixed(ts_params_t* p_param)
{
    uint32_t us = trigger_hf0_cc0_capture(p_param);
    uint64_t ms = trigger_hf1_cc0_capture(p_param);
    uint64_t stamp_raw = ms*TIMER_US_MAX_TARGET + us;
    uint64_t stamp_fixed = fix_stamp_offset_ppm(p_param, stamp_raw);
    uint64_t stamp_us = stamp_fixed / 16;
    return stamp_us;
}

static void linear_fit_init(linear_fit_buffer_t* p_buffer)
{
    p_buffer->counter = 0;
    p_buffer->sum_x = 0;
    p_buffer->sum_x_sq = 0;
    p_buffer->sum_y = 0;
    p_buffer->sum_xy = 0;
}

static void linear_fit_push_data(linear_fit_buffer_t* p_buffer, linear_fit_element_t* p_element)
{
    p_buffer->sum_x     += p_element->x;
    p_buffer->sum_x_sq  += p_element->x * p_element->x;
    p_buffer->sum_y     += p_element->y;
    p_buffer->sum_xy    += p_element->x * p_element->y;
    p_buffer->counter ++;
}

static void linear_fit_get_result(linear_fit_buffer_t* p_buffer, linear_fit_result_t* p_result)
{
    double denow = p_buffer->counter * p_buffer->sum_x_sq - p_buffer->sum_x * p_buffer->sum_x;
    p_result->a0 = (p_buffer->sum_y * p_buffer->sum_x_sq - p_buffer->sum_x * p_buffer->sum_xy) / denow;
    p_result->a1 = (p_buffer->counter * p_buffer->sum_xy - p_buffer->sum_x * p_buffer->sum_y) / denow;
}


static inline void update_radio_status(void)
{
    if (!m_send_sync_pkt)
    {
        if (m_radio_state    != RADIO_STATE_RX ||
            NRF_RADIO->STATE != (RADIO_STATE_STATE_Rx << RADIO_STATE_STATE_Pos))
        {
            update_radio_parameters();
            
            config_rf_rx_start(&m_params);
            
            config_ppi_on_rf_rx(&m_params);
            m_radio_state = RADIO_STATE_RX;
        }
    }
}
/**@brief   Function for handling timeslot events.
 */

static void RADIO_IRQHandler(void)
{
    if (NRF_RADIO->EVENTS_END != 0)
    {
        NRF_RADIO->EVENTS_END = 0;
        (void)NRF_RADIO->EVENTS_END;

        if (m_radio_state == RADIO_STATE_RX && 
           (NRF_RADIO->CRCSTATUS & RADIO_CRCSTATUS_CRCSTATUS_Msk) == (RADIO_CRCSTATUS_CRCSTATUS_CRCOk << RADIO_CRCSTATUS_CRCSTATUS_Pos))
        {
            SynTestRecord_t.recv_count ++;
            sync_timer_offset_compensate();
            ++m_rcv_count;
            if(gSystemSt_t.mSynTimEnable == SYS_BLE_TIME_SYN_DISABLE)
            {
                gSystemSt_t.mSynTimEnable = SYS_BLE_TIME_SYN_ENABLE;
            }
        }
    }
}


static nrf_radio_signal_callback_return_param_t * radio_callback (uint8_t signal_type)
{   
    // NOTE: This callback runs at lower-stack priority (the highest priority possible).
    switch (signal_type) {
    case NRF_RADIO_CALLBACK_SIGNAL_TYPE_START:
        SynTestRecord_t.radio_start_count ++;
        // TIMER0 is pre-configured for 1Mhz.
        NRF_TIMER0->TASKS_STOP          = 1;
        NRF_TIMER0->TASKS_CLEAR         = 1;
        NRF_TIMER0->MODE                = (TIMER_MODE_MODE_Timer << TIMER_MODE_MODE_Pos);
        NRF_TIMER0->EVENTS_COMPARE[0]   = 0;
        NRF_TIMER0->EVENTS_COMPARE[1]   = 0;
    
//        if (m_send_sync_pkt)
//        {
//            NRF_TIMER0->INTENSET  = (TIMER_INTENSET_COMPARE0_Set << TIMER_INTENSET_COMPARE0_Pos);
//        }
//        else
        {
            NRF_TIMER0->INTENSET = (TIMER_INTENSET_COMPARE0_Set << TIMER_INTENSET_COMPARE0_Pos) | 
                                   (TIMER_INTENSET_COMPARE1_Set << TIMER_INTENSET_COMPARE1_Pos);
        }
        NRF_TIMER0->CC[0]               = (TS_LEN_US - TS_SAFETY_MARGIN_US);//1000 - 500
        NRF_TIMER0->CC[1]               = (TS_LEN_US - TS_EXTEND_MARGIN_US);//1000 - 700
        NRF_TIMER0->BITMODE             = (TIMER_BITMODE_BITMODE_24Bit << TIMER_BITMODE_BITMODE_Pos);
        NRF_TIMER0->TASKS_START         = 1;
    

        NRF_RADIO->POWER                = (RADIO_POWER_POWER_Enabled << RADIO_POWER_POWER_Pos);

        NVIC_EnableIRQ(TIMER0_IRQn);
        
        m_total_timeslot_length = 0;
        
        timeslot_begin_handler();
        
        break;
    
    case NRF_RADIO_CALLBACK_SIGNAL_TYPE_TIMER0:
        if (NRF_TIMER0->EVENTS_COMPARE[0] &&
           (NRF_TIMER0->INTENSET & (TIMER_INTENSET_COMPARE0_Enabled << TIMER_INTENCLR_COMPARE0_Pos)))
        {
            SynTestRecord_t.time0_0_count ++;
            NRF_TIMER0->TASKS_STOP  = 1;
            NRF_TIMER0->EVENTS_COMPARE[0] = 0;
            (void)NRF_TIMER0->EVENTS_COMPARE[0];
            
            // This is the "timeslot is about to end" timeout
            timeslot_end_handler();
            // Schedule next timeslot
//            if (m_send_sync_pkt)
//            {
//                m_timeslot_req_normal.params.normal.distance_us = m_total_timeslot_length + m_timeslot_distance;
//                return (nrf_radio_signal_callback_return_param_t*) &m_rsc_return_sched_next_normal;
//            }
//            else
            {
                return (nrf_radio_signal_callback_return_param_t*) &m_rsc_return_sched_next_earliest;
            }
        }

        if (NRF_TIMER0->EVENTS_COMPARE[1] &&
           (NRF_TIMER0->INTENSET & (TIMER_INTENSET_COMPARE1_Enabled << TIMER_INTENCLR_COMPARE1_Pos)))
        {
            SynTestRecord_t.time0_1_count ++;
            NRF_TIMER0->EVENTS_COMPARE[1] = 0;
            (void)NRF_TIMER0->EVENTS_COMPARE[1];
            
            // This is the "try to extend timeslot" timeout
            
//            if (m_total_timeslot_length < (128000000UL - 5000UL - TX_LEN_EXTENSION_US) && !m_send_sync_pkt)
            if (m_total_timeslot_length < (12800000UL - 5000UL - TX_LEN_EXTENSION_US) && !m_send_sync_pkt)
            {
                // Request timeslot extension if total length does not exceed 128 seconds
                return (nrf_radio_signal_callback_return_param_t*) &m_rsc_extend;
            }
            else if (!m_send_sync_pkt)
            {
                NRF_LOG_INFO("timeslot timeout");

                // Don't do anything. Timeslot will end and new one requested upon the next timer0 compare. 
//                // Return with normal action request
//                m_timeslot_req_normal.params.normal.distance_us = m_total_timeslot_length + m_timeslot_distance;
//                return (nrf_radio_signal_callback_return_param_t*) &m_rsc_return_sched_next_normal;
            }
        }
        
        
        
    case NRF_RADIO_CALLBACK_SIGNAL_TYPE_RADIO:
        RADIO_IRQHandler();
        break;
    
    case NRF_RADIO_CALLBACK_SIGNAL_TYPE_EXTEND_FAILED:
        SynTestRecord_t.radio_extend_fail_count ++;
        // Don't do anything. Our timer will expire before timeslot ends
        return (nrf_radio_signal_callback_return_param_t*) &m_rsc_return_no_action;
    
    case NRF_RADIO_CALLBACK_SIGNAL_TYPE_EXTEND_SUCCEEDED:
        SynTestRecord_t.radio_extend_count ++;
        // Extension succeeded: update timer
        NRF_TIMER0->TASKS_STOP          = 1;
        NRF_TIMER0->EVENTS_COMPARE[0]   = 0;
        NRF_TIMER0->EVENTS_COMPARE[1]   = 0;
        NRF_TIMER0->CC[0]               += (TX_LEN_EXTENSION_US - 25);//1000
        NRF_TIMER0->CC[1]               += (TX_LEN_EXTENSION_US - 25);
        NRF_TIMER0->TASKS_START         = 1;
    
        // Keep track of total length
        m_total_timeslot_length += TX_LEN_EXTENSION_US;
        break;
    
    default:
        app_error_handler(MAIN_DEBUG, __LINE__, (const uint8_t*)__FILE__);
        break;
    };

    // Fall-through return: return with no action request
    return (nrf_radio_signal_callback_return_param_t*) &m_rsc_return_no_action;
}

static void update_radio_parameters()
{   
    // TX power
    NRF_RADIO->TXPOWER   = RADIO_TXPOWER_TXPOWER_0dBm   << RADIO_TXPOWER_TXPOWER_Pos;
    
    // RF bitrate
    NRF_RADIO->MODE      = RADIO_MODE_MODE_Nrf_2Mbit       << RADIO_MODE_MODE_Pos;
    
    // Fast startup mode
    NRF_RADIO->MODECNF0 = RADIO_MODECNF0_RU_Fast << RADIO_MODECNF0_RU_Pos;
    
    // CRC configuration
    NRF_RADIO->CRCCNF    = RADIO_CRCCNF_LEN_Three << RADIO_CRCCNF_LEN_Pos; 
    NRF_RADIO->CRCINIT = 0xFFFFFFUL;      // Initial value      
    NRF_RADIO->CRCPOLY = 0x11021UL;     // CRC poly: x^16+x^12^x^5+1
    
    // Packet format 
    NRF_RADIO->PCNF0 = (0 << RADIO_PCNF0_S0LEN_Pos) | (0 << RADIO_PCNF0_LFLEN_Pos) | (0 << RADIO_PCNF0_S1LEN_Pos);
    NRF_RADIO->PCNF1 = (RADIO_PCNF1_WHITEEN_Disabled        << RADIO_PCNF1_WHITEEN_Pos) |
                       (RADIO_PCNF1_ENDIAN_Big              << RADIO_PCNF1_ENDIAN_Pos)  |
                       (4                                   << RADIO_PCNF1_BALEN_Pos)   |
                       (sizeof(m_sync_pkt)                  << RADIO_PCNF1_STATLEN_Pos) |
                       (sizeof(m_sync_pkt)                  << RADIO_PCNF1_MAXLEN_Pos);
    NRF_RADIO->PACKETPTR = (uint32_t)&m_sync_pkt;
    
    // Radio address config
    NRF_RADIO->PREFIX0 = m_params.rf_addr[0];//0-3
    NRF_RADIO->BASE0   = (m_params.rf_addr[1] << 24 | m_params.rf_addr[2] << 16 | m_params.rf_addr[3] << 8 | m_params.rf_addr[4]);
    
    NRF_RADIO->TXADDRESS   = 0;
    NRF_RADIO->RXADDRESSES = (1 << 0);
    
    NRF_RADIO->FREQUENCY = m_params.rf_chn;
    NRF_RADIO->TXPOWER   = RADIO_TXPOWER_TXPOWER_Pos4dBm << RADIO_TXPOWER_TXPOWER_Pos;
    
    NRF_RADIO->EVENTS_END = 0;
    
    NRF_RADIO->INTENCLR = 0xFFFFFFFF;
    NRF_RADIO->INTENSET = RADIO_INTENSET_END_Msk;
    
    NVIC_EnableIRQ(RADIO_IRQn);
}

/**@brief IRQHandler used for execution context management. 
  *        Any available handler can be used as we're not using the associated hardware.
  *        This handler is used to stop and disable UESB
  */
static void timeslot_end_handler(void)
{
    uint32_t ppi_chn_0 = m_params.ppi_chns[0];
    uint32_t ppi_chn_1 = m_params.ppi_chns[1];
    uint32_t ppi_chn_2 = m_params.ppi_chns[2];

    NRF_RADIO->TASKS_DISABLE = 1;
    NRF_RADIO->INTENCLR      = 0xFFFFFFFF;

    NRF_PPI->CHENCLR = (1 << ppi_chn_0)|(1 << ppi_chn_1)|(1 << ppi_chn_2);

    m_total_timeslot_length = 0;
    m_radio_state           = RADIO_STATE_IDLE;
}

/**@brief IRQHandler used for execution context management. 
  *        Any available handler can be used as we're not using the associated hardware.
  *        This handler is used to initiate UESB RX/TX
  */
static void timeslot_begin_handler(void)
{
    update_radio_status();
}

/**@brief Function for handling the Application's system events.
 *
 * @param[in]   sys_evt   system event.
 */
void ts_on_sys_evt(uint32_t sys_evt, void * p_context)
{
    switch(sys_evt)
    {
        case NRF_EVT_FLASH_OPERATION_SUCCESS:
        case NRF_EVT_FLASH_OPERATION_ERROR:
            break;
        case NRF_EVT_RADIO_BLOCKED:
        case NRF_EVT_RADIO_CANCELED:
        {
            // Blocked events are rescheduled with normal priority. They could also
            // be rescheduled with high priority if necessary.
            uint32_t err_code = sd_radio_request((nrf_radio_request_t*) &m_timeslot_req_earliest);
            APP_ERROR_CHECK(err_code);

            m_blocked_cancelled_count++;
            
            break;
        }
        case NRF_EVT_RADIO_SIGNAL_CALLBACK_INVALID_RETURN:
            NRF_LOG_ERROR("NRF_EVT_RADIO_SIGNAL_CALLBACK_INVALID_RETURN\r\n");
            app_error_handler(MAIN_DEBUG, __LINE__, (const uint8_t*)__FILE__);
            break;
        case NRF_EVT_RADIO_SESSION_CLOSED:
            {
                m_timeslot_session_open = false;
                
                NRF_LOG_INFO("NRF_EVT_RADIO_SESSION_CLOSED\r\n");
                gSystemSt_t.mSynTimEnable = SYS_BLE_TIME_SYN_DISABLE;
            }
        
            break;
        case NRF_EVT_RADIO_SESSION_IDLE:
        {
            NRF_LOG_INFO("NRF_EVT_RADIO_SESSION_IDLE\r\n");
            
            uint32_t err_code = sd_radio_session_close();
            APP_ERROR_CHECK(err_code);
            break;
        }
        default:
            // No implementation needed.
            //NRF_LOG_INFO("Event: 0x%08x\r\n", sys_evt);
            break;
    }
}


static inline uint64_t get_local_rf_time_raw(ts_params_t* p_param)
{
    uint32_t us = read_hf0_cc2_capture(p_param);
    uint32_t ms = read_hf1_cc2_capture(p_param);
    uint64_t stamp_local = ((uint64_t)ms)*TIMER_US_MAX_TARGET + us;
    return stamp_local;
}

static inline void timestamp_push_data(ts_params_t* p_param, linear_fit_buffer_t* p_buffer, uint64_t* p_remote, uint64_t* p_local)
{
    double stamp_local = (double)*p_local;
    double stamp_remote = (double)*p_remote;
    
    linear_fit_element_t data;
    data.x = stamp_local;
    data.y = stamp_remote;
    linear_fit_push_data(p_buffer, &data);
}

static inline double GetParamAccuracyUs(ts_params_t* p_param, uint64_t* p_remote, uint64_t* p_local)
{
    double stamp_local = (double)*p_local;
    double stamp_remote = (double)*p_remote;

    double stamp_fixed_local = fix_stamp_offset_ppm(p_param, stamp_local);
    double stamp_delta = stamp_remote - stamp_fixed_local;

    return stamp_delta/16.0;
}


static void sync_timer_offset_exec(sync_snapshot_t* p_snapshot)
{
    static uint8_t fail_count = 0;
    double accuracyUs = p_snapshot->accuracyUs;
    
    bool SetSyncParameterRequired_0 = true;
    bool ResetSyncRequired_0 = true;

    int counter_0 = timestamp_sync_linear_fit_buffer_0.counter;
    if(counter_0 <= GenParamCounter*0.5)
    {
        ResetSyncRequired_0 = false;
    }
    
    if(SetSyncParameterRequired_0
        &&(accuracyUs >= -2)
        &&(accuracyUs <= 2))
    {
        //re-calc skipped
        NRF_LOG_INFO("skipped with accuracy:%d ns", (int32_t)(accuracyUs*1000));
    }
    else
    {
        if(SetSyncParameterRequired_0)
        {
            linear_fit_result_t result;
            linear_fit_get_result(&timestamp_sync_linear_fit_buffer_0, &result);
            m_params.stamp_offset = result.a0;
            m_params.stamp_ppm = (result.a1-1)*1e6;
            NRF_LOG_INFO("[%d], offset:%d, ppm*1000:%d\r\n", timestamp_sync_linear_fit_buffer_0.counter, 
                    (int32_t)m_params.stamp_offset, 
                    (int32_t)(m_params.stamp_ppm*1e3));
//            ResetSyncRequired_0 = true;
            
            accuracyUs = GetParamAccuracyUs(&m_params, 
                &p_snapshot->stamp_remote, 
                &p_snapshot->stamp_local);
        }
    }

    if(((accuracyUs > 600)
        ||(accuracyUs < -600))
        &&(m_params.stamp_offset != 0))
    {
        ResetSyncRequired_0 = true;
        m_params.stamp_offset = 0;
        fail_count ++;
        if(fail_count>10) {
            fail_count = 0;
            NRF_LOG_INFO("us=%.1f\r\n", accuracyUs);
        }

    }

    if(ResetSyncRequired_0)
    {
        linear_fit_init(&timestamp_sync_linear_fit_buffer_0);
    }

    isUpdateProcessing = false;
}


static void sync_timer_offset_compensate(void)
{
    if(isUpdateProcessing)
    {
        return;
    }
//    dump_remote_and_local_stamp(&m_params);
    uint64_t stamp_local = get_local_rf_time_raw(&m_params);
    uint64_t stamp_remote = get_rf_remote_stamp(&m_sync_pkt.stamp);
    sync_snapshot_t snapshot;

    bool SetSyncParameterRequired_0 = false;

    timestamp_push_data(&m_params, 
            &timestamp_sync_linear_fit_buffer_0, 
            &stamp_remote, 
            &stamp_local);
    int counter_0 = timestamp_sync_linear_fit_buffer_0.counter;

    if(counter_0 == GenParamCounter*0.1)
    {
       SetSyncParameterRequired_0 = true; 
    }

    if(((counter_0 % GenParamCounter) == 0)
        &&(counter_0 != 0))
    {
        SetSyncParameterRequired_0 = true;
    }

    
    if(SetSyncParameterRequired_0)
    {
        double accuracyUs = GetParamAccuracyUs(&m_params, 
                &stamp_remote, 
                &stamp_local);
        
        if( (accuracyUs >= -200)
            &&(accuracyUs <= 200)
        )
        {
            //re-calc skipped
//            TIME_SYNC_LOG("skipped with accuracy:%f us", accuracyUs);
        }
        else
        {
            snapshot.accuracyUs = accuracyUs;
            snapshot.stamp_local = stamp_local;
            snapshot.stamp_remote = stamp_remote;
            
            isUpdateProcessing = true;
            uint32_t err_code = app_sched_event_put(&snapshot, sizeof(sync_snapshot_t), (app_sched_event_handler_t)sync_timer_offset_exec);
            if(err_code != NRF_SUCCESS)
            {
                isUpdateProcessing = false;
            }
        }
    }
}

static uint32_t ts_init(const ts_params_t * p_params)
{
    uint32_t err_code;
     if (m_timeslot_session_open)
    {
        err_code = sd_radio_session_close();
        if((NRF_SUCCESS == err_code) ||(NRF_ERROR_BUSY == err_code))
        {
            while(m_timeslot_session_open);  //wait session close evt
        }
        m_params.high_freq_timer[0]->TASKS_STOP  = 1;
        m_params.high_freq_timer[0]->TASKS_CLEAR = 1;
             
        m_params.high_freq_timer[1]->TASKS_STOP  = 1;
        m_params.high_freq_timer[1]->TASKS_CLEAR = 1;
        
    }
    memcpy(&m_params, p_params, sizeof(ts_params_t));
    
    if (m_params.high_freq_timer[0] == 0)
    {
        // TODO: Check all params
        return NRF_ERROR_INVALID_PARAM;
    }

    m_params.stamp_offset = 0;
    m_params.stamp_ppm = 0;
    
    return NRF_SUCCESS;
}

static uint32_t ts_enable(void)
{
    uint32_t err_code;
    
    if (m_timeslot_session_open)
    {  
        return NRF_ERROR_INVALID_STATE;
    }
    
    err_code = sd_clock_hfclk_request();
    if (err_code != NRF_SUCCESS)
    {
        return err_code;
    }
    
    err_code |= sd_power_mode_set(NRF_POWER_MODE_CONSTLAT);
    if (err_code != NRF_SUCCESS)
    {
        return err_code;
    }

    err_code = sd_radio_session_open(radio_callback);
    if (err_code != NRF_SUCCESS)
    {
        return err_code;
    }

    err_code = sd_radio_request(&m_timeslot_req_earliest);
    if (err_code != NRF_SUCCESS)
    {
        return err_code;
    }
    
    m_blocked_cancelled_count  = 0;
    m_send_sync_pkt            = false;
    m_radio_state              = RADIO_STATE_IDLE;

    config_timer_hf0(&m_params);
    config_timer_hf1(&m_params);
    config_ppi_swi(&m_params);
    
    config_swi_set_next_swi(&m_params, TIMER_US_MAX_TARGET/2);
    config_swi_enable(&m_params);

    m_timeslot_session_open    = true;

    return NRF_SUCCESS;
}

void ts_disable(void)
{
    uint32_t err_code;
    if (m_timeslot_session_open)
    {
        err_code = sd_radio_session_close();
        if((NRF_SUCCESS == err_code) ||(NRF_ERROR_BUSY == err_code))
        {
            while(m_timeslot_session_open);  //wait session close evt
        }
        m_params.high_freq_timer[0]->TASKS_STOP  = 1;
        m_params.high_freq_timer[0]->TASKS_CLEAR = 1;
             
        m_params.high_freq_timer[1]->TASKS_STOP  = 1;
        m_params.high_freq_timer[1]->TASKS_CLEAR = 1;
        
    }
    sd_power_mode_set(NRF_POWER_MODE_LOWPWR);
}

void SWI3_EGU3_IRQHandler(void)
{
    if (NRF_EGU3->EVENTS_TRIGGERED[0] != 0)
    {
        NRF_EGU3->EVENTS_TRIGGERED[0] = 0;
        config_swi_disable(&m_params);
        config_ppi_user_side(&m_params, 0);
        if(callback_when_swi != NULL)
        {
            callback_when_swi();
        }
    }
}
static void GenerateConnChnlTab(uint8_t *apChnnlTab,uint8_t *HostMac,uint8_t  *RfChanel)





{

    uint8_t channel_tab_Index[RFPROTO_MAX_CHANNEL_TAB_SIZE];
    uint8_t i = 0u;
    uint8_t j = 0u;
    uint8_t RngData = 0u;
    uint16_t cnt =0;
    uint32_t sum =0;
    

    memset(channel_tab_Index,0xFF,RFPROTO_MAX_CHANNEL_TAB_SIZE);
    for(i = 0; i < RFPROTO_MAX_CHANNEL_TAB_SIZE ;)
    {
        RngData = (HostMac[i] % RFPROTO_MAX_CHANNEL_TAB_SIZE);
        cnt =0;
        for(j = 0u; j < RFPROTO_MAX_CHANNEL_TAB_SIZE; j++)
        {
            if(channel_tab_Index[j] == RngData)
            {
                cnt++;
                RngData = ((HostMac[i] +cnt) % RFPROTO_MAX_CHANNEL_TAB_SIZE);
                j =0;
                continue;
            }
            else if(channel_tab_Index[j] == 0xFF)
            {
                channel_tab_Index[i] = RngData;
                apChnnlTab[i] = Conn_Default_Channel_tab[RngData];
                i++;
                break;
            }
            else
            {
                //do nothing
            }
        }
    }
    sum = HostMac[0] +  HostMac[1] +HostMac[2] +HostMac[3] +HostMac[4] +HostMac[5];
    RngData = (sum % RFPROTO_MAX_CHANNEL_TAB_SIZE);
    *RfChanel = apChnnlTab[RngData];
















}

/*function define*/
static void time_sync_enable(void)
{
    uint32_t       err_code;    
    err_code = ts_enable();
    APP_ERROR_CHECK(err_code);
}


uint64_t get_stamp64_sync_finshed(void)
{
    return read_stamp_fixed(&m_params);
}

static void time_sync_init(uint8_t *rf_address,uint8_t rf_channel)
{
    uint32_t       err_code;

    ts_params_t    ts_params;
     memset(&ts_params,0,sizeof(ts_params));
    ts_params.high_freq_timer[0] = NRF_TIMER2;
    ts_params.high_freq_timer[1] = NRF_TIMER3;
    ts_params.high_freq_timer[2] = NRF_TIMER4;
    ts_params.ppi_chns[0]     = 0;
    ts_params.ppi_chns[1]     = 1;
    ts_params.ppi_chns[2]     = 2;
    ts_params.ppi_chns[3]     = 3;
    ts_params.ppi_chns[4]     = 4;
    ts_params.ppi_chns[5]     = 5;
    ts_params.rf_chn          = rf_channel; /* For testing purposes */

    ts_params.egu             = NRF_EGU3;
    ts_params.egu_irq_type    = SWI3_EGU3_IRQn;
    
    memcpy(ts_params.rf_addr, rf_address, 5);
    
    err_code = ts_init(&ts_params);
    APP_ERROR_CHECK(err_code);
}



void time_sync_start(uint8_t* host_mac)
{
    uint8_t rf_channel;
    uint8_t        gRngFreqTab[RFPROTO_MAX_CHANNEL_TAB_SIZE];
    uint32_t       err_code;
    uint8_t       sum =0;
    uint8_t        rf_address[5] = 
    {
        //0xDE, 0xAD, 0xBE, 0xEF, 0x19
        0x58, 0x69, 0x6D, 0x6D, 0x20
    };
    GenerateConnChnlTab(gRngFreqTab,host_mac,&rf_channel);
    if(host_mac != NULL)
    {
        memcpy(rf_address,host_mac,4u);
        for(uint8_t i=0; i <6u; i++)
        {
            sum ^= host_mac[i];
        }
        rf_address[4] = sum;
    }
    DEBUG_PRINTF("time Syn Rf table %d,%d,%d,%d,%d,%d\r\n",gRngFreqTab[0],gRngFreqTab[1],gRngFreqTab[2],gRngFreqTab[3],gRngFreqTab[4],gRngFreqTab[5]);
    DEBUG_PRINTF("time Syn Rf channel=%d\r\n",rf_channel); 
    DEBUG_PRINTF("time Syn Rf Mac(MSB) %02x,%02x,%02x,%02x,%02x\r\n",rf_address[4],rf_address[3],rf_address[2],rf_address[1],rf_address[0]); 
    
    time_sync_init(&rf_address[0], rf_channel);

    time_sync_enable();
}
