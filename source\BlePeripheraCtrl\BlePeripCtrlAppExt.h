/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : BlePeripCtrlAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2018/07/25    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef BLE_PERIP_CTRL_APP_EXT_H
#define BLE_PERIP_CTRL_APP_EXT_H

/*Include files*/
#include "XimStdint.h"
#include "includes.h"
/*declaration range definition*/
#ifdef BLE_PERIP_CTRL_DRV_C 
  #define BLE_PERIP_CTRL_APP_EXT 
#else 
  #define BLE_PERIP_CTRL_APP_EXT extern 
#endif 

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */


/* data type definiton  */
typedef void (* ble_Evt_handler_t) (ble_evt_t const * p_ble_evt );
typedef void (* ble_adv_Evt_handler_t) (ble_adv_evt_t   p_ble_adv_evt );  
typedef void (* ble_Params_updata_Evt_handler_t) (const ble_gap_conn_params_t  * p_params ,uint16_t conn_handle);    
/* variable definition */
BLE_PERIP_CTRL_APP_EXT   uint16_t   m_conn_handle;
 
/* function declaration */
BLE_PERIP_CTRL_APP_EXT void gatt_init(void);
BLE_PERIP_CTRL_APP_EXT void advertising_init(uint16_t  uudis,uint8_t *CtrolType,ble_adv_Evt_handler_t  bleAdvEvtCallback);
BLE_PERIP_CTRL_APP_EXT void advertising_updata(uint16_t  uudis,uint8_t *CtrolType);
BLE_PERIP_CTRL_APP_EXT void gap_params_init(uint8_t *pDeviceName,uint8_t NameLength,uint8_t *pMac);
BLE_PERIP_CTRL_APP_EXT void conn_params_init(ble_Params_updata_Evt_handler_t  bleParamUpdataCallback);
BLE_PERIP_CTRL_APP_EXT void advertising_start(void);
BLE_PERIP_CTRL_APP_EXT void Ble_dis_service_init(ble_srv_utf8_str_t manufact_name_t,\
                          ble_srv_utf8_str_t model_num_t,\
                          ble_srv_utf8_str_t serial_num_t,\
                          ble_srv_utf8_str_t hw_rev_t, \
                          ble_srv_utf8_str_t fw_rev_t, \
                         ble_srv_utf8_str_t sw_rev_t);
BLE_PERIP_CTRL_APP_EXT void bas_service_init(void);
BLE_PERIP_CTRL_APP_EXT void health_thermometer_service(void);
BLE_PERIP_CTRL_APP_EXT uint32_t Ble_xim_service_init(ble_xim_data_handler_t  BleServiceEvtHandler,uint8_t isOldCalibEn,uint8_t isSysCfgEn);
BLE_PERIP_CTRL_APP_EXT void QueuedModule_init(void);
BLE_PERIP_CTRL_APP_EXT void ble_stack_init(ble_Evt_handler_t  BleEvtHandler);
BLE_PERIP_CTRL_APP_EXT void battery_level_update(uint8_t  battery_level);
BLE_PERIP_CTRL_APP_EXT void battery_temperature_update(float temperature_degree);
BLE_PERIP_CTRL_APP_EXT uint32_t XimService_data_send(uint16_t serviceBaseUUID,uint8_t   * p_data,uint16_t  length);
BLE_PERIP_CTRL_APP_EXT uint16_t gerCurrentMtuSize(void);
BLE_PERIP_CTRL_APP_EXT void conn_params_updata_start(uint8_t level);
BLE_PERIP_CTRL_APP_EXT uint8_t get_conn_params_level(void);
BLE_PERIP_CTRL_APP_EXT void set_conn_params_level(uint8_t level);
BLE_PERIP_CTRL_APP_EXT void Ble_Dfu_Bootloader_enter_prepare(void);

#endif   /* end of header file */
