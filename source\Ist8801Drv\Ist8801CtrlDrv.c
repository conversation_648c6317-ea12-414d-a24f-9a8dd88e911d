/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Ist8801CtrDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef IST8801_CTRL_DRV_C
#define IST8801_CTRL_DRV_C
#endif

/* include files */
#include "Ist8801CtrlDrv.h"

/* static variable definition */

/* static function declaration */

/* static function definition */

/* public function definition */
void ist8801_Set_Sensitivity(uint8_t Halltype,uint8_t dynamic_range,uint8_t bit_resolution)
{
    uint8_t tempValue =0;
    uint8_t backup_odr = 0;
    uint8_t Addr = 0;
    
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_CNTL1,&backup_odr, 1);
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_CNTL1, &tempValue,1);
    I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_CNTL1, &tempValue,1);
    
    tempValue |= 0x04;
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_IFCNTL, &tempValue,1u);
 
    tempValue = (dynamic_range ) | bit_resolution;
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_IFCNTL, &tempValue,1u);
 
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_CNTL1,&backup_odr, 1);
}


void ist8801_Interrupt_Mode( uint8_t Halltype, uint8_t status )
{
    uint8_t tempValue;
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    
    I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_INTSRS,&tempValue, 1);
    if( status == HALL_IST_ENABLE )
    {
        tempValue |= 0x80;
    }
    else 
    {
        tempValue &= 0x7F;
    }
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_INTSRS, &tempValue,1u);                //interrupt on/off
}

void ist8801_Interrupt_Type( uint8_t Halltype, uint8_t status )
{
    uint8_t tempValue;
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    
    I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_INTSRS, &tempValue, 1);
    if( status == HALL_IST_ENABLE )
    {
        tempValue |= 0x10;            //HS[15:0] > LTH[15:0] and HS[15:0] < HTH[15:0]     
    }
    else 
    {
        tempValue &= 0xEF;            //HS[15:0] < LTH[15:0] or HS[15:0] > HTH[15:0] 
    }
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_INTSRS,&tempValue,1u);     
}

void ist8801_SetODR( uint8_t Halltype, uint8_t value)
{
    uint8_t tempValue = 0;
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr,IST8801_REG_CNTL1, &tempValue,1u);
    
    I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr,IST8801_REG_IFCNTL, &tempValue, 1);
    
    tempValue |= 0x04;
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_IFCNTL, &tempValue,1u);

    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_CNTL1, &value,1u);
}

void ist8801_Set_Threshold_Level(uint8_t Halltype, uint8_t status, int16_t value )
{
    uint8_t tempBuf;
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    
    if( status == IST8801_LOW_THRESHOLD )
    {
        tempBuf = value & 0x00FF;
        I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_LTHL, &tempBuf,1u);    
        tempBuf = ( value & 0xFF00 )>>8;
        I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_LTHH, &tempBuf,1u);    
    }
    else if( status == IST8801_HIGH_THRESHOLD )
    {
        tempBuf = value & 0x00FF;
        I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_HTHL,&tempBuf,1u);    
        tempBuf = ( value & 0xFF00 )>>8;
        I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_HTHH,&tempBuf,1u); 
    }
}


bool ist8801_Init(uint8_t Halltype,int16_t LThreshold,int16_t HThreshold)
{
    uint8_t tempData = 0u;
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    HALL_CTRL_DELAY_MS(50);  //sensor initial time : 50 milliseconds
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_ACTR, &tempData,1u);
    
    tempData = 0x01;
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_SRST,&tempData,1u);   //Soft-Reset
    HALL_CTRL_DELAY_MS(20);  
    
    //read device id
    I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_WMI, &tempData,1u);
    if(tempData != IST8801_DEVICE_ID)
    {
        HALL_CTRL_PRINTF("ist8801 chip id=0x%x err",tempData) ;
        return false;
    }
    HALL_CTRL_PRINTF("ist8801 chip id=0x%x",tempData) ;

    // tempData = 0x4<<1 | 0x02<<5;
    // I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr,IST8801_REG_CNTL2, &tempData,1u);
     
    tempData = 0x40;
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr,IST8801_REG_PERSINT, &tempData,1u);

    //set interrupt mode
    ist8801_Interrupt_Mode(Halltype,HALL_IST_DISABLE);
    ist8801_Interrupt_Type(Halltype,HALL_IST_ENABLE);//HS[15:0] > LTH[15:0] and HS[15:0] < HTH[15:0]     
 
    //Init Setting
    //set threshold level
    ist8801_Set_Threshold_Level(Halltype, IST8801_LOW_THRESHOLD, -100);
    ist8801_Set_Threshold_Level(Halltype, IST8801_HIGH_THRESHOLD, 100);
 
     //0X0D SET RANGE
     ist8801_Set_Sensitivity(Halltype, GAIN_2_TIME,ADC_RES_16_BIT);

    //TESTPIN Setting
     //default  0x00
 
    //set interrupt on
    ist8801_Interrupt_Mode( Halltype ,HALL_IST_ENABLE);

    //set ODR
    ist8801_SetODR( Halltype ,IST8801_CNTL1_SETTING);
    return true;
}

bool ist8801_GetData(uint8_t Halltype,int16_t *data)
{
    static int16_t  iTrigLastdata = 0;
    static int16_t  iGripLastdata = 0;
    int16_t *apLastData = &iTrigLastdata;
    uint8_t tempData = 0;
    uint8_t raw[2] = {0};
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
        apLastData = &iTrigLastdata;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
        apLastData = &iGripLastdata;
    }
    
    if(I2C_RW_ST_SUCCESS == I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_STAT1, &tempData, 1))
    {
        if( tempData & 0x01 )        //must read register 0x11 and 0x12
        {
            //Combine High Low byte
            if(I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_DATAL, raw, 2) == I2C_RW_ST_SUCCESS)
            {
                *data = (((int16_t)raw[1]) << 8) | raw[0];
                *apLastData = *data ;
            }
            else
            {
                *data = *apLastData;
                return false;
            }
        }
        else
        {
            *data = *apLastData;
        }
        return true;
    }
    else
    {
        return false;
    }
}

void ist8801_Interrupt_Clear(uint8_t Halltype)
{
    uint8_t tempValue =0;
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    //interrupt clear
    I2C_ReadOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_PERSINT, &tempValue, 1);
    tempValue |= 0x01;
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_PERSINT, &tempValue,1);            
    tempValue &= 0xFE;
    I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_PERSINT, &tempValue,1);    
}

void ist8801_SuspendMode(uint8_t Halltype, uint8_t status )
{
    uint8_t tempValue =0;
    uint8_t Addr = 0;
    if(Halltype == HALL_CTRL_TRIG)
    {
        Addr = IST8801_I2C_ADDR_TRIG;
    }
    else
    {
        Addr = IST8801_I2C_ADDR_GRIP; 
    }
    if( status == HALL_IST_ENABLE )
    {
        //switch suspend mode         
        //setting standby mode
        ist8801_SetODR( Halltype ,IST8801_CNTL1_STANDBY);
        //setting suspend mode
        tempValue = 0x02;
        I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_ACTR, &tempValue,1u);
        HALL_CTRL_DELAY_MS(4);   //delay 4 ms;
    }
    else if( status == HALL_IST_DISABLE )
    {
        //switch standby mode 
        //setting suspend mode
        tempValue = 0x00;
        I2C_WriteOneReg(IST8801_I2C_HANDLE,Addr, IST8801_REG_ACTR,&tempValue,1u);
        HALL_CTRL_DELAY_MS(4);   //delay 4 ms;
        //setting operation mode
        ist8801_SetODR( Halltype ,IST8801_CNTL1_SETTING);
    }
}

/***********************************************END**********************************************/

