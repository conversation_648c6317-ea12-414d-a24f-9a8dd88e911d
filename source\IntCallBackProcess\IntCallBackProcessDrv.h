/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : IntCallBackProcessDrv.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2018/05/29    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef CALL_BACK_PROCESS_DRV_H
#define CALL_BACK_PROCESS_DRV_H

/*Include files*/
#include "IntCallBackProcessAppExt.h"

/* support module */
#include "includes.h"
#include "UartRWAppExt.h"
/*****following definitions can not be used outside this driver******/
/* macro definition */
/*Camera command number*/




/*ble pair timeout*/

/* data type definiton  */

#endif /* End of header file */
