/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : Main.C
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/04/13    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/


#ifndef MAIN_DRV_C
#define MAIN_DRV_C
#endif

/********************************************************************************
**  Include files
********************************************************************************/
#include "includes.h"

/* static variable definition */

/* static function declaration */

/* static function definition */
int main(void)
{
    BspInit();
    Task_StartUp(NULL);
    while (1) /* Should Never Get Here.  */
    {                                            
        TaskStartScheduler();
    }
}
/***********************************************END**********************************************/
