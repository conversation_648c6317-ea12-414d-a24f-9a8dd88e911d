/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : BleDisCtrlDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2018/07/25    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef BLE_DIS_CTRL_DRV_C
#define BLE_DIS_CTRL_DRV_C
#endif

/* include files */
#include "BlePeripCtrlDrv.h"

/* static function declaration */


/* static variable definition */
const ble_gap_conn_params_t  arr_conn_params_cfg[XIM_CONN_PARAMS_UPDATA_LEVEL]=
{
    {
        MSEC_TO_UNITS(7.5, UNIT_1_25_MS),
        MSEC_TO_UNITS(10, UNIT_1_25_MS),
        SLAVE_LATENCY,
        CONN_SUP_TIMEOUT
    },
    {
        MSEC_TO_UNITS(7.5, UNIT_1_25_MS),
        MSEC_TO_UNITS(12.5, UNIT_1_25_MS),
        SLAVE_LATENCY,
        CONN_SUP_TIMEOUT
    },
    {
        MSEC_TO_UNITS(12.5, UNIT_1_25_MS),
        MSEC_TO_UNITS(15, UNIT_1_25_MS),
        SLAVE_LATENCY,
        CONN_SUP_TIMEOUT
    },
    {
        MSEC_TO_UNITS(15, UNIT_1_25_MS),
        MSEC_TO_UNITS(48, UNIT_1_25_MS),
        SLAVE_LATENCY,
        CONN_SUP_TIMEOUT
    }
} ;
 BLE_ADVERTISING_DEF(m_advertising);                                                 /**< Advertising module instance. */
static   uint8_t  gConnParamLevel = XIM_CONN_PARAMS_UPDATA_LEVEL; // 12.5- -48 ms
/* static function definition */
 ble_adv_Evt_handler_t  iBleAdvEvtCallbackHandler = NULL;  
ble_Params_updata_Evt_handler_t  iBleParamsUpdataHandler = NULL;
/* public function definition */
/**@brief Function for assert macro callback.
 *
 * @details This function will be called in case of an assert in the SoftDevice.
 *
 * @warning This handler is an example only and does not fit a final product. You need to analyse
 *          how your product is supposed to react in case of Assert.
 * @warning On assert from the SoftDevice, the system can only recover on reset.
 *
 * @param[in] line_num    Line number of the failing ASSERT call.
 * @param[in] p_file_name File name of the failing ASSERT call.
 */
void assert_nrf_callback(uint16_t line_num, const uint8_t * p_file_name)
{
    app_error_handler(DEAD_BEEF, line_num, p_file_name);
}
 /**@brief Function for handling advertising events.
 *
 * @details This function will be called for advertising events which are passed to the application.
 *
 * @param[in] ble_adv_evt  Advertising event.
 */
static void on_adv_evt(ble_adv_evt_t ble_adv_evt)
{
    switch (ble_adv_evt)
    {
        case BLE_ADV_EVT_FAST:
             //NRF_LOG_INFO("Fast advertising.");
            break;
        case BLE_ADV_EVT_IDLE:
            
            break;
        default:
            break;
    }
    if(iBleAdvEvtCallbackHandler != NULL)
    {
        iBleAdvEvtCallbackHandler(ble_adv_evt);
    }
}
/**@brief Function for handling an event from the Connection Parameters Module.
 *
 * @details This function will be called for all events in the Connection Parameters Module
 *          which are passed to the application.
 *
 * @note All this function does is to disconnect. This could have been done by simply setting
 *       the disconnect_on_fail config parameter, but instead we use the event handler
 *       mechanism to demonstrate its use.
 *
 * @param[in] p_evt  Event received from the Connection Parameters Module.
 */
static void on_conn_params_evt(ble_conn_params_evt_t * p_evt)
{
    uint32_t err_code;

    if (p_evt->evt_type == BLE_CONN_PARAMS_EVT_FAILED)
    {
        if(gConnParamLevel <3u)
        {
            conn_params_updata_start(gConnParamLevel +1u);
        }
        else
        {
             err_code = sd_ble_gap_disconnect(m_conn_handle, BLE_HCI_CONN_INTERVAL_UNACCEPTABLE);
             NRF_LOG_INFO("all conn_params change fail disconnect\n",err_code);
             APP_ERROR_CHECK(err_code);
        }
    }
    else
    {
        if(get_conn_params_level() == XIM_CONN_PARAMS_UPDATA_LEVEL)  //init params
        {
            conn_params_updata_start(0); //  best config
        }
        else
        {
            NRF_LOG_INFO("conn_params change ok level is=%x\n",gConnParamLevel);
            if(iBleParamsUpdataHandler != NULL)
            {
                iBleParamsUpdataHandler(&arr_conn_params_cfg[gConnParamLevel],p_evt->conn_handle);
            }
        }
    }
}
  static void disconnect(uint16_t conn_handle, void * p_context)
{
    UNUSED_PARAMETER(p_context);

    ret_code_t err_code = sd_ble_gap_disconnect(conn_handle, BLE_HCI_REMOTE_USER_TERMINATED_CONNECTION);
    if (err_code != NRF_SUCCESS)
    {
        NRF_LOG_WARNING("Failed to disconnect connection. Connection handle: %d Error: %d", conn_handle, err_code);
    }
    else
    {
        NRF_LOG_DEBUG("Disconnected connection handle %d", conn_handle);
    }
}

static void advertising_config_get(ble_adv_modes_config_t * p_config)
{
    memset(p_config, 0, sizeof(ble_adv_modes_config_t));

    p_config->ble_adv_fast_enabled  = true;
    p_config->ble_adv_fast_interval = APP_ADV_INTERVAL;
    p_config->ble_adv_fast_timeout  = APP_ADV_DURATION;
}
/**@brief Function for handling errors from the Connection Parameters module.
 *
 * @param[in] nrf_error  Error code containing information about what went wrong.
 */
static void conn_params_error_handler(uint32_t nrf_error)
{
    APP_ERROR_HANDLER(nrf_error);
}
/**@brief Function for initializing the Advertising functionality.
 */
void advertising_init(uint16_t  uudis,uint8_t *CtrolType,ble_adv_Evt_handler_t  bleAdvEvtCallback)
{
    uint32_t               err_code;
    ble_advertising_init_t init;
    iBleAdvEvtCallbackHandler = bleAdvEvtCallback;
    ble_uuid_t adv_uuids[] =
    {
        {uudis, BLE_UUID_TYPE_BLE}
    };
    uint8_t xim_manuf_data[] = 
    {
        0x00, //scene ID
        'X', 'i','m','m','s','1','2',
    };
     xim_manuf_data[6] = CtrolType[0];
     xim_manuf_data[7] = CtrolType[1];
     ble_advdata_manuf_data_t xim_adv_data = 
     {
        .company_identifier = XIM_COMPANY_ID,
        .data= {sizeof(xim_manuf_data), &xim_manuf_data[0]},
     };
    memset(&init, 0, sizeof(init));

    init.advdata.include_appearance = false;
    init.advdata.flags              = BLE_GAP_ADV_FLAGS_LE_ONLY_LIMITED_DISC_MODE;
    init.advdata.uuids_complete.uuid_cnt = sizeof(adv_uuids) / sizeof(adv_uuids[0]);
    init.advdata.uuids_complete.p_uuids  = adv_uuids;
    init.advdata.p_manuf_specific_data = (ble_advdata_manuf_data_t*)&xim_adv_data;

    init.srdata.name_type = BLE_ADVDATA_FULL_NAME;
    init.srdata.include_appearance = true;
    
    
    init.config.ble_adv_fast_enabled  = true;
    init.config.ble_adv_fast_interval = APP_ADV_INTERVAL;
    init.config.ble_adv_fast_timeout  = APP_ADV_DURATION;
    init.evt_handler = on_adv_evt;

    err_code = ble_advertising_init(&m_advertising, &init);
    APP_ERROR_CHECK(err_code);

    ble_advertising_conn_cfg_tag_set(&m_advertising, APP_BLE_CONN_CFG_TAG);
}

void advertising_updata(uint16_t  uudis,uint8_t *CtrolType)
{
    uint32_t               err_code;
    ble_advertising_init_t  init;
    ble_uuid_t adv_uuids[] =
    {
        {uudis, BLE_UUID_TYPE_BLE}
    };
    uint8_t xim_manuf_data[] = 
    {
        0x00, //scene ID
        'X', 'i','m','m','s','1','2',
    };
    xim_manuf_data[6] = CtrolType[0];
    xim_manuf_data[7] = CtrolType[1];
     ble_advdata_manuf_data_t xim_adv_data = 
     {
        .company_identifier = XIM_COMPANY_ID,
        .data= {sizeof(xim_manuf_data), &xim_manuf_data[0]},
     };
    memset(&init, 0, sizeof(init));

    init.advdata.include_appearance = false;
    init.advdata.flags              = BLE_GAP_ADV_FLAGS_LE_ONLY_LIMITED_DISC_MODE;
    init.advdata.uuids_complete.uuid_cnt = sizeof(adv_uuids) / sizeof(adv_uuids[0]);
    init.advdata.uuids_complete.p_uuids  = adv_uuids;
    init.advdata.p_manuf_specific_data = (ble_advdata_manuf_data_t*)&xim_adv_data;

    init.srdata.name_type = BLE_ADVDATA_FULL_NAME;
    init.srdata.include_appearance = true;
    
    init.config.ble_adv_fast_enabled  = true;
    init.config.ble_adv_fast_interval = APP_ADV_INTERVAL;
    init.config.ble_adv_fast_timeout  = APP_ADV_DURATION;
    init.evt_handler = on_adv_evt;

    err_code = ble_advertising_advdata_update(&m_advertising, &init.advdata,&init.srdata);
    APP_ERROR_CHECK(err_code);
}

/**@brief Function for the GAP initialization.
 *
 * @details This function will set up all the necessary GAP (Generic Access Profile) parameters of
 *          the device. It also sets the permissions and appearance.
 */
void gap_params_init(uint8_t *pDeviceName,uint8_t NameLength,uint8_t *pMac)
{
    static ble_gap_addr_t address_now;
    
    uint32_t                err_code;
    ble_gap_conn_params_t   gap_conn_params;
    ble_gap_conn_sec_mode_t sec_mode;

    BLE_GAP_CONN_SEC_MODE_SET_OPEN(&sec_mode);

    err_code = sd_ble_gap_device_name_set(&sec_mode,
                                          (const uint8_t *) pDeviceName,
                                          NameLength);
    APP_ERROR_CHECK(err_code);
    err_code = sd_ble_gap_appearance_set(BLE_APPEARANCE_XIM_COBRA02);
    APP_ERROR_CHECK(err_code);
                                          
    err_code = sd_ble_gap_addr_get(&address_now);
    address_now.addr_type = BLE_GAP_ADDR_TYPE_PUBLIC;
    memcpy(address_now.addr,pMac,6u);
                                          
    ble_gap_privacy_params_t privacy_params = {0};
    privacy_params.privacy_mode = BLE_GAP_PRIVACY_MODE_OFF;
    privacy_params.private_addr_type = BLE_GAP_ADDR_TYPE_PUBLIC;
        
    err_code = sd_ble_gap_addr_set(&address_now);
    APP_ERROR_CHECK(err_code);
        
    err_code = sd_ble_gap_privacy_set(&privacy_params);
    if(err_code == BLE_ERROR_GAP_INVALID_BLE_ADDR)
    {
        DEBUG_PRINTF("invalid addr");
        while(1);
    }
    memset(&gap_conn_params, 0, sizeof(gap_conn_params));

    gap_conn_params.min_conn_interval = MIN_CONN_INTERVAL;
    gap_conn_params.max_conn_interval = MAX_CONN_INTERVAL;
    gap_conn_params.slave_latency     = SLAVE_LATENCY;
    gap_conn_params.conn_sup_timeout  = CONN_SUP_TIMEOUT;

    err_code = sd_ble_gap_ppcp_set(&gap_conn_params);
    APP_ERROR_CHECK(err_code);
}
/**@brief Function for initializing the Connection Parameters module.
 */
void conn_params_init(ble_Params_updata_Evt_handler_t  bleParamUpdataCallback)
{
    uint32_t               err_code;
    ble_conn_params_init_t cp_init;

    memset(&cp_init, 0, sizeof(cp_init));
    iBleParamsUpdataHandler = bleParamUpdataCallback;
    cp_init.p_conn_params                  = NULL;
    cp_init.first_conn_params_update_delay = FIRST_CONN_PARAMS_UPDATE_DELAY;
    cp_init.next_conn_params_update_delay  = NEXT_CONN_PARAMS_UPDATE_DELAY;
    cp_init.max_conn_params_update_count   = MAX_CONN_PARAMS_UPDATE_COUNT;
    cp_init.start_on_notify_cccd_handle    = BLE_GATT_HANDLE_INVALID;
    cp_init.disconnect_on_fail             = false;
    cp_init.evt_handler                    = on_conn_params_evt;
    cp_init.error_handler                  = conn_params_error_handler;

    err_code = ble_conn_params_init(&cp_init);
    APP_ERROR_CHECK(err_code);
}
void conn_params_updata_start(uint8_t level)
{
    uint32_t               err_code;
    ble_gap_conn_params_t  new_params;
    
    if(level >3u)
    {
        level = 3u;
    }
    new_params = arr_conn_params_cfg[level];
    gConnParamLevel = level;
    err_code = ble_conn_params_change_conn_params(m_conn_handle,&new_params)  ;
    NRF_LOG_INFO("start conn_params_change =0x%x\n",err_code);
}
uint8_t get_conn_params_level(void)
{
    return gConnParamLevel;
}

void set_conn_params_level(uint8_t level)
{
     gConnParamLevel = level;
}
/**@brief Function for starting advertising. */
void advertising_start(void )
{
   ret_code_t err_code = ble_advertising_start(&m_advertising, BLE_ADV_MODE_FAST);
   APP_ERROR_CHECK(err_code);
}

void Ble_Dfu_Bootloader_enter_prepare(void)
{
    NRF_LOG_INFO("Device is preparing to enter disable mode.");

    // Prevent device from advertising on disconnect.
    ble_adv_modes_config_t config;
    advertising_config_get(&config);
    config.ble_adv_on_disconnect_disabled = true;
    ble_advertising_modes_config_set(&m_advertising, &config);

    // Disconnect all other bonded devices that currently are connected.
    // This is required to receive a service changed indication
    // on bootup after a successful (or aborted) Device Firmware Update.
    uint32_t conn_count = ble_conn_state_for_each_connected(disconnect, NULL);
    NRF_LOG_INFO("Disconnected %d links.", conn_count);
}
/***********************************************END**********************************************/
