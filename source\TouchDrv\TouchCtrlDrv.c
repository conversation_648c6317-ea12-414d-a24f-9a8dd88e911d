/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : TouchCtrDrv.c
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef TOUCH_CTRL_DRV_C
#define TOUCH_CTRL_DRV_C
#endif

/* include files */
#include "TouchCtrlDrv.h"

/* static variable definition */

/* static function declaration */

/* static function definition */
static bool xim_touchpad_drv_write_cmd(uint16_t cmd)
{
    uint8_t addr = cmd & 0xFF;
    uint8_t data = (cmd >> 8) & 0xFF;
    
    if(I2C_WriteOneReg(TOUCHPAD_I2C_HANDLE,TOUCHPAD_I2C_ADDR,addr,&data,1u) != I2C_RW_ST_SUCCESS)
    {
        return false;
    }
    return true;
}

static bool xim_touchpad_drv_read_register(uint16_t register_address, uint8_t *value)
{
    if(I2C_ReadTwoReg(TOUCHPAD_I2C_HANDLE,TOUCHPAD_I2C_ADDR,register_address,value,2u) != I2C_RW_ST_SUCCESS)
    {
        return false;
    }

    return true;
}

static bool xim_touchpad_drv_write_register(uint16_t register_address, const uint16_t value)
{
    uint8_t w_data[2];
    w_data[0] = value & 0xFF;
    w_data[1] = (value >> 8) & 0xFF;
    
    if(I2C_WriteTwoReg(TOUCHPAD_I2C_HANDLE,TOUCHPAD_I2C_ADDR,register_address,w_data,2u) != I2C_RW_ST_SUCCESS)
    {
        return false;
    }

    return true;
}
static bool xim_touchpad_drv_power_sequence(void)
{
    uint8_t retry = 0;
    uint16_t chip_code;
    
retry_power_sequence:
    if (!xim_touchpad_drv_write_register(0xc000, 0x0001)) 
    {
        TOUCHPAD_PRINTF("power sequence error (vendor cmd enable)\n");
        goto fail_power_sequence;
    }
    if (!xim_touchpad_drv_read_register(0xcc00, (uint8_t *)&chip_code)) 
    {
        TOUCHPAD_PRINTF("fail to read chip code\n");
        goto fail_power_sequence;
    }
    //TOUCHPAD_PRINTF("chip code(Fireware) = 0x%x\n", chip_code);

    if (!xim_touchpad_drv_write_cmd(0xc004)) 
    {
        TOUCHPAD_PRINTF("power sequence error (intn clear)\n");
        goto fail_power_sequence;
    }
    if (!xim_touchpad_drv_write_register(0xc002, 0x0001)) 
    {
        TOUCHPAD_PRINTF("power sequence error (nvm init)\n");
        goto fail_power_sequence;
    }

    if (!xim_touchpad_drv_write_register(0xc001, 0x0001)) 
    {
        TOUCHPAD_PRINTF("power sequence error (program start)\n");
        goto fail_power_sequence;
    }
    //nrf_delay_ms(FIRMWARE_ON_DELAY);
    return true;

fail_power_sequence:
    if (retry++ < 3) 
    {
        TOUCHPAD_DELAY_MS(CHIP_ON_DELAY);
        TOUCHPAD_PRINTF("retry = %d", retry);
        goto retry_power_sequence;
    }
    return false;

}
static bool xim_touchpad_init_touchmode(void)
{

    if(!xim_touchpad_drv_write_register(ZINITIX_INITIAL_TOUCH_MODE, TOUCH_POINT_MODE))
    {
        return false;
    }
    if(!xim_touchpad_drv_write_register(ZINITIX_TOUCH_MODE, TOUCH_POINT_MODE))
    {
        return false;
    }
    if(!xim_touchpad_drv_write_register(ZINITIX_SUPPORTED_FINGER_NUM, MAX_SUPPORTED_FINGER_NUM))
    {
        return false;
    }
    if(!xim_touchpad_drv_write_register(ZINITIX_INT_ENABLE_FLAG, 0))
    {
        return false;
    }
    if(!xim_touchpad_drv_write_cmd(ZINITIX_CALIBRATE_CMD))
    {
        return false;
    }
    if(!xim_touchpad_drv_write_register(ZINITIX_INT_ENABLE_FLAG, 0x800F))
    {
        return false;
    }
    return true;
}
/* public function definition */
void xim_touchpad_drv_sw_reset(void)
{
    if(!xim_touchpad_drv_write_cmd(ZINITIX_SWRESET_CMD))
    {
        TOUCHPAD_PRINTF("touch swreset cmd send error \n");
    }
    else
    {
        TOUCHPAD_DELAY_MS(DELAY_AFTER_SWRESET_TO_CLEAR);
    }
}

//   org demo delay 330ms no clear cmd
 void xim_touchpad_drv_sleep(void)
{
    // DO not  del the comments code
    TOUCHPAD_PRINTF("xim_touchpad_drv_sleep ");

    xim_touchpad_drv_write_cmd(ZINITIX_CLEAR_INT_STATUS_CMD);
    // more than 55
    TOUCHPAD_DELAY_MS(60);// org demo delay 330ms
    xim_touchpad_drv_write_cmd(ZINITIX_SLEEP_CMD);
    TOUCHPAD_DELAY_MS(10);
}
void xim_touchpad_drv_wakeup(void)
{
    TOUCHPAD_PRINTF("xim_touchpad_drv_wakeup ");
    // DO not  del the comments code
    nrf_delay_ms(10);
    xim_touchpad_drv_write_cmd(ZINITIX_WAKEUP_CMD);
    nrf_delay_ms(10);

    return ;
}

bool xim_touchpad_drv_read_point_info(touchpad_motion_t *pTp_data)
{
    zinitix_point_info_t point_info;

    memset(&point_info, 0, sizeof(point_info));

    if(I2C_ReadTwoReg(TOUCHPAD_I2C_HANDLE,TOUCHPAD_I2C_ADDR,ZINITIX_POINT_STATUS_REG,(uint8_t *)&point_info,sizeof(point_info)) != I2C_RW_ST_SUCCESS)
    {
        return false;
    }
    xim_touchpad_drv_write_cmd(ZINITIX_CLEAR_INT_STATUS_CMD);

    pTp_data->atcive_event = TP_IDLE_EVT;   ///

    if (point_info.status.down_event) 
    {
        pTp_data->atcive_event =  TP_DOWN_EVT;///4
    } 
    else    if (point_info.status.move_event) 
    {
        pTp_data->atcive_event =  TP_MOVE_EVT;//8
        
    } else    if (point_info.status.up_event) 
    {
        pTp_data->atcive_event =  TP_UP_EVT;// C
    }

    pTp_data->x       = (point_info.x) & (0x0FFFFF);
    pTp_data->y       =  (point_info.y) & (0x0FFFFF) ;

    return true;
}

bool xim_touchpad_drv_read_info(uint16_t *pTpFw_ver)
{
    uint8_t product_id[2];
    
    if(!xim_touchpad_drv_read_register(0xcc00, product_id))
    {
        return false;
    }

    //TOUCHPAD_PRINTF("chip code version: 0x%02X 0x%02X", product_id[0], product_id[1]);
    if ((product_id[0] == 0x32) && (product_id[1] == 0xE5)) 
    {
        //do nothing
    }
    else
    {
        return false ;
    }

    if(!xim_touchpad_drv_read_register(ZINITIX_DATA_VERSION_REG, product_id))
    {
        return false;
    }
    TOUCHPAD_PRINTF("download fw version: 0x%02X 0x%02X", product_id[0], product_id[1]);
    
    *pTpFw_ver = (((uint16_t)product_id[1])<<8) + product_id[0];

    if(!xim_touchpad_drv_read_register(ZINITIX_FIRMWARE_VERSION, product_id))
    {
        return false;
    }
    TOUCHPAD_PRINTF("FW ver: 0x%02X 0x%02X", product_id[0], product_id[1]);
    
    if(!xim_touchpad_drv_read_register(ZINITIX_CHIP_REVISION, product_id))
    {
        return false;
    }
    TOUCHPAD_PRINTF("chip ver: 0x%02X 0x%02X", product_id[0], product_id[1]);
    return true;
}
#if SYS_CFG_USE_TP_SWITCH == FUN_EN
void xim_touchpad_drv_pin_reset(void) 
{
    TOUCHPAD_PIN_RESET_LOW();
    TOUCHPAD_DELAY_MS(5);
    TOUCHPAD_IN_RESET_HIGH();
    // about 421us to get ready
    TOUCHPAD_DELAY_MS(5);
}


bool xim_touchpad_drv_init(uint16_t *pTpFw_ver)
{
    xim_touchpad_drv_pin_reset();
    if (!xim_touchpad_drv_power_sequence())
    {
        //  return false;
        TOUCHPAD_PRINTF("TP power up init failure");
        TOUCHPAD_PRINTF("tp init abort!!!!!!!!!!!!!!!!!!!");
        return false;
    }
    //nrf_delay_ms(TP_POWER_SEQUENCE_DELAY);
    TOUCHPAD_DELAY_MS(TP_POWER_SEQUENCE_DELAY);

    // Do a soft reset/ hw reset
   // xim_touchpad_drv_sw_reset();
    if(!xim_touchpad_drv_read_info(pTpFw_ver))
    {
        return false;
    }
    if(!xim_touchpad_init_touchmode())
    {
        return false;
    }

    xim_touchpad_drv_write_cmd(ZINITIX_CLEAR_INT_STATUS_CMD);

    return true;
}
#endif

/***********************************************END**********************************************/

