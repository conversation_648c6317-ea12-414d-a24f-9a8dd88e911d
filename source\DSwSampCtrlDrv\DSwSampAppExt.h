/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : DSwSampAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/03/15    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef D_SW_SAMP_APP_EXT_H
#define D_SW_SAMP_APP_EXT_H

#ifdef  D_SW_SAMP_DRV_C
#define D_SW_SAMP_APP_EXT
#else 
#define D_SW_SAMP_APP_EXT extern
#endif

/*Include files*/
#include "XimStdint.h"
#include "DSwSampCfgExt.h"
/* Type Definitions */

/* macro definition */

/* Switch sample mode */
#define D_SW_SAMP_MODE_OFF                (uint8_t)0u
#define D_SW_SAMP_MODE_ON                 (uint8_t)1u
#define D_SW_SAMP_MODE_DIRECT             (uint8_t)2u
#define D_SW_SAMP_MODE_INV                (uint8_t)3u
#define D_SW_SAMP_MODE_SET_LOW_GOING      (uint8_t)4u
#define D_SW_SAMP_MODE_SET_HIGH_GOING     (uint8_t)5u
#define D_SW_SAMP_MODE_TOGGLE_LOW_GOING   (uint8_t)6u
//�л�����������
#define D_SW_SAMP_MODE_TOGGLE_HIGH_GOING  (uint8_t)7u

#define D_SW_SAMP_MODE_GENERAL_MASK       (uint8_t)0x0Fu

/* these 2 macro can be assigned to each switch 
 * DSS -- D_SW_SAMP */
#define DSS_NOEF_TO_ON_AS_OFF_YES         (uint8_t)0x80u
#define DSS_NOEF_TO_ON_AS_OFF_NO          (uint8_t)0u

#define DSS_MODE_NOEF_TO_ON_PROC_MASK     (uint8_t)0xC0u

#define D_SW_SAMP_HIGH                    (uint8_t)1u       /* Physical high state for switch                     */
#define D_SW_SAMP_LOW                     (uint8_t)0u       /* Physical low state for switch                      */

/* variable definition */

/* Supply all switch logic state in array "gDSwLogicSt[]" to user */

D_SW_SAMP_APP_EXT uint8_t  gDSwLogicSt[D_SW_SAMP_OBJ_NUM];

/* function declaration */
D_SW_SAMP_APP_EXT void DSwSampInit(void);
D_SW_SAMP_APP_EXT void ClearSwSampFilterCnt(void);
D_SW_SAMP_APP_EXT void DSwSamp(void);
D_SW_SAMP_APP_EXT void DSwSampTimesSet(uint8_t aDSwSampTimes);
D_SW_SAMP_APP_EXT void GetTrig_GripValue(uint8_t *apTrigValue, const int16_t aRawTrig);


#endif	/* end of header file */
