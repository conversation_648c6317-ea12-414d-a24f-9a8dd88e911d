#ifndef __IMU_TYPES_H__
#define __IMU_TYPES_H__

#include <stdint.h>

#define  M_PI             ((double)3.1415926535)

typedef struct
{
    float roll;
    float pitch;
    float yaw;
} ypr_t;

typedef struct
{
    float q0;
    float q1;
    float q2;
    float q3;
} quaternion_t;

typedef struct
{
    float min, max;
} m_magnet_scale_fix_source_t;

typedef struct
{
    m_magnet_scale_fix_source_t x, y, z;
} m_magnet_scale_fix_sources_t;

typedef struct{
    float x;
    float y;
    float z;
}dof3_float_t;

typedef enum
{
    magnet_source_xim_max_mean = 0x00,       //Magnet Xim Offset -> Xim Scale
    magnet_source_akm,                      //Magnet Xim Offset -> AKM calib
    magnet_source_xim_ellipse,               //Magnet Xim Offset -> Xim Matrix
    magnet_source_no_magnet = 0x10,
}magnet_source_type_t;

typedef struct{
    uint32_t times;
    struct{
        dof3_float_t offset;
        dof3_float_t scale;
    }accel;
    struct{
        dof3_float_t offset;
        dof3_float_t scale;
    }gyro;
    struct{
        magnet_source_type_t mag_calib_type;
        dof3_float_t offset;
        dof3_float_t scale;
    }magnet;
}dof9_calib_t;

typedef struct
{
    float v00;
    float v01;
    float v02;
    
    float v10;
    float v11;
    float v12;
    
    float v20;
    float v21;
    float v22;
}matrix_33_t;


/**@brief Structure to hold acceleromter values.
 * Sequence of z, y, and x is important to correspond with
 * the sequence of which z, y, and x data are read from the sensor.
 * All values are unsigned 16 bit integers
*/
typedef struct
{
    int16_t x;
    int16_t y;
    int16_t z;
}dof9_element_t;

typedef struct
{
    uint64_t timestamp;
    dof9_element_t accel;
    dof9_element_t gyro;
    dof9_element_t magnet;
} m9_sensor_integer_t;

typedef struct{
    dof3_float_t accel;
    dof3_float_t gyro;
    dof3_float_t magnet;
    uint64_t timestamp;
}f9_sensor_float_t;

typedef void (*pfIMU_ypr_callback)(ypr_t* p_ytr);
typedef void (*pfIMU_q4_callback)(quaternion_t* p_q4);
typedef void (*pfIMU_motions_9_callback)(m9_sensor_integer_t* p_m9);
typedef void (*pfIMU_f9_callback)(f9_sensor_float_t* p_f9);


typedef enum{
    imu_event_enter_active = 0,
    imu_event_enter_idle,
    imu_event_enter_powerdown,
    imu_event_enter_hardware_error,
}imu_interface_event_t;
typedef void (*pfimu_event_handle_t)(imu_interface_event_t event);

#endif
