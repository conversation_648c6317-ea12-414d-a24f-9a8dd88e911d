/*  BEGIN_FILE_HDR
************************************************************************************************
*   NOTICE                              
*   This software is the property of Ximmerse Technologies. Any information contained in this 
*   doc should not be reproduced, or used, or disclosed without the written authorization from 
*   Ximmerse Technologies.
************************************************************************************************
*   File Name       : PowerCtrlAppExt.h
************************************************************************************************
*   Project/Product : 
*   Title           : 
*   Author          : 
************************************************************************************************
*   Description     : 
*
************************************************************************************************
*   Limitations     : 
*
************************************************************************************************
*
************************************************************************************************
*   Revision History:
* 
*    Version    Date          Initials      CR#             Descriptions
*   ---------   ----------    ------------  ----------    ---------------
*   1.0         2016/06/07    guotai        N/A            Original                
*  
************************************************************************************************
* END_FILE_HDR*/

#ifndef PWR_CTRL_APP_EXT_H
#define PWR_CTRL_APP_EXT_H

/*Include files*/
#include "includes.h"
#include "XimStdint.h"

/*declaration range definition*/
#ifdef PWR_CTRL_DRV_C 
  #define PWR_CTRL_APP_EXT 
#else 
  #define PWR_CTRL_APP_EXT extern 
#endif

/***********************************************************************************************
* ALL definition and declaration can be used ,but can not revised outside this driver          *
************************************************************************************************/
/* macro definition */
#define PWR_OPERATE_SUCCESS     (0x00u)
#define PWR_OPERATE_ERROR       (0xFFu)
  
#define PWR_OPWRATE_CHARGING     (0x00)
#define PWR_OPWRATE_CHG_DONE     (0x01)
#define PWR_OPWRATE_CHG_ERR      (0x02)

#define PWR_CTRL_EOL_THRESHOLDE  (10) //%5
#define PWR_CTRL_OFF_THRESHOLDE  (3) //%5
  
#define PWR_CTRL_ENTER_SLEEP_FLG            (0x02)
#define PWR_CTRL_ENTER_USB_POWER_OFF_FLG    (0x04)
/* data type definiton  */


/* variable definition */

/* function declaration */
PWR_CTRL_APP_EXT    void PwrHoldCtrl(uint8_t aEnable);
PWR_CTRL_APP_EXT    void ImuPwrCtrl(uint8_t aEnable);
PWR_CTRL_APP_EXT    void TpPwrCtrl(uint8_t aEnable);
PWR_CTRL_APP_EXT    void HallPwrCtrl(uint8_t aEnable);
PWR_CTRL_APP_EXT    void PvElecMagnetPwrCtrl(uint8_t aEnable);
PWR_CTRL_APP_EXT    void AINKeyPwrCtrl(uint8_t aEnable);
PWR_CTRL_APP_EXT    void BlobPwmPwrCtrl(uint8_t aEnable,uint16_t preiod,uint16_t duty_R,uint16_t duty_G,uint16_t duty_B);
PWR_CTRL_APP_EXT    void ElectroMagnetSwitchCtrl(uint8_t aEnable, uint32_t timeout_ms);
PWR_CTRL_APP_EXT    void MotorPwmPwrCtrl(uint8_t aEnable,uint16_t preiod,uint16_t duty_m,uint32_t timeout_ms);
PWR_CTRL_APP_EXT    void BatCharge(uint8_t aEnable);
PWR_CTRL_APP_EXT    uint32_t BatVolSample_filter(void);
PWR_CTRL_APP_EXT    uint8_t GetBatElectric(uint8_t isLiBat,uint32_t volt_mv);
PWR_CTRL_APP_EXT    uint8_t getChargeStatus(void);
PWR_CTRL_APP_EXT    void sleep_mode_enter(void);
PWR_CTRL_APP_EXT    void PowerOff_mode_enter(void);
#endif   /* end of header file */
